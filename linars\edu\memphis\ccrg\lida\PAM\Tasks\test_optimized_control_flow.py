"""
优化后的控制流任务测试文件
"""

import asyncio
import logging
import time
from unittest.mock import Mock, MagicMock

from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory

from .OptimizedDoSuccTask import OptimizedDoSuccTask
from .OptimizedDoSelectTreeTask import OptimizedDoSelectTreeTask
from .OptimizedForEachTask import OptimizedForEachTask, LoopType

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_mock_node(name: str, node_id: int = None, activation: float = 0.5) -> Node:
    """创建模拟节点"""
    node = Mock(spec=Node)
    node.get_tn_name.return_value = name
    node.get_node_id.return_value = node_id or hash(name)
    node.get_activation.return_value = activation
    node.set_incentive_salience = Mock()
    node.get_incentive_salience.return_value = 0.8
    return node


def create_mock_link(source: Node, sink: Node) -> Link:
    """创建模拟链接"""
    link = Mock(spec=Link)
    link.get_source.return_value = source
    link.get_sink.return_value = sink
    link.get_activation.return_value = 0.7
    link.get_node_id.return_value = hash(f"{source.get_tn_name()}->{sink.get_tn_name()}")
    return link


def create_mock_pam() -> PAMemory:
    """创建模拟PAM"""
    pam = Mock(spec=PAMemory)
    
    # 模拟工作空间缓冲区
    seq_buffer = Mock()
    goal_buffer = Mock()
    
    seq_ns = Mock(spec=NodeStructure)
    goal_ns = Mock(spec=NodeStructure)
    
    seq_ns.get_do_main_path_map.return_value = {"test_stamp": []}
    
    seq_buffer.get_buffer_content.return_value = seq_ns
    goal_buffer.get_buffer_content.return_value = goal_ns
    
    def get_workspace_buffer(name):
        if name == "seq":
            return seq_buffer
        elif name == "goal":
            return goal_buffer
        else:
            return Mock()
    
    pam.get_workspace_buffer = get_workspace_buffer
    
    # 模拟监听器
    listener = Mock()
    listener.receive_percept = Mock()
    pam.get_listener.return_value = listener
    
    # 模拟act_root方法
    pam.get_act_root = Mock()
    
    return pam


async def test_optimized_do_succ_task():
    """测试优化后的顺序执行任务"""
    print("\n=== 测试优化后的顺序执行任务 ===")
    
    # 创建模拟对象
    source_node = create_mock_node("source_scene", 1)
    sink_node = create_mock_node("sink_scene", 2)
    pam = create_mock_pam()
    
    # 创建任务
    task = OptimizedDoSuccTask(
        sink=sink_node,
        source=source_node,
        pam=pam,
        act_stamp="test_stamp",
        execution_mode="sequential"
    )
    
    # 检查初始化状态
    print(f"任务状态: {task.status}")
    print(f"执行步骤数: {len(task.execution_steps)}")
    print(f"执行模式: {task.execution_mode}")
    
    # 运行任务（模拟框架调用）
    try:
        task.run_this_framework_task()
        
        # 等待异步执行完成
        await asyncio.sleep(0.1)
        
        # 检查执行结果
        stats = task.get_execution_stats()
        progress = task.get_execution_progress()
        
        print(f"执行统计: {stats}")
        print(f"执行进度: {progress}")
        
        return task.status.value in ['completed', 'running']
        
    except Exception as e:
        logger.error(f"DoSuccTask test failed: {e}")
        return False


async def test_optimized_do_select_tree_task():
    """测试优化后的条件选择任务"""
    print("\n=== 测试优化后的条件选择任务 ===")
    
    # 创建模拟对象
    source_node = create_mock_node("condition_source", 3)
    sink_node = create_mock_node("condition_sink", 4)
    link = create_mock_link(source_node, sink_node)
    
    pam = create_mock_pam()
    goal_ns = Mock(spec=NodeStructure)
    scene_ns = Mock(spec=NodeStructure)
    
    # 创建任务
    task = OptimizedDoSelectTreeTask(
        link=link,
        pam=pam,
        goal_ns=goal_ns,
        scene_ns=scene_ns,
        act_stamp="test_condition_stamp"
    )
    
    # 检查初始化状态
    print(f"任务状态: {task.status}")
    print(f"条件分支数: {len(task.condition_branches)}")
    print(f"else分支数: {len(task.else_branches)}")
    
    # 运行任务
    try:
        task.run_this_framework_task()
        
        # 等待异步执行完成
        await asyncio.sleep(0.1)
        
        # 检查执行结果
        stats = task.get_execution_stats()
        condition_stats = task.get_condition_stats()
        
        print(f"执行统计: {stats}")
        print(f"条件统计: {condition_stats}")
        
        return task.status.value in ['completed', 'running']
        
    except Exception as e:
        logger.error(f"DoSelectTreeTask test failed: {e}")
        return False


async def test_optimized_for_each_task():
    """测试优化后的循环任务"""
    print("\n=== 测试优化后的循环任务 ===")
    
    # 创建模拟对象
    source_node = create_mock_node("loop_source", 5)
    sink_node = create_mock_node("loop_sink", 6)
    link = create_mock_link(source_node, sink_node)
    
    pam = create_mock_pam()
    seq_ns = Mock(spec=NodeStructure)
    scene_ns = Mock(spec=NodeStructure)
    
    # 测试不同类型的循环
    loop_types = [LoopType.DO_WHILE, LoopType.WHILE, LoopType.FOR, LoopType.FOREACH]
    results = []
    
    for loop_type in loop_types:
        print(f"\n--- 测试 {loop_type} 循环 ---")
        
        # 创建任务
        task = OptimizedForEachTask(
            link=link,
            pam=pam,
            seq_ns=seq_ns,
            scene_ns=scene_ns,
            act_stamp=f"test_{loop_type}_stamp",
            loop_type=loop_type,
            max_iterations=5  # 限制迭代次数以便测试
        )
        
        # 为不同循环类型设置特定变量
        if loop_type == LoopType.FOR:
            task.loop_variables.update({
                'start': 0,
                'end': 3,
                'step': 1,
                'current': 0
            })
        elif loop_type == LoopType.FOREACH:
            task.loop_variables.update({
                'items': ['item1', 'item2', 'item3'],
                'current_index': 0
            })
        
        # 检查初始化状态
        print(f"任务状态: {task.status}")
        print(f"循环类型: {task.loop_type}")
        print(f"循环变量: {task.loop_variables}")
        
        # 运行任务
        try:
            task.run_this_framework_task()
            
            # 等待异步执行完成
            await asyncio.sleep(0.2)
            
            # 检查执行结果
            stats = task.get_execution_stats()
            loop_stats = task.get_loop_stats()
            
            print(f"执行统计: {stats}")
            print(f"循环统计: {loop_stats}")
            
            success = task.status.value in ['completed', 'running']
            results.append(success)
            
        except Exception as e:
            logger.error(f"{loop_type} loop test failed: {e}")
            results.append(False)
    
    return all(results)


async def test_task_error_handling():
    """测试任务错误处理"""
    print("\n=== 测试任务错误处理 ===")
    
    # 创建会导致错误的模拟对象
    source_node = create_mock_node("error_source", 7)
    sink_node = create_mock_node("error_sink", 8)
    
    # 创建会抛出异常的PAM
    pam = Mock(spec=PAMemory)
    pam.get_workspace_buffer.side_effect = Exception("Simulated PAM error")
    
    # 创建任务
    task = OptimizedDoSuccTask(
        sink=sink_node,
        source=source_node,
        pam=pam,
        act_stamp="error_test_stamp",
        enable_retry=True,
        max_retries=2
    )
    
    print(f"初始任务状态: {task.status}")
    
    # 运行任务
    try:
        task.run_this_framework_task()
        
        # 等待异步执行完成
        await asyncio.sleep(0.2)
        
        # 检查错误处理结果
        stats = task.get_execution_stats()
        
        print(f"最终任务状态: {task.status}")
        print(f"错误信息: {task.error_info}")
        print(f"重试次数: {task.retry_count}")
        print(f"执行统计: {stats}")
        
        # 错误处理测试成功的标准是任务状态为失败但有重试记录
        return task.status.value == 'failed' and task.retry_count > 0
        
    except Exception as e:
        logger.error(f"Error handling test failed: {e}")
        return False


async def test_task_performance():
    """测试任务性能"""
    print("\n=== 测试任务性能 ===")
    
    # 创建多个任务并发执行
    tasks = []
    start_time = time.time()
    
    for i in range(10):
        source_node = create_mock_node(f"perf_source_{i}", 100 + i)
        sink_node = create_mock_node(f"perf_sink_{i}", 200 + i)
        pam = create_mock_pam()
        
        task = OptimizedDoSuccTask(
            sink=sink_node,
            source=source_node,
            pam=pam,
            act_stamp=f"perf_test_stamp_{i}",
            execution_mode="sequential"
        )
        
        tasks.append(task)
    
    # 并发运行所有任务
    async def run_task(task):
        task.run_this_framework_task()
        await asyncio.sleep(0.05)  # 模拟执行时间
        return task.get_execution_stats()
    
    results = await asyncio.gather(*[run_task(task) for task in tasks], return_exceptions=True)
    
    execution_time = time.time() - start_time
    
    # 统计结果
    successful_tasks = sum(1 for result in results if not isinstance(result, Exception))
    failed_tasks = len(results) - successful_tasks
    
    print(f"总执行时间: {execution_time:.4f}s")
    print(f"成功任务数: {successful_tasks}")
    print(f"失败任务数: {failed_tasks}")
    print(f"平均每任务时间: {execution_time / len(tasks):.4f}s")
    
    return successful_tasks >= len(tasks) * 0.8  # 80%成功率


async def test_task_checkpoints():
    """测试任务检查点功能"""
    print("\n=== 测试任务检查点功能 ===")
    
    # 创建模拟对象
    source_node = create_mock_node("checkpoint_source", 9)
    sink_node = create_mock_node("checkpoint_sink", 10)
    pam = create_mock_pam()
    
    # 创建启用检查点的任务
    task = OptimizedDoSuccTask(
        sink=sink_node,
        source=source_node,
        pam=pam,
        act_stamp="checkpoint_test_stamp",
        enable_checkpoints=True
    )
    
    # 手动创建一些检查点
    task.execution_context.create_checkpoint("test_checkpoint_1")
    task.execution_context.set_variable("test_var", "test_value")
    task.execution_context.create_checkpoint("test_checkpoint_2")
    task.execution_context.set_variable("test_var", "modified_value")
    
    print(f"检查点数量: {len(task.execution_context.checkpoints)}")
    print(f"当前变量值: {task.execution_context.get_variable('test_var')}")
    
    # 恢复到第一个检查点
    restored = task.execution_context.restore_checkpoint("test_checkpoint_1")
    
    print(f"检查点恢复成功: {restored}")
    print(f"恢复后变量值: {task.execution_context.get_variable('test_var')}")
    
    return restored and task.execution_context.get_variable('test_var') == "test_value"


async def main():
    """主测试函数"""
    print("开始优化后的控制流任务测试...")
    
    tests = [
        ("顺序执行任务", test_optimized_do_succ_task),
        ("条件选择任务", test_optimized_do_select_tree_task),
        ("循环任务", test_optimized_for_each_task),
        ("错误处理", test_task_error_handling),
        ("性能测试", test_task_performance),
        ("检查点功能", test_task_checkpoints)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*50}")
            print(f"运行测试: {test_name}")
            print(f"{'='*50}")
            
            start_time = time.time()
            result = await test_func()
            execution_time = time.time() - start_time
            
            results.append(result)
            status = "✓ 通过" if result else "✗ 失败"
            print(f"\n{test_name}: {status} (耗时: {execution_time:.4f}s)")
            
        except Exception as e:
            logger.error(f"测试 {test_name} 出现异常: {e}")
            results.append(False)
            print(f"\n{test_name}: ✗ 异常 (错误: {str(e)})")
    
    # 总结
    successful_tests = sum(results)
    total_tests = len(results)
    success_rate = successful_tests / total_tests * 100
    
    print(f"\n{'='*50}")
    print(f"测试总结")
    print(f"{'='*50}")
    print(f"成功测试: {successful_tests}/{total_tests}")
    print(f"成功率: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("🎉 测试整体通过！")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    return successful_tests == total_tests


if __name__ == "__main__":
    asyncio.run(main())
