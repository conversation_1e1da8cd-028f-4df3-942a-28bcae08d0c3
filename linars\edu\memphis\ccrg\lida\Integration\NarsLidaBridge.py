"""
NARS-LIDA集成桥接
建立NARS推理系统与LIDA认知架构的统一桥接，实现三段论推理与图式搜索的双向转换
"""

import logging
import threading
from typing import Dict, List, Set, Optional, Any, Tuple, Union
from collections import defaultdict
import time

from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName

# 检查NARS相关类的可用性
try:
    from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm
    from linars.edu.memphis.ccrg.linars.term import Term
    from linars.org.opennars.language.statement import Statement
    from linars.org.opennars.language.variable import Variable
    from linars.org.opennars.entity.task import Task
    NARS_AVAILABLE = True
except ImportError:
    NARS_AVAILABLE = False


class ReasoningContext:
    """推理上下文"""
    
    def __init__(self, context_id: str):
        self.context_id = context_id
        self.premises = []
        self.conclusions = []
        self.inference_rules = []
        self.confidence_threshold = 0.5
        self.created_time = time.time()
        self.last_updated = time.time()


class InferenceRule:
    """推理规则"""
    
    def __init__(self, rule_name: str, rule_type: str, confidence: float = 0.8):
        self.rule_name = rule_name
        self.rule_type = rule_type  # syllogism, induction, deduction, abduction
        self.confidence = confidence
        self.usage_count = 0
        self.success_rate = 0.0


class NarsLidaBridge:
    """
    NARS-LIDA集成桥接器
    实现NARS推理系统与LIDA认知架构的统一桥接
    """

    def __init__(self, nar=None, pam=None):
        """
        初始化桥接器
        
        Args:
            nar: NARS推理器实例
            pam: PAM实例
        """
        self.nar = nar
        self.pam = pam
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 推理上下文管理
        self.reasoning_contexts = {}
        self.context_lock = threading.RLock()
        
        # 推理规则库
        self.inference_rules = self.initialize_inference_rules()
        
        # 转换缓存
        self.conversion_cache = {}
        self.cache_lock = threading.RLock()
        
        # 集成参数
        self.integration_params = {
            'max_reasoning_depth': 5,
            'confidence_threshold': 0.6,
            'max_premises': 10,
            'reasoning_timeout': 10.0,
            'cache_size_limit': 1000
        }
        
        # 统计信息
        self.stats = {
            'nars_to_lida_conversions': 0,
            'lida_to_nars_conversions': 0,
            'successful_inferences': 0,
            'failed_inferences': 0,
            'cache_hits': 0,
            'reasoning_contexts_created': 0
        }

    def initialize_inference_rules(self):
        """
        初始化推理规则库
        
        Returns:
            dict: 推理规则字典
        """
        rules = {}
        
        # 三段论推理规则
        rules['syllogism'] = [
            InferenceRule('modus_ponens', 'syllogism', 0.9),
            InferenceRule('modus_tollens', 'syllogism', 0.85),
            InferenceRule('hypothetical_syllogism', 'syllogism', 0.8),
            InferenceRule('disjunctive_syllogism', 'syllogism', 0.8)
        ]
        
        # 归纳推理规则
        rules['induction'] = [
            InferenceRule('generalization', 'induction', 0.7),
            InferenceRule('statistical_induction', 'induction', 0.75),
            InferenceRule('analogical_reasoning', 'induction', 0.6)
        ]
        
        # 演绎推理规则
        rules['deduction'] = [
            InferenceRule('universal_instantiation', 'deduction', 0.9),
            InferenceRule('existential_generalization', 'deduction', 0.85),
            InferenceRule('conditional_proof', 'deduction', 0.8)
        ]
        
        # 溯因推理规则
        rules['abduction'] = [
            InferenceRule('best_explanation', 'abduction', 0.6),
            InferenceRule('causal_inference', 'abduction', 0.65),
            InferenceRule('diagnostic_reasoning', 'abduction', 0.7)
        ]
        
        return rules

    def create_reasoning_context(self, context_id: str = None):
        """
        创建推理上下文
        
        Args:
            context_id: 上下文ID
            
        Returns:
            ReasoningContext: 推理上下文
        """
        try:
            if context_id is None:
                context_id = f"context_{int(time.time() * 1000)}"
            
            with self.context_lock:
                context = ReasoningContext(context_id)
                self.reasoning_contexts[context_id] = context
                self.stats['reasoning_contexts_created'] += 1
                
            self.logger.debug(f"Created reasoning context: {context_id}")
            return context
            
        except Exception as e:
            self.logger.error(f"Error creating reasoning context: {e}")
            return None

    def nars_to_lida_conversion(self, nars_term, context_id: str = None):
        """
        NARS术语到LIDA节点的转换
        
        Args:
            nars_term: NARS术语
            context_id: 推理上下文ID
            
        Returns:
            Node: LIDA节点
        """
        try:
            if not NARS_AVAILABLE:
                self.logger.warning("NARS not available for conversion")
                return None
            
            # 检查缓存
            cache_key = f"nars_to_lida_{str(nars_term)}"
            with self.cache_lock:
                if cache_key in self.conversion_cache:
                    self.stats['cache_hits'] += 1
                    return self.conversion_cache[cache_key]
            
            # 执行转换
            lida_node = self.perform_nars_to_lida_conversion(nars_term)
            
            # 更新缓存
            with self.cache_lock:
                if len(self.conversion_cache) < self.integration_params['cache_size_limit']:
                    self.conversion_cache[cache_key] = lida_node
            
            # 更新统计
            self.stats['nars_to_lida_conversions'] += 1
            
            # 添加到推理上下文
            if context_id and context_id in self.reasoning_contexts:
                context = self.reasoning_contexts[context_id]
                context.premises.append(lida_node)
                context.last_updated = time.time()
            
            return lida_node
            
        except Exception as e:
            self.logger.error(f"Error in NARS to LIDA conversion: {e}")
            return None

    def lida_to_nars_conversion(self, lida_node, context_id: str = None):
        """
        LIDA节点到NARS术语的转换
        
        Args:
            lida_node: LIDA节点
            context_id: 推理上下文ID
            
        Returns:
            Term: NARS术语
        """
        try:
            if not NARS_AVAILABLE:
                self.logger.warning("NARS not available for conversion")
                return None
            
            # 检查缓存
            cache_key = f"lida_to_nars_{self.get_node_id(lida_node)}"
            with self.cache_lock:
                if cache_key in self.conversion_cache:
                    self.stats['cache_hits'] += 1
                    return self.conversion_cache[cache_key]
            
            # 执行转换
            nars_term = self.perform_lida_to_nars_conversion(lida_node)
            
            # 更新缓存
            with self.cache_lock:
                if len(self.conversion_cache) < self.integration_params['cache_size_limit']:
                    self.conversion_cache[cache_key] = nars_term
            
            # 更新统计
            self.stats['lida_to_nars_conversions'] += 1
            
            # 添加到推理上下文
            if context_id and context_id in self.reasoning_contexts:
                context = self.reasoning_contexts[context_id]
                context.premises.append(nars_term)
                context.last_updated = time.time()
            
            return nars_term
            
        except Exception as e:
            self.logger.error(f"Error in LIDA to NARS conversion: {e}")
            return None

    def perform_nars_to_lida_conversion(self, nars_term):
        """
        执行NARS到LIDA的转换
        
        Args:
            nars_term: NARS术语
            
        Returns:
            Node: LIDA节点
        """
        try:
            from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeImpl import NodeImpl
            
            # 获取术语名称
            term_name = str(nars_term)
            if hasattr(nars_term, 'name'):
                term_name = nars_term.name()
            elif hasattr(nars_term, 'getName'):
                term_name = nars_term.getName()
            
            # 创建LIDA节点
            lida_node = NodeImpl()
            lida_node.set_node_name(term_name)
            
            # 设置节点属性
            if hasattr(nars_term, 'getTruth'):
                truth = nars_term.getTruth()
                if truth:
                    lida_node.set_property('confidence', truth.getConfidence())
                    lida_node.set_property('frequency', truth.getFrequency())
            
            # 处理复合术语
            if isinstance(nars_term, CompoundTerm):
                lida_node.set_property('type', 'compound')
                lida_node.set_property('complexity', len(nars_term.term) if hasattr(nars_term, 'term') else 1)
            else:
                lida_node.set_property('type', 'atomic')
                lida_node.set_property('complexity', 1)
            
            return lida_node
            
        except Exception as e:
            self.logger.error(f"Error performing NARS to LIDA conversion: {e}")
            return None

    def perform_lida_to_nars_conversion(self, lida_node):
        """
        执行LIDA到NARS的转换
        
        Args:
            lida_node: LIDA节点
            
        Returns:
            Term: NARS术语
        """
        try:
            if not NARS_AVAILABLE:
                return None
            
            # 获取节点名称
            node_name = self.get_node_name(lida_node)
            if not node_name:
                return None
            
            # 使用Narsese解析器创建术语
            if self.nar and hasattr(self.nar, 'narsese'):
                narsese = self.nar.narsese
                if hasattr(narsese, 'parse_term'):
                    return narsese.parse_term(node_name)
            
            # 后备方案：创建简单术语
            from linars.edu.memphis.ccrg.linars.atomic_term import AtomicTerm
            return AtomicTerm(node_name)
            
        except Exception as e:
            self.logger.error(f"Error performing LIDA to NARS conversion: {e}")
            return None

    def perform_syllogistic_reasoning(self, premises, context_id: str = None):
        """
        执行三段论推理

        Args:
            premises: 前提列表
            context_id: 推理上下文ID

        Returns:
            list: 推理结论列表
        """
        try:
            if not NARS_AVAILABLE or not self.nar:
                self.logger.warning("NARS not available for syllogistic reasoning")
                return []

            conclusions = []
            context = None

            if context_id:
                context = self.reasoning_contexts.get(context_id)
                if not context:
                    context = self.create_reasoning_context(context_id)

            # 应用三段论推理规则
            for rule in self.inference_rules.get('syllogism', []):
                try:
                    rule_conclusions = self.apply_syllogistic_rule(rule, premises)
                    conclusions.extend(rule_conclusions)
                    rule.usage_count += 1

                    if context:
                        context.inference_rules.append(rule)

                except Exception as e:
                    self.logger.error(f"Error applying syllogistic rule {rule.rule_name}: {e}")

            # 过滤和排序结论
            filtered_conclusions = self.filter_conclusions(conclusions)

            # 更新统计
            if filtered_conclusions:
                self.stats['successful_inferences'] += 1
            else:
                self.stats['failed_inferences'] += 1

            return filtered_conclusions

        except Exception as e:
            self.logger.error(f"Error in syllogistic reasoning: {e}")
            self.stats['failed_inferences'] += 1
            return []

    def apply_syllogistic_rule(self, rule, premises):
        """
        应用三段论推理规则

        Args:
            rule: 推理规则
            premises: 前提列表

        Returns:
            list: 推理结论
        """
        try:
            conclusions = []

            if rule.rule_name == 'modus_ponens':
                # 肯定前件式：如果P则Q，P，因此Q
                conclusions = self.apply_modus_ponens(premises)
            elif rule.rule_name == 'modus_tollens':
                # 否定后件式：如果P则Q，非Q，因此非P
                conclusions = self.apply_modus_tollens(premises)
            elif rule.rule_name == 'hypothetical_syllogism':
                # 假言三段论：如果P则Q，如果Q则R，因此如果P则R
                conclusions = self.apply_hypothetical_syllogism(premises)
            elif rule.rule_name == 'disjunctive_syllogism':
                # 析取三段论：P或Q，非P，因此Q
                conclusions = self.apply_disjunctive_syllogism(premises)

            # 为结论设置置信度
            for conclusion in conclusions:
                if hasattr(conclusion, 'set_confidence'):
                    conclusion.set_confidence(rule.confidence)

            return conclusions

        except Exception as e:
            self.logger.error(f"Error applying rule {rule.rule_name}: {e}")
            return []

    def apply_modus_ponens(self, premises):
        """
        应用肯定前件式推理

        Args:
            premises: 前提列表

        Returns:
            list: 推理结论
        """
        try:
            conclusions = []

            # 寻找形如"如果P则Q"和"P"的前提
            implications = []
            facts = []

            for premise in premises:
                if self.is_implication(premise):
                    implications.append(premise)
                else:
                    facts.append(premise)

            # 应用肯定前件式
            for implication in implications:
                antecedent, consequent = self.extract_implication_parts(implication)

                for fact in facts:
                    if self.terms_match(antecedent, fact):
                        # 推出结论
                        conclusion = self.create_conclusion(consequent, 'modus_ponens')
                        conclusions.append(conclusion)

            return conclusions

        except Exception as e:
            self.logger.error(f"Error in modus ponens: {e}")
            return []

    def apply_modus_tollens(self, premises):
        """
        应用否定后件式推理

        Args:
            premises: 前提列表

        Returns:
            list: 推理结论
        """
        try:
            conclusions = []

            # 寻找形如"如果P则Q"和"非Q"的前提
            implications = []
            negations = []

            for premise in premises:
                if self.is_implication(premise):
                    implications.append(premise)
                elif self.is_negation(premise):
                    negations.append(premise)

            # 应用否定后件式
            for implication in implications:
                antecedent, consequent = self.extract_implication_parts(implication)

                for negation in negations:
                    negated_term = self.extract_negated_term(negation)

                    if self.terms_match(consequent, negated_term):
                        # 推出否定的前件
                        negated_antecedent = self.create_negation(antecedent)
                        conclusion = self.create_conclusion(negated_antecedent, 'modus_tollens')
                        conclusions.append(conclusion)

            return conclusions

        except Exception as e:
            self.logger.error(f"Error in modus tollens: {e}")
            return []

    def perform_graph_schema_search(self, query_terms, context_id: str = None):
        """
        执行图式搜索

        Args:
            query_terms: 查询术语
            context_id: 推理上下文ID

        Returns:
            list: 搜索结果
        """
        try:
            search_results = []

            # 转换查询术语为LIDA节点
            lida_nodes = []
            for term in query_terms:
                lida_node = self.nars_to_lida_conversion(term, context_id)
                if lida_node:
                    lida_nodes.append(lida_node)

            # 使用增强的搜索机制
            if self.pam and hasattr(self.pam, 'task_spawner'):
                try:
                    from linars.edu.memphis.ccrg.lida.PAM.Tasks.SearchSB import SearchSB
                    search_task = SearchSB(lida_nodes, 'reasoning_guided', self.pam)

                    # 同步执行搜索（简化版本）
                    search_task.run_this_framework_task()

                    # 获取搜索结果（这里需要实际的结果获取机制）
                    # search_results = search_task.get_results()

                except ImportError:
                    self.logger.warning("SearchSB not available for graph schema search")

            # 使用Neo4j直接搜索作为后备
            if not search_results:
                search_results = self.neo4j_graph_search(lida_nodes)

            return search_results

        except Exception as e:
            self.logger.error(f"Error in graph schema search: {e}")
            return []

    def neo4j_graph_search(self, nodes):
        """
        使用Neo4j进行图搜索

        Args:
            nodes: 节点列表

        Returns:
            list: 搜索结果
        """
        try:
            results = []

            if not nodes:
                return results

            # 构建Cypher查询
            node_names = [self.get_node_name(node) for node in nodes if self.get_node_name(node)]

            if not node_names:
                return results

            # 使用Neo4j搜索相关节点
            try:
                from linars.edu.memphis.ccrg.lida.Data.neo_util import NeoUtil

                # 构建查询语句
                if len(node_names) == 1:
                    query = f"MATCH (n{{name:'{node_names[0]}'}})-[r]-(m) RETURN n, r, m LIMIT 20"
                else:
                    # 多节点查询
                    name_conditions = " OR ".join([f"n.name = '{name}'" for name in node_names])
                    query = f"MATCH (n)-[r]-(m) WHERE {name_conditions} RETURN n, r, m LIMIT 50"

                result = NeoUtil.execute_query(query)

                if result:
                    while hasattr(result, "has_next") and result.has_next():
                        row = result.next()

                        # 提取节点和关系
                        for key in result.columns():
                            value = row.get(key)
                            if value:
                                if hasattr(NeoUtil, 'cast_neo_to_lida_node'):
                                    lida_item = NeoUtil.cast_neo_to_lida_node(value)
                                    if lida_item:
                                        results.append(lida_item)
                                elif hasattr(NeoUtil, 'cast_neo_to_lida_link'):
                                    lida_item = NeoUtil.cast_neo_to_lida_link(value)
                                    if lida_item:
                                        results.append(lida_item)

            except Exception as e:
                self.logger.warning(f"Neo4j search failed: {e}")

            return results

        except Exception as e:
            self.logger.error(f"Error in Neo4j graph search: {e}")
            return []

    def bidirectional_reasoning_search(self, start_terms, goal_terms, context_id: str = None):
        """
        双向推理搜索：结合推理和搜索

        Args:
            start_terms: 起始术语
            goal_terms: 目标术语
            context_id: 推理上下文ID

        Returns:
            dict: 包含推理路径和搜索结果的字典
        """
        try:
            result = {
                'reasoning_paths': [],
                'search_results': [],
                'integrated_conclusions': []
            }

            # 前向推理：从起始术语推理
            forward_conclusions = self.perform_syllogistic_reasoning(start_terms, context_id)

            # 后向搜索：从目标术语搜索
            backward_results = self.perform_graph_schema_search(goal_terms, context_id)

            # 寻找推理和搜索的交集
            intersections = self.find_reasoning_search_intersections(
                forward_conclusions, backward_results
            )

            # 构建完整的推理路径
            for intersection in intersections:
                path = self.build_reasoning_path(start_terms, intersection, goal_terms)
                if path:
                    result['reasoning_paths'].append(path)

            result['search_results'] = backward_results
            result['integrated_conclusions'] = intersections

            return result

        except Exception as e:
            self.logger.error(f"Error in bidirectional reasoning search: {e}")
            return {'reasoning_paths': [], 'search_results': [], 'integrated_conclusions': []}

    def find_reasoning_search_intersections(self, reasoning_results, search_results):
        """
        寻找推理结果和搜索结果的交集

        Args:
            reasoning_results: 推理结果
            search_results: 搜索结果

        Returns:
            list: 交集结果
        """
        try:
            intersections = []

            for reasoning_result in reasoning_results:
                for search_result in search_results:
                    if self.results_intersect(reasoning_result, search_result):
                        intersections.append({
                            'reasoning_result': reasoning_result,
                            'search_result': search_result,
                            'intersection_type': self.get_intersection_type(reasoning_result, search_result)
                        })

            return intersections

        except Exception as e:
            self.logger.error(f"Error finding intersections: {e}")
            return []

    def results_intersect(self, reasoning_result, search_result):
        """
        检查推理结果和搜索结果是否相交

        Args:
            reasoning_result: 推理结果
            search_result: 搜索结果

        Returns:
            bool: 是否相交
        """
        try:
            # 简单的名称匹配
            reasoning_name = self.get_result_name(reasoning_result)
            search_name = self.get_result_name(search_result)

            if reasoning_name and search_name:
                return reasoning_name == search_name

            return False

        except Exception as e:
            self.logger.error(f"Error checking intersection: {e}")
            return False

    def integrate_with_lida_workspace(self, reasoning_results, search_results):
        """
        将推理和搜索结果集成到LIDA工作空间

        Args:
            reasoning_results: 推理结果
            search_results: 搜索结果
        """
        try:
            if not self.pam or not hasattr(self.pam, 'get_listener'):
                return

            listener = self.pam.get_listener()
            if not hasattr(listener, 'receive_percept'):
                return

            # 提交推理结果
            for result in reasoning_results:
                lida_node = self.nars_to_lida_conversion(result)
                if lida_node:
                    listener.receive_percept(lida_node, ModuleName.CurrentSM)

            # 提交搜索结果
            for result in search_results:
                if hasattr(result, 'get_node_name') or hasattr(result, 'getTNname'):
                    # 已经是LIDA节点
                    listener.receive_percept(result, ModuleName.CurrentSM)
                else:
                    # 需要转换
                    lida_node = self.nars_to_lida_conversion(result)
                    if lida_node:
                        listener.receive_percept(lida_node, ModuleName.CurrentSM)

            self.logger.info(f"Integrated {len(reasoning_results)} reasoning results and {len(search_results)} search results")

        except Exception as e:
            self.logger.error(f"Error integrating with LIDA workspace: {e}")

    # 辅助方法
    def is_implication(self, term):
        """检查术语是否是蕴含关系"""
        try:
            term_str = str(term)
            return '-->' in term_str or '==>' in term_str or 'implies' in term_str.lower()
        except:
            return False

    def is_negation(self, term):
        """检查术语是否是否定"""
        try:
            term_str = str(term)
            return term_str.startswith('--') or 'not' in term_str.lower() or '非' in term_str
        except:
            return False

    def extract_implication_parts(self, implication):
        """提取蕴含关系的前件和后件"""
        try:
            term_str = str(implication)
            if '-->' in term_str:
                parts = term_str.split('-->')
                return parts[0].strip(), parts[1].strip()
            elif '==>' in term_str:
                parts = term_str.split('==>')
                return parts[0].strip(), parts[1].strip()
            return None, None
        except:
            return None, None

    def extract_negated_term(self, negation):
        """提取否定术语中的原术语"""
        try:
            term_str = str(negation)
            if term_str.startswith('--'):
                return term_str[2:].strip()
            elif term_str.startswith('not '):
                return term_str[4:].strip()
            elif term_str.startswith('非'):
                return term_str[1:].strip()
            return term_str
        except:
            return str(negation)

    def terms_match(self, term1, term2):
        """检查两个术语是否匹配"""
        try:
            return str(term1).strip() == str(term2).strip()
        except:
            return False

    def create_conclusion(self, term, rule_type):
        """创建推理结论"""
        try:
            if NARS_AVAILABLE and self.nar and hasattr(self.nar, 'narsese'):
                return self.nar.narsese.parse_term(str(term))
            else:
                # 简单的结论对象
                return {'term': term, 'rule': rule_type, 'confidence': 0.8}
        except:
            return {'term': term, 'rule': rule_type, 'confidence': 0.8}

    def create_negation(self, term):
        """创建术语的否定"""
        try:
            return f"--{term}"
        except:
            return f"--{str(term)}"

    def filter_conclusions(self, conclusions):
        """过滤和排序结论"""
        try:
            # 去重
            unique_conclusions = []
            seen = set()

            for conclusion in conclusions:
                conclusion_str = str(conclusion)
                if conclusion_str not in seen:
                    seen.add(conclusion_str)
                    unique_conclusions.append(conclusion)

            # 按置信度排序（如果有的话）
            try:
                unique_conclusions.sort(
                    key=lambda x: getattr(x, 'confidence', 0.5) if hasattr(x, 'confidence')
                    else x.get('confidence', 0.5) if isinstance(x, dict) else 0.5,
                    reverse=True
                )
            except:
                pass

            return unique_conclusions[:self.integration_params['max_premises']]

        except Exception as e:
            self.logger.error(f"Error filtering conclusions: {e}")
            return conclusions

    def get_node_id(self, node):
        """获取节点ID"""
        try:
            if hasattr(node, 'get_node_id'):
                return str(node.get_node_id())
            elif hasattr(node, 'getNodeId'):
                return str(node.getNodeId())
            elif hasattr(node, 'id'):
                return str(node.id)
            else:
                return str(id(node))
        except:
            return str(id(node))

    def get_node_name(self, node):
        """获取节点名称"""
        try:
            if hasattr(node, 'get_node_name'):
                return node.get_node_name()
            elif hasattr(node, 'getTNname'):
                return node.getTNname()
            elif hasattr(node, 'getName'):
                return node.getName()
            elif hasattr(node, 'name'):
                return node.name
            else:
                return str(node)
        except:
            return str(node)

    def get_result_name(self, result):
        """获取结果名称"""
        try:
            if hasattr(result, 'term'):
                return str(result.term)
            else:
                return self.get_node_name(result)
        except:
            return str(result)

    def get_intersection_type(self, reasoning_result, search_result):
        """获取交集类型"""
        try:
            # 简单的类型判断
            if hasattr(reasoning_result, 'rule'):
                return f"reasoning_{reasoning_result.rule}"
            else:
                return "semantic_match"
        except:
            return "unknown"

    def build_reasoning_path(self, start_terms, intersection, goal_terms):
        """构建推理路径"""
        try:
            path = {
                'start': start_terms,
                'intersection': intersection,
                'goal': goal_terms,
                'steps': []
            }

            # 简化的路径构建
            path['steps'].append({
                'type': 'reasoning',
                'from': start_terms,
                'to': intersection['reasoning_result']
            })

            path['steps'].append({
                'type': 'search',
                'from': intersection['search_result'],
                'to': goal_terms
            })

            return path

        except Exception as e:
            self.logger.error(f"Error building reasoning path: {e}")
            return None

    def get_statistics(self):
        """获取统计信息"""
        return self.stats.copy()

    def clear_cache(self):
        """清空缓存"""
        with self.cache_lock:
            self.conversion_cache.clear()
        self.logger.info("Conversion cache cleared")

    def cleanup_contexts(self, max_age_seconds=3600):
        """清理过期的推理上下文"""
        try:
            current_time = time.time()
            expired_contexts = []

            with self.context_lock:
                for context_id, context in self.reasoning_contexts.items():
                    if current_time - context.last_updated > max_age_seconds:
                        expired_contexts.append(context_id)

                for context_id in expired_contexts:
                    del self.reasoning_contexts[context_id]

            if expired_contexts:
                self.logger.info(f"Cleaned up {len(expired_contexts)} expired reasoning contexts")

        except Exception as e:
            self.logger.error(f"Error cleaning up contexts: {e}")
