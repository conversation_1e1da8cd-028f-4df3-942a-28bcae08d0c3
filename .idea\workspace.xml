<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="5364511b-dfb5-48bf-8534-d1f847bf722f" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectCodeStyleSettingsMigration">
    <option name="version" value="2" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2wOKGQN3cJmCK9uHH8vtKBHLPt1" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="autoscrollToSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="openDirectoriesWithSingleClick" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Python.run0.executor": "Debug",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "codeReviewSummary": "[]",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "G:/linars/linars-all-by-ag/理论架构",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "SDK",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.4213564",
    "run.code.analysis.last.selected.profile": "pProject Default",
    "settings.editor.selected.configurable": "preferences.lookFeel",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="G:\linars\linars-all-by-ag\理论架构" />
      <recent name="G:\linars\linars-all-by-ag\linars\org\opennars\language" />
      <recent name="G:\linars\linars-all-by-ag\linars\edu\memphis\ccrg\lida\Alifeagent\configs" />
      <recent name="G:\linars\linars-all-by-ag\configs" />
      <recent name="G:\linars\linars-all-by-ag\linars\edu\memphis\ccrg\lida\Framework\Initialization" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="G:\linars\linars-all-by-ag\linars\edu\memphis\ccrg\lida\Workspace\WorkspaceBuffers" />
      <recent name="G:\linars\linars-all-by-ag" />
      <recent name="G:\linars\linars-all-by-ag\linars" />
      <recent name="G:\linars\linars-all-by-ag\configs" />
      <recent name="G:\linars\linars-all-by-ag\linars\org\opennars\language" />
    </key>
  </component>
  <component name="RunManager">
    <configuration default="true" type="Babashka" factoryName="BabashkaLocalRepl" activateToolWindowBeforeRun="false">
      <setting name="displayName" value="" />
      <setting name="bbPath" value="" />
      <setting name="parameters" value="" />
      <option name="PARENT_ENVS" value="true" />
      <setting name="workingDir" value="" />
      <setting name="focusEditor" value="false" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="ClojureREPL" factoryName="Local" activateToolWindowBeforeRun="false">
      <method v="2" />
    </configuration>
    <configuration default="true" type="ClojureREPL" factoryName="Remote" activateToolWindowBeforeRun="false">
      <setting name="displayName" value="" />
      <setting name="host" value="" />
      <setting name="port" value="0" />
      <setting name="replType" value="SOCKET" />
      <setting name="configType" value="SPECIFY" />
      <setting name="replPortFileType" value="STANDARD" />
      <setting name="customPortFile" value="" />
      <setting name="fixLineNumbers" value="false" />
      <setting name="focusEditor" value="false" />
      <setting name="urlFile" value="" />
      <method v="2" />
    </configuration>
    <configuration name="run0" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="linars-all-by-ag" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/alifeagent" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/alifeagent/run0.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.run0" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="5364511b-dfb5-48bf-8534-d1f847bf722f" name="更改" comment="" />
      <created>1745907084316</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1745907084316</updated>
      <workItem from="1745907085252" duration="27804000" />
      <workItem from="1745934985852" duration="14293000" />
      <workItem from="1745977632819" duration="1511000" />
      <workItem from="1745982907092" duration="4682000" />
      <workItem from="1745987639239" duration="1999000" />
      <workItem from="1745989652931" duration="3318000" />
      <workItem from="1745992997627" duration="16245000" />
      <workItem from="1746009288254" duration="782000" />
      <workItem from="1746010084044" duration="182000" />
      <workItem from="1746010286380" duration="6265000" />
      <workItem from="1746020416967" duration="2208000" />
      <workItem from="1746022640716" duration="2879000" />
      <workItem from="1746025752912" duration="12086000" />
      <workItem from="1746053277349" duration="3753000" />
      <workItem from="1746070634871" duration="59000" />
      <workItem from="1746070718811" duration="26000" />
      <workItem from="1746070760580" duration="377000" />
      <workItem from="1746071153174" duration="2694000" />
      <workItem from="1746073899726" duration="1841000" />
      <workItem from="1746075770030" duration="571000" />
      <workItem from="1746081507196" duration="4764000" />
      <workItem from="1746086303422" duration="4260000" />
      <workItem from="1746090605073" duration="5333000" />
      <workItem from="1746095963273" duration="1473000" />
      <workItem from="1746097452507" duration="2978000" />
      <workItem from="1746100450812" duration="5751000" />
      <workItem from="1746106237300" duration="2198000" />
      <workItem from="1746108452047" duration="5968000" />
      <workItem from="1746115403047" duration="4259000" />
      <workItem from="1746119680703" duration="1376000" />
      <workItem from="1746121075439" duration="1721000" />
      <workItem from="1746122813054" duration="2849000" />
      <workItem from="1746152465024" duration="3881000" />
      <workItem from="1746156605586" duration="1715000" />
      <workItem from="1746158334112" duration="9812000" />
      <workItem from="1746169566553" duration="5522000" />
      <workItem from="1746175103651" duration="8793000" />
      <workItem from="1746183917199" duration="2445000" />
      <workItem from="1746186378981" duration="3589000" />
      <workItem from="1746190007530" duration="3415000" />
      <workItem from="1746193633361" duration="1445000" />
      <workItem from="1746195097978" duration="4757000" />
      <workItem from="1746199876641" duration="1092000" />
      <workItem from="1746200983246" duration="211000" />
      <workItem from="1746201209162" duration="10814000" />
      <workItem from="1746237590542" duration="20529000" />
      <workItem from="1746259425661" duration="1329000" />
      <workItem from="1746260770431" duration="9518000" />
      <workItem from="1746272229405" duration="24834000" />
      <workItem from="1746326254070" duration="11401000" />
      <workItem from="1746338338325" duration="16800000" />
      <workItem from="1746355176058" duration="9879000" />
      <workItem from="1746365080091" duration="24053000" />
      <workItem from="1746412974682" duration="17961000" />
      <workItem from="1746810460696" duration="65000" />
      <workItem from="1757007649182" duration="495000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/agent_starter.py</url>
          <line>51</line>
          <option name="timeStamp" value="5" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/SelectConceptTask.py</url>
          <line>37</line>
          <option name="timeStamp" value="44" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/inference_control.py</url>
          <line>33</line>
          <option name="timeStamp" value="45" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/memory.py</url>
          <line>183</line>
          <option name="timeStamp" value="46" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/operator/mental/doubt.py</url>
          <line>33</line>
          <option name="timeStamp" value="47" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/concept.py</url>
          <line>387</line>
          <option name="timeStamp" value="49" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/operator/mental/consider.py</url>
          <line>36</line>
          <option name="timeStamp" value="51" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/operator/mental/remind.py</url>
          <line>35</line>
          <option name="timeStamp" value="52" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/storage/buffer.py</url>
          <line>208</line>
          <option name="timeStamp" value="53" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/memory.py</url>
          <line>608</line>
          <option name="timeStamp" value="55" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/GoalBackgroundTask.py</url>
          <line>403</line>
          <option name="timeStamp" value="56" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/GoalBackgroundTask.py</url>
          <line>234</line>
          <option name="timeStamp" value="57" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/GoalBackgroundTask.py</url>
          <line>157</line>
          <option name="timeStamp" value="60" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/ProcessGBufferTask.py</url>
          <line>79</line>
          <option name="timeStamp" value="61" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/ProcessGBufferTask.py</url>
          <line>181</line>
          <option name="timeStamp" value="63" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/ProcessGBufferTask.py</url>
          <line>26</line>
          <option name="timeStamp" value="64" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/storage/buffer.py</url>
          <line>100</line>
          <option name="timeStamp" value="65" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>409</line>
          <option name="timeStamp" value="66" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>248</line>
          <option name="timeStamp" value="71" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>265</line>
          <option name="timeStamp" value="73" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>262</line>
          <option name="timeStamp" value="74" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>259</line>
          <option name="timeStamp" value="75" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>251</line>
          <option name="timeStamp" value="77" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/language/statement.py</url>
          <line>79</line>
          <option name="timeStamp" value="78" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>335</line>
          <option name="timeStamp" value="79" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/entity/sentence.py</url>
          <line>85</line>
          <option name="timeStamp" value="80" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>215</line>
          <option name="timeStamp" value="81" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/FrameworkModuleImpl.py</url>
          <line>123</line>
          <option name="timeStamp" value="82" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceBuffers/WSBufferImpl_graph.py</url>
          <line>40</line>
          <option name="timeStamp" value="83" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>213</line>
          <option name="timeStamp" value="84" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/ProcessGBufferTask.py</url>
          <line>33</line>
          <option name="timeStamp" value="86" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/SubGraphSet.py</url>
          <line>30</line>
          <option name="timeStamp" value="87" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/memory.py</url>
          <line>61</line>
          <option name="timeStamp" value="88" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/memory.py</url>
          <line>537</line>
          <option name="timeStamp" value="89" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/inference_control.py</url>
          <line>128</line>
          <option name="timeStamp" value="90" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/concept/process_anticipation.py</url>
          <line>223</line>
          <option name="timeStamp" value="91" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/inference/compositional_rules.py</url>
          <line>253</line>
          <option name="timeStamp" value="92" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/inference/rule_tables.py</url>
          <line>69</line>
          <option name="timeStamp" value="93" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/storage/buffer.py</url>
          <line>166</line>
          <option name="timeStamp" value="94" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/memory.py</url>
          <line>228</line>
          <option name="timeStamp" value="96" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceBuffers/WMBufferImpl_mem.py</url>
          <line>36</line>
          <option name="timeStamp" value="97" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceBuffers/WMBufferImpl_mem.py</url>
          <line>35</line>
          <option name="timeStamp" value="98" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/agent_xml_factory.py</url>
          <line>1457</line>
          <option name="timeStamp" value="99" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/agent_xml_factory.py</url>
          <line>1043</line>
          <option name="timeStamp" value="100" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/agent_xml_factory.py</url>
          <line>1028</line>
          <option name="timeStamp" value="102" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/agent_starter.py</url>
          <line>44</line>
          <option name="timeStamp" value="103" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/agent_starter.py</url>
          <line>42</line>
          <option name="timeStamp" value="104" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/derivation_context.py</url>
          <line>23</line>
          <option name="timeStamp" value="107" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/memory.py</url>
          <line>198</line>
          <option name="timeStamp" value="112" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/GoalBackgroundTask.py</url>
          <line>64</line>
          <option name="timeStamp" value="113" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/main/nar.py</url>
          <line>111</line>
          <option name="timeStamp" value="114" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceBuffers/WMBufferImpl_mem.py</url>
          <line>37</line>
          <option name="timeStamp" value="115" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/memory.py</url>
          <line>532</line>
          <option name="timeStamp" value="116" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/ProcessGBufferTask.py</url>
          <line>91</line>
          <option name="timeStamp" value="117" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/ProcessGBufferTask.py</url>
          <line>30</line>
          <option name="timeStamp" value="120" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/process_goal.py</url>
          <line>209</line>
          <option name="timeStamp" value="121" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/GoalBackgroundTask.py</url>
          <line>86</line>
          <option name="timeStamp" value="122" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Tasks/FrameworkTaskImpl.py</url>
          <line>150</line>
          <option name="timeStamp" value="123" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/concept/process_task.py</url>
          <line>47</line>
          <option name="timeStamp" value="124" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/inference_control.py</url>
          <line>101</line>
          <option name="timeStamp" value="129" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/concept/process_task.py</url>
          <line>45</line>
          <option name="timeStamp" value="131" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/concept/process_judgment.py</url>
          <line>205</line>
          <option name="timeStamp" value="132" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/NodeStructureImpl.py</url>
          <line>824</line>
          <option name="timeStamp" value="148" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Data/neo_util.py</url>
          <line>589</line>
          <option name="timeStamp" value="149" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/NodeStructureImpl.py</url>
          <line>827</line>
          <option name="timeStamp" value="150" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/LinkImpl.py</url>
          <line>147</line>
          <option name="timeStamp" value="151" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/LinkImpl.py</url>
          <line>174</line>
          <option name="timeStamp" value="152" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Data/neo_util.py</url>
          <line>451</line>
          <option name="timeStamp" value="153" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/NodeStructureImpl.py</url>
          <line>137</line>
          <option name="timeStamp" value="154" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/NodeStructureImpl.py</url>
          <line>73</line>
          <option name="timeStamp" value="155" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/NodeStructureImpl.py</url>
          <line>75</line>
          <option name="timeStamp" value="161" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/concept/process_judgment.py</url>
          <line>180</line>
          <option name="timeStamp" value="162" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Featuredetectors/listen_detector.py</url>
          <line>183</line>
          <option name="timeStamp" value="163" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamNodeImpl.py</url>
          <line>30</line>
          <option name="timeStamp" value="165" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Data/neo_util.py</url>
          <line>168</line>
          <option name="timeStamp" value="166" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Data/neo_util.py</url>
          <line>155</line>
          <option name="timeStamp" value="167" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/storage/bag1.py</url>
          <line>172</line>
          <option name="timeStamp" value="169" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/storage/bag1.py</url>
          <line>74</line>
          <option name="timeStamp" value="170" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/inference/temporal_rules.py</url>
          <line>58</line>
          <option name="timeStamp" value="173" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/entity/stamp.py</url>
          <line>424</line>
          <option name="timeStamp" value="174" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/GoalBackgroundTask.py</url>
          <line>154</line>
          <option name="timeStamp" value="177" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/inference_control.py</url>
          <line>75</line>
          <option name="timeStamp" value="179" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/concept/process_task.py</url>
          <line>63</line>
          <option name="timeStamp" value="182" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/SubGraphSet.py</url>
          <line>45</line>
          <option name="timeStamp" value="185" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/concept/process_task.py</url>
          <line>92</line>
          <option name="timeStamp" value="187" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/derivation_context.py</url>
          <line>215</line>
          <option name="timeStamp" value="198" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/derivation_context.py</url>
          <line>235</line>
          <option name="timeStamp" value="200" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/derivation_context.py</url>
          <line>300</line>
          <option name="timeStamp" value="202" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/derivation_context.py</url>
          <line>315</line>
          <option name="timeStamp" value="203" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/derivation_context.py</url>
          <line>134</line>
          <option name="timeStamp" value="211" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>341</line>
          <option name="timeStamp" value="212" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/concept/process_judgment.py</url>
          <line>235</line>
          <option name="timeStamp" value="213" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/entity/stamp.py</url>
          <line>572</line>
          <option name="timeStamp" value="214" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>346</line>
          <option name="timeStamp" value="224" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>192</line>
          <option name="timeStamp" value="225" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>151</line>
          <option name="timeStamp" value="226" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/ProcessGBufferTask.py</url>
          <line>106</line>
          <option name="timeStamp" value="230" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/concept.py</url>
          <line>290</line>
          <option name="timeStamp" value="231" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>380</line>
          <option name="timeStamp" value="234" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/ProcessGBufferTask.py</url>
          <line>93</line>
          <option name="timeStamp" value="237" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/storage/bag1.py</url>
          <line>102</line>
          <option name="timeStamp" value="239" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/ProcessGBufferTask.py</url>
          <line>103</line>
          <option name="timeStamp" value="241" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>385</line>
          <option name="timeStamp" value="242" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>349</line>
          <option name="timeStamp" value="243" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/ProcessGBufferTask.py</url>
          <line>105</line>
          <option name="timeStamp" value="247" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/GoalBackgroundTask.py</url>
          <line>152</line>
          <option name="timeStamp" value="251" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/GoalBackgroundTask.py</url>
          <line>328</line>
          <option name="timeStamp" value="253" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/process_goal.py</url>
          <line>1094</line>
          <option name="timeStamp" value="254" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>190</line>
          <option name="timeStamp" value="255" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/BasicPamInitializer.py</url>
          <line>45</line>
          <option name="timeStamp" value="263" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>417</line>
          <option name="timeStamp" value="286" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/compound_term.py</url>
          <line>936</line>
          <option name="timeStamp" value="289" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>487</line>
          <option name="timeStamp" value="291" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>525</line>
          <option name="timeStamp" value="293" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>515</line>
          <option name="timeStamp" value="294" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/derivation_context.py</url>
          <line>133</line>
          <option name="timeStamp" value="297" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/compound_term.py</url>
          <line>930</line>
          <option name="timeStamp" value="299" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/compound_term.py</url>
          <line>939</line>
          <option name="timeStamp" value="302" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/compound_term.py</url>
          <line>562</line>
          <option name="timeStamp" value="303" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/process_goal.py</url>
          <line>162</line>
          <option name="timeStamp" value="304" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/GoalBackgroundTask.py</url>
          <line>215</line>
          <option name="timeStamp" value="305" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>252</line>
          <option name="timeStamp" value="309" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>257</line>
          <option name="timeStamp" value="310" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/concept/process_judgment.py</url>
          <line>220</line>
          <option name="timeStamp" value="313" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/derivation_context.py</url>
          <line>205</line>
          <option name="timeStamp" value="314" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/derivation_context.py</url>
          <line>226</line>
          <option name="timeStamp" value="315" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/derivation_context.py</url>
          <line>291</line>
          <option name="timeStamp" value="316" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/ProcessGBufferTask.py</url>
          <line>126</line>
          <option name="timeStamp" value="319" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/memory.py</url>
          <line>232</line>
          <option name="timeStamp" value="321" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/derivation_context.py</url>
          <line>162</line>
          <option name="timeStamp" value="325" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/compound_term.py</url>
          <line>961</line>
          <option name="timeStamp" value="326" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/compound_term.py</url>
          <line>182</line>
          <option name="timeStamp" value="329" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/term.py</url>
          <line>515</line>
          <option name="timeStamp" value="330" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/language/variable.py</url>
          <line>139</line>
          <option name="timeStamp" value="331" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>565</line>
          <option name="timeStamp" value="333" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>326</line>
          <option name="timeStamp" value="336" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>437</line>
          <option name="timeStamp" value="338" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/language/implication.py</url>
          <line>203</line>
          <option name="timeStamp" value="343" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/language/statement.py</url>
          <line>343</line>
          <option name="timeStamp" value="344" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>430</line>
          <option name="timeStamp" value="345" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>399</line>
          <option name="timeStamp" value="349" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/process_goal.py</url>
          <line>1206</line>
          <option name="timeStamp" value="353" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/process_goal.py</url>
          <line>1014</line>
          <option name="timeStamp" value="354" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>255</line>
          <option name="timeStamp" value="358" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/language/statement.py</url>
          <line>237</line>
          <option name="timeStamp" value="361" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>284</line>
          <option name="timeStamp" value="362" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>290</line>
          <option name="timeStamp" value="363" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>299</line>
          <option name="timeStamp" value="364" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>307</line>
          <option name="timeStamp" value="365" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>428</line>
          <option name="timeStamp" value="366" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/language/implication.py</url>
          <line>174</line>
          <option name="timeStamp" value="367" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/GoalBackgroundTask.py</url>
          <line>198</line>
          <option name="timeStamp" value="372" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/memory.py</url>
          <line>193</line>
          <option name="timeStamp" value="375" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/memory.py</url>
          <line>207</line>
          <option name="timeStamp" value="376" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/GoalBackgroundTask.py</url>
          <line>193</line>
          <option name="timeStamp" value="377" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/GoalBackgroundTask.py</url>
          <line>307</line>
          <option name="timeStamp" value="381" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>369</line>
          <option name="timeStamp" value="383" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>340</line>
          <option name="timeStamp" value="385" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>476</line>
          <option name="timeStamp" value="386" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>472</line>
          <option name="timeStamp" value="387" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>617</line>
          <option name="timeStamp" value="388" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>643</line>
          <option name="timeStamp" value="395" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/process_goal.py</url>
          <line>168</line>
          <option name="timeStamp" value="396" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>712</line>
          <option name="timeStamp" value="397" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>703</line>
          <option name="timeStamp" value="398" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>721</line>
          <option name="timeStamp" value="399" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>743</line>
          <option name="timeStamp" value="400" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/process_goal.py</url>
          <line>1023</line>
          <option name="timeStamp" value="401" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>776</line>
          <option name="timeStamp" value="402" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>627</line>
          <option name="timeStamp" value="405" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/process_goal.py</url>
          <line>1358</line>
          <option name="timeStamp" value="406" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>286</line>
          <option name="timeStamp" value="407" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>71</line>
          <option name="timeStamp" value="408" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>805</line>
          <option name="timeStamp" value="409" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/process_goal.py</url>
          <line>1755</line>
          <option name="timeStamp" value="411" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>817</line>
          <option name="timeStamp" value="412" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/compound_term.py</url>
          <line>252</line>
          <option name="timeStamp" value="413" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>498</line>
          <option name="timeStamp" value="414" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/inference/local_rules_helper.py</url>
          <line>123</line>
          <option name="timeStamp" value="419" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/inference/local_rules_helper.py</url>
          <line>130</line>
          <option name="timeStamp" value="420" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/linars_all_by_ag$run0.coverage" NAME="run0 覆盖结果" MODIFIED="1746437106026" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/alifeagent" />
  </component>
</project>