"""
可执行图式测试文件
"""

import asyncio
import logging
from .TreeChart import TreeChart, SchemaType, ExecutionMode
from .SchemaFactory import schema_factory
from .SchemaExecutor import schema_executor, ExecutionContext

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_action_schema():
    """测试动作图式"""
    print("\n=== 测试动作图式 ===")
    
    # 创建打印动作图式
    print_schema = schema_factory.create_action_schema(
        action_type='print',
        action_params={'message': 'Hello from action schema!'}
    )
    
    # 创建执行上下文
    context = ExecutionContext(context_id='test_action')
    
    # 执行图式
    result = await schema_executor.execute(print_schema, context)
    
    print(f"执行结果: {result.success}")
    print(f"执行时间: {result.execution_time:.4f}s")
    print(f"结果: {result.result}")
    
    return result.success


async def test_sequence_schema():
    """测试顺序图式"""
    print("\n=== 测试顺序图式 ===")
    
    # 创建多个动作图式
    action1 = schema_factory.create_action_schema(
        action_type='print',
        action_params={'message': 'Step 1: Initialize'}
    )
    
    action2 = schema_factory.create_action_schema(
        action_type='set_variable',
        action_params={'variable_name': 'counter', 'variable_value': 10}
    )
    
    action3 = schema_factory.create_action_schema(
        action_type='print',
        action_params={'message': 'Step 3: Process complete'}
    )
    
    # 创建顺序图式
    sequence_schema = schema_factory.create_sequence_schema(
        steps=[action1, action2, action3]
    )
    
    # 创建执行上下文
    context = ExecutionContext(context_id='test_sequence')
    
    # 执行图式
    result = await schema_executor.execute(sequence_schema, context)
    
    print(f"执行结果: {result.success}")
    print(f"执行时间: {result.execution_time:.4f}s")
    print(f"变量值: {context.get_variable('counter')}")
    
    return result.success


async def test_condition_schema():
    """测试条件图式"""
    print("\n=== 测试条件图式 ===")
    
    # 创建条件动作（返回True）
    condition_action = schema_factory.create_action_schema(
        action_type='get_variable',
        action_params={'variable_name': 'test_condition'}
    )
    
    # 创建then分支
    then_action = schema_factory.create_action_schema(
        action_type='print',
        action_params={'message': 'Condition is true!'}
    )
    
    # 创建else分支
    else_action = schema_factory.create_action_schema(
        action_type='print',
        action_params={'message': 'Condition is false!'}
    )
    
    # 创建条件图式
    condition_schema = schema_factory.create_condition_schema(
        condition=condition_action,
        then_branch=then_action,
        else_branch=else_action
    )
    
    # 创建执行上下文并设置条件变量
    context = ExecutionContext(context_id='test_condition')
    context.set_variable('test_condition', True)
    
    # 执行图式
    result = await schema_executor.execute(condition_schema, context)
    
    print(f"执行结果: {result.success}")
    print(f"执行时间: {result.execution_time:.4f}s")
    
    return result.success


async def test_loop_schema():
    """测试循环图式"""
    print("\n=== 测试循环图式 ===")
    
    # 创建循环条件（检查计数器）
    condition_action = schema_factory.create_action_schema(
        action_type='get_variable',
        action_params={'variable_name': 'loop_counter'}
    )
    
    # 创建循环体（打印并递减计数器）
    body_sequence = schema_factory.create_sequence_schema([
        schema_factory.create_action_schema(
            action_type='print',
            action_params={'message': 'Loop iteration'}
        ),
        schema_factory.create_action_schema(
            action_type='set_variable',
            action_params={'variable_name': 'loop_counter', 'variable_value': 2}  # 简化：固定值
        )
    ])
    
    # 创建循环图式
    loop_schema = schema_factory.create_loop_schema(
        condition=condition_action,
        body=body_sequence,
        loop_type='while',
        max_iterations=3
    )
    
    # 创建执行上下文并设置初始计数器
    context = ExecutionContext(context_id='test_loop')
    context.set_variable('loop_counter', 3)
    
    # 执行图式
    result = await schema_executor.execute(loop_schema, context)
    
    print(f"执行结果: {result.success}")
    print(f"执行时间: {result.execution_time:.4f}s")
    print(f"迭代次数: {result.partial_results.get('iterations', 0)}")
    
    return result.success


async def test_scene_schema():
    """测试场景图式"""
    print("\n=== 测试场景图式 ===")
    
    # 创建多个并行执行的动作
    actions = []
    for i in range(3):
        action = schema_factory.create_action_schema(
            action_type='print',
            action_params={'message': f'Parallel action {i+1}'}
        )
        actions.append(action)
    
    # 创建场景图式
    scene_schema = schema_factory.create_scene_schema(
        scene_elements=actions,
        parallel_execution=True
    )
    
    # 创建执行上下文
    context = ExecutionContext(context_id='test_scene')
    
    # 执行图式
    result = await schema_executor.execute(scene_schema, context)
    
    print(f"执行结果: {result.success}")
    print(f"执行时间: {result.execution_time:.4f}s")
    print(f"成功元素: {result.partial_results.get('successful', 0)}")
    print(f"失败元素: {result.partial_results.get('failed', 0)}")
    
    return result.success


async def test_complex_nested_schema():
    """测试复杂嵌套图式"""
    print("\n=== 测试复杂嵌套图式 ===")
    
    # 创建初始化序列
    init_sequence = schema_factory.create_sequence_schema([
        schema_factory.create_action_schema(
            action_type='print',
            action_params={'message': 'Initializing system...'}
        ),
        schema_factory.create_action_schema(
            action_type='set_variable',
            action_params={'variable_name': 'system_ready', 'variable_value': True}
        )
    ])
    
    # 创建主处理逻辑
    main_logic = schema_factory.create_condition_schema(
        condition=schema_factory.create_action_schema(
            action_type='get_variable',
            action_params={'variable_name': 'system_ready'}
        ),
        then_branch=schema_factory.create_action_schema(
            action_type='print',
            action_params={'message': 'System is ready, processing...'}
        ),
        else_branch=schema_factory.create_action_schema(
            action_type='print',
            action_params={'message': 'System not ready!'}
        )
    )
    
    # 创建清理序列
    cleanup_sequence = schema_factory.create_sequence_schema([
        schema_factory.create_action_schema(
            action_type='print',
            action_params={'message': 'Cleaning up...'}
        ),
        schema_factory.create_action_schema(
            action_type='set_variable',
            action_params={'variable_name': 'system_ready', 'variable_value': False}
        )
    ])
    
    # 创建整体流程
    main_flow = schema_factory.create_sequence_schema([
        init_sequence,
        main_logic,
        cleanup_sequence
    ])
    
    # 创建执行上下文
    context = ExecutionContext(context_id='test_complex')
    
    # 执行图式
    result = await schema_executor.execute(main_flow, context)
    
    print(f"执行结果: {result.success}")
    print(f"执行时间: {result.execution_time:.4f}s")
    print(f"最终系统状态: {context.get_variable('system_ready')}")
    
    return result.success


async def test_execution_modes():
    """测试不同执行模式"""
    print("\n=== 测试执行模式 ===")
    
    # 创建一个简单的动作图式
    action_schema = schema_factory.create_action_schema(
        action_type='print',
        action_params={'message': 'Testing execution modes'}
    )
    
    modes = [ExecutionMode.MACHINE, ExecutionMode.HUMAN, ExecutionMode.ADAPTIVE]
    results = []
    
    for mode in modes:
        print(f"\n测试 {mode.value} 模式:")
        context = ExecutionContext(context_id=f'test_{mode.value}')
        context.execution_mode = mode
        
        result = await schema_executor.execute(action_schema, context)
        results.append(result.success)
        
        print(f"  执行结果: {result.success}")
        print(f"  执行时间: {result.execution_time:.4f}s")
        print(f"  执行模式: {result.metadata.get('execution_mode')}")
    
    return all(results)


async def main():
    """主测试函数"""
    print("开始可执行图式测试...")
    
    tests = [
        test_action_schema,
        test_sequence_schema,
        test_condition_schema,
        test_loop_schema,
        test_scene_schema,
        test_complex_nested_schema,
        test_execution_modes
    ]
    
    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            logger.error(f"测试 {test.__name__} 失败: {e}")
            results.append(False)
    
    # 打印统计信息
    print("\n=== 执行统计 ===")
    stats = schema_executor.get_execution_stats()
    for key, value in stats.items():
        if isinstance(value, dict):
            print(f"{key}:")
            for sub_key, sub_value in value.items():
                print(f"  {sub_key}: {sub_value}")
        else:
            print(f"{key}: {value}")
    
    # 总结
    successful_tests = sum(results)
    total_tests = len(results)
    print(f"\n=== 测试总结 ===")
    print(f"成功测试: {successful_tests}/{total_tests}")
    print(f"成功率: {successful_tests/total_tests*100:.1f}%")
    
    return successful_tests == total_tests


if __name__ == "__main__":
    asyncio.run(main())
