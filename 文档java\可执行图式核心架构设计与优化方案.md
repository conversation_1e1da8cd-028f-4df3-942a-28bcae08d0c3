# 可执行图式核心架构设计与优化方案

## 一、引言

### 1. 项目背景

本文档基于对Java版本LINARS项目的深入分析，特别是对可执行图式相关实现的详细研究，提出Python版本的优化架构设计方案。通过参考"文档java"文件夹下的内核分析文档，结合Java源码的复杂逻辑，我们将对Python版本的可执行图式进行系统性的架构重构和功能完善。

### 2. 核心目标

- **架构清晰化**：实现图式表示与执行的清晰分离
- **性能优化**：提升执行效率和并发处理能力  
- **功能完善**：支持复杂的控制流结构和嵌套执行
- **集成增强**：与NARS推理系统和搜索机制深度集成
- **模式支持**：同时支持机算模式（精确执行）和人算模式（模糊执行）

## 二、Java版本分析总结

### 1. 核心组件分析

基于对Java源码的分析，可执行图式的核心组件包括：

#### 1.1 图式表示层
- **TreeChart**：语义/语法树的核心表示类，继承自CompoundTerm
- **TreeBag**：管理TreeChart的容器，支持优先级排序
- **TreeNode**：树节点的基本表示
- **SubGraph/SubGraphSet**：子图结构的表示和管理

#### 1.2 执行控制层  
- **DoSelectTreeTask**：条件判断结构的执行任务
- **DoSuccTask**：顺序执行结构的执行任务
- **ForEachTask**：循环结构的执行任务
- **DoSimpleSceneTask**：场景执行的基础任务
- **IsaPamTask**：变量绑定和参数处理任务

#### 1.3 数据管理层
- **PamImpl0**：感知联想记忆的核心实现
- **NodeStructure**：节点结构的管理
- **WorkspaceBuffer**：工作空间缓冲区

### 2. 执行流程分析

Java版本的执行流程遵循以下模式：

1. **入口定位**：通过`getActRoot`方法定位执行入口点
2. **结构识别**：根据边类型（时序首、判断首、循环条件等）识别控制结构
3. **任务创建**：创建相应的执行任务（DoSelectTreeTask、ForEachTask等）
4. **任务调度**：通过AssistingTaskSpawner进行任务调度
5. **变量处理**：在执行过程中处理变量绑定和替换
6. **结果处理**：处理执行结果并进行状态更新
7. **回溯处理**：支持执行完成后的回溯机制

### 3. 关键设计模式

- **任务模式**：每个执行单元都封装为独立的任务
- **策略模式**：不同的控制结构使用不同的执行策略
- **观察者模式**：通过监听器机制处理执行结果
- **工厂模式**：动态创建不同类型的执行任务

## 三、Python版本架构设计

### 1. 整体架构设计

基于对Java版本的分析和Python语言特性，我们设计了以下分层架构：

```
┌─────────────────────────────────────────────────────────────┐
│                    应用接口层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   自然语言接口   │  │   图式构建接口   │  │   执行控制接口   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    执行引擎层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   图式解释器     │  │   执行调度器     │  │   上下文管理器   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   错误处理器     │  │   性能监控器     │  │   缓存管理器     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    图式表示层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   图式模型       │  │   控制流结构     │  │   变量绑定       │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   图式容器       │  │   图式工厂       │  │   图式验证器     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    集成服务层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   NARS集成       │  │   搜索集成       │  │   数据库集成     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   激活扩散       │  │   注意力机制     │  │   学习机制       │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2. 核心设计原则

1. **分离关注点**：图式表示、执行逻辑、集成服务分离
2. **可扩展性**：支持新的控制结构和执行策略
3. **高性能**：异步执行、缓存优化、并发处理
4. **容错性**：完善的错误处理和恢复机制
5. **可观测性**：详细的执行监控和性能统计

### 3. 关键技术特性

- **异步执行引擎**：基于asyncio的高性能异步执行
- **多模式支持**：机算、人算、自适应三种执行模式
- **智能缓存**：多层缓存机制提升执行效率
- **动态优化**：运行时性能监控和自动优化
- **深度集成**：与NARS、搜索、激活扩散的无缝集成

## 四、实现路线图

### 阶段一：核心架构实现（1-2周）
1. 图式表示模型设计与实现
2. 基础执行引擎开发
3. 核心控制流结构支持

### 阶段二：功能完善（2-3周）
1. 变量绑定机制实现
2. 错误处理和恢复机制
3. 性能优化和缓存系统

### 阶段三：集成优化（2-3周）
1. NARS推理系统集成
2. 搜索机制深度集成
3. 激活扩散机制集成

### 阶段四：测试验证（1-2周）
1. 单元测试和集成测试
2. 性能基准测试
3. 功能验证和优化

本架构设计为LINARS Python版本的可执行图式提供了坚实的技术基础，确保系统的高性能、高可靠性和强扩展性。

## 五、核心组件详细设计

### 1. 图式表示模型

#### 1.1 ExecutableSchema基类设计

```python
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from enum import Enum
import uuid
import time

class SchemaType(Enum):
    SEQUENCE = "sequence"
    CONDITION = "condition"
    LOOP = "loop"
    ACTION = "action"
    VARIABLE = "variable"
    COMPOSITE = "composite"

class ExecutionMode(Enum):
    MACHINE = "machine"  # 机算模式（精确执行）
    HUMAN = "human"      # 人算模式（模糊执行）
    ADAPTIVE = "adaptive" # 自适应模式

class ExecutableSchema(ABC):
    """可执行图式的抽象基类"""

    def __init__(self, schema_id: str = None, schema_type: SchemaType = None):
        self.schema_id = schema_id or str(uuid.uuid4())
        self.schema_type = schema_type
        self.parameters = {}
        self.sub_schemas = []
        self.parent_schema = None
        self.execution_mode = ExecutionMode.ADAPTIVE
        self.metadata = {
            'created_time': time.time(),
            'version': '1.0',
            'complexity': 0.0,
            'priority': 0.5
        }
        self.validation_rules = []
        self.execution_constraints = {}

    @abstractmethod
    def validate(self) -> bool:
        """验证图式的有效性"""
        pass

    @abstractmethod
    def get_execution_plan(self) -> List[Dict[str, Any]]:
        """获取执行计划"""
        pass

    def add_sub_schema(self, sub_schema: 'ExecutableSchema'):
        """添加子图式"""
        sub_schema.parent_schema = self
        self.sub_schemas.append(sub_schema)
        self._update_complexity()

    def set_parameter(self, key: str, value: Any):
        """设置参数"""
        self.parameters[key] = value

    def get_parameter(self, key: str, default: Any = None) -> Any:
        """获取参数"""
        return self.parameters.get(key, default)

    def _update_complexity(self):
        """更新复杂度计算"""
        base_complexity = 0.1
        sub_complexity = sum(sub.metadata.get('complexity', 0.1) for sub in self.sub_schemas)
        self.metadata['complexity'] = min(1.0, base_complexity + sub_complexity * 0.1)
```

#### 1.2 具体图式实现

```python
class SequenceSchema(ExecutableSchema):
    """顺序执行图式"""

    def __init__(self, schema_id: str = None, steps: List[ExecutableSchema] = None):
        super().__init__(schema_id, SchemaType.SEQUENCE)
        self.steps = steps or []
        self.execution_constraints = {
            'max_steps': 100,
            'timeout': 300,  # 5分钟超时
            'allow_partial_failure': False
        }

    def validate(self) -> bool:
        """验证顺序图式"""
        if not self.steps:
            return False
        if len(self.steps) > self.execution_constraints.get('max_steps', 100):
            return False
        return all(step.validate() for step in self.steps)

    def get_execution_plan(self) -> List[Dict[str, Any]]:
        """获取顺序执行计划"""
        plan = []
        for i, step in enumerate(self.steps):
            plan.append({
                'step_id': i,
                'schema': step,
                'type': 'sequential',
                'dependencies': [i-1] if i > 0 else [],
                'timeout': self.execution_constraints.get('timeout', 300) / len(self.steps)
            })
        return plan

    def add_step(self, step: ExecutableSchema, position: int = -1):
        """添加执行步骤"""
        if position == -1:
            self.steps.append(step)
        else:
            self.steps.insert(position, step)
        step.parent_schema = self
        self._update_complexity()

class ConditionalSchema(ExecutableSchema):
    """条件执行图式"""

    def __init__(self, schema_id: str = None, condition: ExecutableSchema = None,
                 then_branch: ExecutableSchema = None, else_branch: ExecutableSchema = None):
        super().__init__(schema_id, SchemaType.CONDITION)
        self.condition = condition
        self.then_branch = then_branch
        self.else_branch = else_branch
        self.execution_constraints = {
            'condition_timeout': 30,
            'branch_timeout': 300,
            'default_branch': 'then'  # 当条件无法判断时的默认分支
        }

    def validate(self) -> bool:
        """验证条件图式"""
        if not self.condition or not self.condition.validate():
            return False
        if not self.then_branch or not self.then_branch.validate():
            return False
        if self.else_branch and not self.else_branch.validate():
            return False
        return True

    def get_execution_plan(self) -> List[Dict[str, Any]]:
        """获取条件执行计划"""
        return [{
            'condition': self.condition,
            'then_branch': self.then_branch,
            'else_branch': self.else_branch,
            'type': 'conditional',
            'condition_timeout': self.execution_constraints.get('condition_timeout', 30),
            'branch_timeout': self.execution_constraints.get('branch_timeout', 300),
            'default_branch': self.execution_constraints.get('default_branch', 'then')
        }]

### 2. 执行引擎设计

#### 2.1 执行上下文管理

```python
import asyncio
import logging
from typing import Dict, List, Any, Optional, Callable
from concurrent.futures import ThreadPoolExecutor
import time
from dataclasses import dataclass, field

@dataclass
class ExecutionContext:
    """执行上下文"""
    context_id: str
    variables: Dict[str, Any] = field(default_factory=dict)
    execution_stack: List[Dict[str, Any]] = field(default_factory=list)
    execution_mode: ExecutionMode = ExecutionMode.ADAPTIVE
    start_time: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)
    parent_context: Optional['ExecutionContext'] = None
    child_contexts: List['ExecutionContext'] = field(default_factory=list)

    def create_child_context(self, child_id: str) -> 'ExecutionContext':
        """创建子上下文"""
        child = ExecutionContext(
            context_id=child_id,
            variables=self.variables.copy(),  # 继承变量
            execution_mode=self.execution_mode,
            parent_context=self
        )
        self.child_contexts.append(child)
        return child

    def set_variable(self, name: str, value: Any):
        """设置变量"""
        self.variables[name] = value

    def get_variable(self, name: str, default: Any = None) -> Any:
        """获取变量，支持作用域查找"""
        if name in self.variables:
            return self.variables[name]
        elif self.parent_context:
            return self.parent_context.get_variable(name, default)
        else:
            return default

    def push_execution(self, schema: ExecutableSchema):
        """压入执行栈"""
        self.execution_stack.append({
            'schema': schema,
            'start_time': time.time(),
            'status': 'running'
        })

    def pop_execution(self) -> Optional[Dict[str, Any]]:
        """弹出执行栈"""
        if self.execution_stack:
            return self.execution_stack.pop()
        return None

    def get_execution_depth(self) -> int:
        """获取执行深度"""
        return len(self.execution_stack)

@dataclass
class ExecutionResult:
    """执行结果"""
    success: bool
    result: Any = None
    error: Exception = None
    execution_time: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    partial_results: Dict[str, Any] = field(default_factory=dict)

    def merge(self, other: 'ExecutionResult'):
        """合并执行结果"""
        if other.success and not self.success:
            self.success = True

        if other.partial_results:
            self.partial_results.update(other.partial_results)

        self.execution_time += other.execution_time

        # 合并元数据
        if other.metadata:
            self.metadata.update(other.metadata)
```

#### 2.2 图式解释器核心实现

```python
class SchemaInterpreter:
    """图式解释器"""

    def __init__(self, max_workers: int = 4, cache_size: int = 1000):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.action_handlers = {}
        self.execution_cache = {}  # 执行结果缓存
        self.cache_size = cache_size
        self.execution_stats = {
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'average_execution_time': 0.0,
            'cache_hits': 0,
            'cache_misses': 0
        }
        self.performance_monitor = PerformanceMonitor()

    def register_action_handler(self, action_type: str, handler: Callable):
        """注册动作处理器"""
        self.action_handlers[action_type] = handler
        self.logger.info(f"Registered action handler for type: {action_type}")

    async def execute(self, schema: ExecutableSchema, context: ExecutionContext) -> ExecutionResult:
        """执行图式"""
        start_time = time.time()

        # 检查缓存
        cache_key = self._generate_cache_key(schema, context)
        if cache_key in self.execution_cache:
            self.execution_stats['cache_hits'] += 1
            cached_result = self.execution_cache[cache_key].copy()
            cached_result.metadata['from_cache'] = True
            return cached_result

        self.execution_stats['cache_misses'] += 1
        context.push_execution(schema)

        try:
            # 性能监控开始
            self.performance_monitor.start_execution(schema.schema_id)

            # 根据执行模式选择执行策略
            if context.execution_mode == ExecutionMode.MACHINE:
                result = await self._execute_precise(schema, context)
            elif context.execution_mode == ExecutionMode.HUMAN:
                result = await self._execute_fuzzy(schema, context)
            else:
                result = await self._execute_adaptive(schema, context)

            result.execution_time = time.time() - start_time
            result.metadata['execution_mode'] = context.execution_mode.value
            result.metadata['schema_type'] = schema.schema_type.value

            # 缓存结果
            self._cache_result(cache_key, result)

            # 更新统计
            self._update_stats(result)

            # 性能监控结束
            self.performance_monitor.end_execution(schema.schema_id, result.success)

            return result

        except Exception as e:
            self.logger.error(f"Error executing schema {schema.schema_id}: {e}")
            result = ExecutionResult(False, error=e)
            result.execution_time = time.time() - start_time
            result.metadata['execution_mode'] = context.execution_mode.value
            result.metadata['schema_type'] = schema.schema_type.value

            self._update_stats(result)
            self.performance_monitor.end_execution(schema.schema_id, False)

            return result
        finally:
            context.pop_execution()
```
```
