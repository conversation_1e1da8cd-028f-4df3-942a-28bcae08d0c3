"""
优化后的条件选择树任务
"""

import asyncio
import logging
from typing import List, Optional, Any, Dict, Set
from collections import defaultdict

from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory
from linars.edu.memphis.ccrg.lida.Data.neo_util import NeoUtil
from linars.edu.memphis.ccrg.lida.Nlanguage.TreeChart import TreeChart, SchemaType
from linars.edu.memphis.ccrg.lida.Nlanguage.SchemaFactory import schema_factory
from linars.edu.memphis.ccrg.lida.Nlanguage.SchemaExecutor import schema_executor, ExecutionContext
from .OptimizedControlFlowTask import OptimizedControlFlowTask, ControlFlowType, TaskStatus


class OptimizedDoSelectTreeTask(OptimizedControlFlowTask):
    """
    优化后的条件选择树任务
    
    主要改进：
    1. 基于图式的条件判断模型
    2. 支持复杂的条件表达式
    3. 智能的分支选择策略
    4. 完善的回溯机制
    5. 性能优化和缓存
    """
    
    def __init__(self, link: Link, pam: PAMemory, goal_ns: NodeStructure, 
                 scene_ns: NodeStructure, act_stamp: Optional[str] = None, **kwargs):
        super().__init__(ControlFlowType.CONDITION, 1, "optimized_select_tree")
        
        self.link = link
        self.pam = pam
        self.goal_ns = goal_ns
        self.scene_ns = scene_ns
        self.act_stamp = act_stamp
        
        # 条件选择相关属性
        self.condition_schema = None
        self.condition_branches = []
        self.else_branches = []
        self.selected_branch = None
        
        # 缓存和优化
        self.condition_cache = {}
        self.branch_cache = {}
        self.evaluation_history = []
        
        # 配置选项
        self.config.update({
            'condition_timeout': kwargs.get('condition_timeout', 30),
            'branch_timeout': kwargs.get('branch_timeout', 300),
            'default_branch': kwargs.get('default_branch', 'first'),
            'enable_branch_prediction': kwargs.get('enable_branch_prediction', True),
            'enable_condition_caching': kwargs.get('enable_condition_caching', True),
            'max_condition_depth': kwargs.get('max_condition_depth', 10)
        })
        
        # 初始化条件结构
        self._initialize_condition_structure()
    
    def _initialize_condition_structure(self):
        """初始化条件结构"""
        try:
            # 构建条件分支结构
            self._build_condition_branches()
            
            # 创建条件图式
            self._create_condition_schema()
            
            self.status = TaskStatus.READY
            self.logger.info(f"Initialized condition structure with {len(self.condition_branches)} branches")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize condition structure: {e}")
            self.status = TaskStatus.FAILED
            self.error_info = str(e)
    
    def _build_condition_branches(self):
        """构建条件分支结构"""
        # 获取工作空间缓冲区
        seq_ns = self.pam.get_workspace_buffer("seq").get_buffer_content(None)
        yufa_ns = self.pam.get_workspace_buffer("yufa").get_buffer_content(None)
        
        # 存储上位时序以便回溯
        if seq_ns and self.act_stamp:
            path_map = seq_ns.get_do_main_path_map()
            if self.act_stamp in path_map:
                path_map[self.act_stamp].append(self.link)
        
        # 查找判断首
        sink_name = self.link.get_sink().get_tn_name()
        query = f"match (m:场景)-[r:判断首]->(i:场景) where m.name = '{sink_name}' return r"
        
        # 执行查询并构建分支
        self._process_condition_query(query)
        
        # 查找else分支
        self._find_else_branches()
    
    def _process_condition_query(self, query: str):
        """处理条件查询"""
        try:
            # 使用缓存
            if query in self.condition_cache:
                condition_links = self.condition_cache[query]
            else:
                condition_links = self._execute_condition_query(query)
                if self.config.get('enable_condition_caching', True):
                    self.condition_cache[query] = condition_links
            
            # 处理条件链接
            for i, link in enumerate(condition_links):
                branch = {
                    'branch_id': i,
                    'condition_link': link,
                    'condition_node': link.get_sink() if link else None,
                    'type': 'condition_branch',
                    'priority': self._calculate_branch_priority(link),
                    'evaluation_count': 0,
                    'success_count': 0
                }
                self.condition_branches.append(branch)
            
            # 按优先级排序
            self.condition_branches.sort(key=lambda x: x['priority'], reverse=True)
            
        except Exception as e:
            self.logger.error(f"Failed to process condition query: {e}")
            raise e
    
    def _execute_condition_query(self, query: str) -> List[Link]:
        """执行条件查询"""
        try:
            link = NeoUtil.get_link_cypher(query)
            return [link] if link else []
        except Exception as e:
            self.logger.warning(f"Condition query failed: {e}")
            return []
    
    def _find_else_branches(self):
        """查找else分支"""
        try:
            # 查找else相关的链接
            sink_name = self.link.get_sink().get_tn_name()
            else_query = f"match (m:场景)-[r:否则]->(i:场景) where m.name = '{sink_name}' return r"
            
            else_links = self._execute_condition_query(else_query)
            
            for i, link in enumerate(else_links):
                else_branch = {
                    'branch_id': f"else_{i}",
                    'condition_link': link,
                    'condition_node': link.get_sink() if link else None,
                    'type': 'else_branch',
                    'priority': 0.1,  # else分支优先级较低
                    'evaluation_count': 0,
                    'success_count': 0
                }
                self.else_branches.append(else_branch)
                
        except Exception as e:
            self.logger.warning(f"Failed to find else branches: {e}")
    
    def _calculate_branch_priority(self, link: Optional[Link]) -> float:
        """计算分支优先级"""
        if not link:
            return 0.0
        
        # 基于激活度和历史成功率计算优先级
        base_priority = 0.5
        
        # 考虑节点激活度
        if hasattr(link, 'get_activation'):
            activation = link.get_activation()
            base_priority += activation * 0.3
        
        # 考虑历史成功率
        branch_id = str(link.get_node_id()) if hasattr(link, 'get_node_id') else str(hash(str(link)))
        if branch_id in self.branch_cache:
            cache_info = self.branch_cache[branch_id]
            success_rate = cache_info['success_count'] / max(cache_info['total_count'], 1)
            base_priority += success_rate * 0.2
        
        return min(1.0, base_priority)
    
    def _create_condition_schema(self):
        """创建条件图式"""
        if not self.condition_branches and not self.else_branches:
            # 创建默认条件图式
            self.condition_schema = self._create_default_condition_schema()
            return
        
        # 创建条件评估图式
        condition_action = schema_factory.create_action_schema(
            action_type='evaluate_condition',
            action_params={
                'branches': self.condition_branches,
                'else_branches': self.else_branches,
                'pam': self.pam,
                'act_stamp': self.act_stamp
            },
            schema_id=f"{self.task_id}_condition"
        )
        
        # 创建then分支图式
        then_branches = []
        for branch in self.condition_branches:
            branch_action = schema_factory.create_action_schema(
                action_type='execute_branch',
                action_params={
                    'branch': branch,
                    'pam': self.pam,
                    'act_stamp': self.act_stamp
                },
                schema_id=f"{self.task_id}_branch_{branch['branch_id']}"
            )
            then_branches.append(branch_action)
        
        then_schema = schema_factory.create_sequence_schema(
            steps=then_branches,
            schema_id=f"{self.task_id}_then_branches"
        ) if then_branches else None
        
        # 创建else分支图式
        else_schema = None
        if self.else_branches:
            else_actions = []
            for branch in self.else_branches:
                branch_action = schema_factory.create_action_schema(
                    action_type='execute_branch',
                    action_params={
                        'branch': branch,
                        'pam': self.pam,
                        'act_stamp': self.act_stamp
                    },
                    schema_id=f"{self.task_id}_else_{branch['branch_id']}"
                )
                else_actions.append(branch_action)
            
            else_schema = schema_factory.create_sequence_schema(
                steps=else_actions,
                schema_id=f"{self.task_id}_else_branches"
            )
        
        # 创建条件图式
        self.condition_schema = schema_factory.create_condition_schema(
            condition=condition_action,
            then_branch=then_schema,
            else_branch=else_schema,
            schema_id=f"{self.task_id}_condition_schema",
            condition_timeout=self.config.get('condition_timeout', 30),
            branch_timeout=self.config.get('branch_timeout', 300)
        )
    
    def _create_default_condition_schema(self) -> TreeChart:
        """创建默认条件图式"""
        default_action = schema_factory.create_action_schema(
            action_type='default_condition',
            action_params={
                'link': self.link,
                'pam': self.pam,
                'act_stamp': self.act_stamp
            },
            schema_id=f"{self.task_id}_default"
        )
        return default_action
    
    async def _execute_control_flow(self) -> Any:
        """执行控制流逻辑"""
        if not self.condition_schema:
            raise ValueError("Condition schema not initialized")
        
        # 创建执行上下文
        execution_context = ExecutionContext(
            context_id=f"{self.task_id}_execution"
        )
        
        # 设置执行变量
        execution_context.set_variable('pam', self.pam)
        execution_context.set_variable('act_stamp', self.act_stamp)
        execution_context.set_variable('link', self.link)
        execution_context.set_variable('goal_ns', self.goal_ns)
        execution_context.set_variable('scene_ns', self.scene_ns)
        
        # 注册条件处理器
        self._register_condition_handlers()
        
        # 执行条件图式
        result = await schema_executor.execute(self.condition_schema, execution_context)
        
        if result.success:
            self.logger.info("Condition execution completed successfully")
            self._process_successful_condition(result)
        else:
            self.logger.error(f"Condition execution failed: {result.error}")
            self._process_failed_condition(result)
        
        return result
    
    def _register_condition_handlers(self):
        """注册条件处理器"""
        from linars.edu.memphis.ccrg.lida.Nlanguage.SchemaExecutor import ActionHandler
        
        class ConditionEvaluationHandler(ActionHandler):
            def __init__(self, task_instance):
                self.task = task_instance
            
            async def execute(self, action_params: Dict[str, Any], context: ExecutionContext) -> Any:
                branches = action_params.get('branches', [])
                else_branches = action_params.get('else_branches', [])
                
                # 评估条件分支
                return await self.task._evaluate_condition_branches(branches, else_branches, context)
            
            def validate_params(self, action_params: Dict[str, Any]) -> bool:
                return 'branches' in action_params
        
        class BranchExecutionHandler(ActionHandler):
            def __init__(self, task_instance):
                self.task = task_instance
            
            async def execute(self, action_params: Dict[str, Any], context: ExecutionContext) -> Any:
                branch = action_params.get('branch')
                pam = action_params.get('pam')
                act_stamp = action_params.get('act_stamp')
                
                # 执行分支
                return await self.task._execute_branch(branch, pam, act_stamp, context)
            
            def validate_params(self, action_params: Dict[str, Any]) -> bool:
                return 'branch' in action_params
        
        class DefaultConditionHandler(ActionHandler):
            def __init__(self, task_instance):
                self.task = task_instance
            
            async def execute(self, action_params: Dict[str, Any], context: ExecutionContext) -> Any:
                link = action_params.get('link')
                pam = action_params.get('pam')
                act_stamp = action_params.get('act_stamp')
                
                # 执行默认条件逻辑
                return await self.task._execute_default_condition(link, pam, act_stamp, context)
            
            def validate_params(self, action_params: Dict[str, Any]) -> bool:
                return 'link' in action_params and 'pam' in action_params
        
        # 注册处理器
        schema_executor.register_action_handler('evaluate_condition', ConditionEvaluationHandler(self))
        schema_executor.register_action_handler('execute_branch', BranchExecutionHandler(self))
        schema_executor.register_action_handler('default_condition', DefaultConditionHandler(self))
    
    async def _evaluate_condition_branches(self, branches: List[Dict], 
                                         else_branches: List[Dict], 
                                         context: ExecutionContext) -> Any:
        """评估条件分支"""
        self.logger.debug(f"Evaluating {len(branches)} condition branches")
        
        # 使用分支预测
        if self.config.get('enable_branch_prediction', True):
            predicted_branch = self._predict_best_branch(branches)
            if predicted_branch:
                self.logger.debug(f"Predicted best branch: {predicted_branch['branch_id']}")
                branches = [predicted_branch] + [b for b in branches if b != predicted_branch]
        
        # 评估每个分支
        for branch in branches:
            try:
                evaluation_result = await self._evaluate_single_branch(branch, context)
                branch['evaluation_count'] += 1
                
                if evaluation_result.get('condition_met', False):
                    branch['success_count'] += 1
                    self.selected_branch = branch
                    self.logger.info(f"Selected branch {branch['branch_id']} based on condition evaluation")
                    return {'selected_branch': branch, 'condition_met': True}
                    
            except Exception as e:
                self.logger.warning(f"Branch {branch['branch_id']} evaluation failed: {e}")
                continue
        
        # 如果没有条件满足，选择else分支
        if else_branches:
            self.selected_branch = else_branches[0]  # 选择第一个else分支
            self.logger.info(f"Selected else branch {self.selected_branch['branch_id']}")
            return {'selected_branch': self.selected_branch, 'condition_met': False}
        
        # 如果没有else分支，根据配置选择默认分支
        default_branch_strategy = self.config.get('default_branch', 'first')
        if default_branch_strategy == 'first' and branches:
            self.selected_branch = branches[0]
        elif default_branch_strategy == 'highest_priority' and branches:
            self.selected_branch = max(branches, key=lambda x: x['priority'])
        
        return {'selected_branch': self.selected_branch, 'condition_met': False, 'default_selected': True}
    
    async def _evaluate_single_branch(self, branch: Dict, context: ExecutionContext) -> Dict[str, Any]:
        """评估单个分支"""
        condition_link = branch.get('condition_link')
        condition_node = branch.get('condition_node')
        
        if not condition_link or not condition_node:
            return {'condition_met': False, 'reason': 'No condition link or node'}
        
        # 这里可以实现具体的条件评估逻辑
        # 目前返回基于激活度的简单评估
        try:
            activation = getattr(condition_node, 'get_activation', lambda: 0.5)()
            threshold = 0.6  # 激活阈值
            
            condition_met = activation > threshold
            
            return {
                'condition_met': condition_met,
                'activation': activation,
                'threshold': threshold,
                'reason': f'Activation {activation} {">" if condition_met else "<="} threshold {threshold}'
            }
            
        except Exception as e:
            return {'condition_met': False, 'reason': f'Evaluation error: {str(e)}'}
    
    def _predict_best_branch(self, branches: List[Dict]) -> Optional[Dict]:
        """预测最佳分支"""
        if not branches:
            return None
        
        # 基于历史成功率和优先级预测
        best_branch = None
        best_score = -1
        
        for branch in branches:
            # 计算预测分数
            priority_score = branch.get('priority', 0.0) * 0.6
            
            # 历史成功率
            success_rate = 0.0
            if branch['evaluation_count'] > 0:
                success_rate = branch['success_count'] / branch['evaluation_count']
            
            history_score = success_rate * 0.4
            
            total_score = priority_score + history_score
            
            if total_score > best_score:
                best_score = total_score
                best_branch = branch
        
        return best_branch
    
    async def _execute_branch(self, branch: Dict, pam: PAMemory, 
                            act_stamp: Optional[str], context: ExecutionContext) -> Any:
        """执行分支"""
        branch_id = branch.get('branch_id')
        condition_link = branch.get('condition_link')
        
        self.logger.debug(f"Executing branch {branch_id}")
        
        try:
            if condition_link:
                # 处理条件链接
                condition_node = condition_link.get_sink()
                
                # 接收感知
                pam.get_listener().receive_percept(condition_node, ModuleName.SeqGraph)
                pam.get_listener().receive_percept(condition_link, ModuleName.SeqGraph)
                
                # 继续执行
                pam.get_act_root(condition_link, False, False, act_stamp)
                
                return {
                    'branch_id': branch_id,
                    'success': True,
                    'executed_link': condition_link
                }
            else:
                return {
                    'branch_id': branch_id,
                    'success': True,
                    'message': 'Default branch execution'
                }
                
        except Exception as e:
            self.logger.error(f"Branch {branch_id} execution failed: {e}")
            return {
                'branch_id': branch_id,
                'success': False,
                'error': str(e)
            }
    
    async def _execute_default_condition(self, link: Link, pam: PAMemory, 
                                       act_stamp: Optional[str], context: ExecutionContext) -> Any:
        """执行默认条件逻辑"""
        self.logger.info("Executing default condition logic")
        
        try:
            # 简单的默认处理
            if link:
                sink_node = link.get_sink()
                pam.get_listener().receive_percept(sink_node, ModuleName.SeqGraph)
                pam.get_listener().receive_percept(link, ModuleName.SeqGraph)
            
            return {
                'success': True,
                'message': 'Default condition executed',
                'link': link
            }
            
        except Exception as e:
            self.logger.error(f"Default condition execution failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _process_successful_condition(self, result: Any):
        """处理成功的条件执行"""
        self.execution_result = result
        
        # 更新分支缓存
        if self.selected_branch:
            branch_id = str(self.selected_branch.get('branch_id', ''))
            if branch_id not in self.branch_cache:
                self.branch_cache[branch_id] = {'total_count': 0, 'success_count': 0}
            
            self.branch_cache[branch_id]['total_count'] += 1
            self.branch_cache[branch_id]['success_count'] += 1
    
    def _process_failed_condition(self, result: Any):
        """处理失败的条件执行"""
        self.execution_result = result
        self.error_info = str(result.error) if hasattr(result, 'error') else "Unknown error"
        
        # 更新分支缓存
        if self.selected_branch:
            branch_id = str(self.selected_branch.get('branch_id', ''))
            if branch_id not in self.branch_cache:
                self.branch_cache[branch_id] = {'total_count': 0, 'success_count': 0}
            
            self.branch_cache[branch_id]['total_count'] += 1
    
    def get_condition_stats(self) -> Dict[str, Any]:
        """获取条件统计信息"""
        return {
            'task_id': self.task_id,
            'condition_branches_count': len(self.condition_branches),
            'else_branches_count': len(self.else_branches),
            'selected_branch': self.selected_branch.get('branch_id') if self.selected_branch else None,
            'branch_cache_size': len(self.branch_cache),
            'evaluation_history_size': len(self.evaluation_history),
            'status': self.status.value
        }
