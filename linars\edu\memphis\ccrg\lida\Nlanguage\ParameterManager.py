"""
参数传递管理器 - 处理复杂的参数传递和上下文管理
"""

import logging
import time
import uuid
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Callable, Union, Set, Tuple
from enum import Enum
import copy
import threading

from .VariableBindingSystem import (
    VariableBindingSystem, Variable, Scope, VariableType, 
    AccessMode, ScopeType, variable_binding_system
)


class ParameterPassingMode(Enum):
    """参数传递模式"""
    BY_VALUE = "by_value"           # 按值传递
    BY_REFERENCE = "by_reference"   # 按引用传递
    BY_NAME = "by_name"             # 按名传递
    BY_NEED = "by_need"             # 按需传递（惰性求值）
    COPY_IN_COPY_OUT = "copy_in_copy_out"  # 复制进复制出


class ParameterDirection(Enum):
    """参数方向"""
    INPUT = "input"         # 输入参数
    OUTPUT = "output"       # 输出参数
    INPUT_OUTPUT = "input_output"  # 输入输出参数
    RETURN = "return"       # 返回值


@dataclass
class ParameterDefinition:
    """参数定义"""
    name: str
    parameter_type: VariableType
    direction: ParameterDirection = ParameterDirection.INPUT
    passing_mode: ParameterPassingMode = ParameterPassingMode.BY_VALUE
    default_value: Any = None
    is_required: bool = True
    is_variadic: bool = False  # 可变参数
    constraints: Dict[str, Any] = field(default_factory=dict)
    description: str = ""
    
    def validate_value(self, value: Any) -> bool:
        """验证参数值"""
        # 基本类型检查
        if self.is_required and value is None:
            return False
        
        # 约束检查
        for constraint_name, constraint_value in self.constraints.items():
            if not self._check_constraint(constraint_name, constraint_value, value):
                return False
        
        return True
    
    def _check_constraint(self, constraint_name: str, constraint_value: Any, value: Any) -> bool:
        """检查单个约束"""
        try:
            if constraint_name == "min_value" and isinstance(value, (int, float)):
                return value >= constraint_value
            elif constraint_name == "max_value" and isinstance(value, (int, float)):
                return value <= constraint_value
            elif constraint_name == "min_length" and hasattr(value, '__len__'):
                return len(value) >= constraint_value
            elif constraint_name == "max_length" and hasattr(value, '__len__'):
                return len(value) <= constraint_value
            elif constraint_name == "allowed_values":
                return value in constraint_value
            elif constraint_name == "pattern" and isinstance(value, str):
                import re
                return bool(re.match(constraint_value, value))
            else:
                return True
        except Exception:
            return False


@dataclass
class ParameterBinding:
    """参数绑定"""
    definition: ParameterDefinition
    value: Any
    source_variable: Optional[Variable] = None
    target_variable: Optional[Variable] = None
    binding_time: float = field(default_factory=time.time)
    is_bound: bool = False
    
    def bind(self, value: Any, source_variable: Optional[Variable] = None):
        """绑定参数"""
        if not self.definition.validate_value(value):
            raise ValueError(f"Invalid value for parameter '{self.definition.name}'")
        
        if self.definition.passing_mode == ParameterPassingMode.BY_VALUE:
            self.value = copy.deepcopy(value)
        elif self.definition.passing_mode == ParameterPassingMode.BY_REFERENCE:
            self.value = value
        elif self.definition.passing_mode == ParameterPassingMode.BY_NAME:
            # 按名传递：存储表达式，延迟求值
            self.value = value
        elif self.definition.passing_mode == ParameterPassingMode.BY_NEED:
            # 按需传递：惰性求值
            self.value = lambda: value if callable(value) else value
        elif self.definition.passing_mode == ParameterPassingMode.COPY_IN_COPY_OUT:
            self.value = copy.deepcopy(value)
        
        self.source_variable = source_variable
        self.binding_time = time.time()
        self.is_bound = True
    
    def get_value(self) -> Any:
        """获取参数值"""
        if not self.is_bound:
            if self.definition.default_value is not None:
                return self.definition.default_value
            elif not self.definition.is_required:
                return None
            else:
                raise ValueError(f"Required parameter '{self.definition.name}' is not bound")
        
        if self.definition.passing_mode == ParameterPassingMode.BY_NEED:
            # 惰性求值
            if callable(self.value):
                return self.value()
            else:
                return self.value
        else:
            return self.value
    
    def copy_out(self):
        """复制输出（用于COPY_IN_COPY_OUT模式）"""
        if (self.definition.passing_mode == ParameterPassingMode.COPY_IN_COPY_OUT and
            self.definition.direction in [ParameterDirection.OUTPUT, ParameterDirection.INPUT_OUTPUT] and
            self.source_variable):
            self.source_variable.set_value(copy.deepcopy(self.value))


class ParameterContext:
    """参数上下文"""
    
    def __init__(self, context_id: str, parent_context: Optional['ParameterContext'] = None):
        self.context_id = context_id
        self.parent_context = parent_context
        self.child_contexts: List['ParameterContext'] = []
        self.parameter_bindings: Dict[str, ParameterBinding] = {}
        self.return_values: Dict[str, Any] = {}
        self.created_time = time.time()
        self.is_active = True
        
        if parent_context:
            parent_context.child_contexts.append(self)
    
    def bind_parameter(self, definition: ParameterDefinition, value: Any, 
                      source_variable: Optional[Variable] = None) -> ParameterBinding:
        """绑定参数"""
        binding = ParameterBinding(definition)
        binding.bind(value, source_variable)
        self.parameter_bindings[definition.name] = binding
        return binding
    
    def get_parameter_value(self, name: str) -> Any:
        """获取参数值"""
        if name in self.parameter_bindings:
            return self.parameter_bindings[name].get_value()
        elif self.parent_context:
            return self.parent_context.get_parameter_value(name)
        else:
            raise KeyError(f"Parameter '{name}' not found")
    
    def set_return_value(self, name: str, value: Any):
        """设置返回值"""
        self.return_values[name] = value
    
    def get_return_value(self, name: str = "default") -> Any:
        """获取返回值"""
        return self.return_values.get(name)
    
    def copy_out_parameters(self):
        """复制输出参数"""
        for binding in self.parameter_bindings.values():
            binding.copy_out()
    
    def cleanup(self):
        """清理上下文"""
        self.is_active = False
        self.parameter_bindings.clear()
        self.return_values.clear()
        
        if self.parent_context and self in self.parent_context.child_contexts:
            self.parent_context.child_contexts.remove(self)


class ParameterManager:
    """参数管理器"""
    
    def __init__(self, variable_binding_system: VariableBindingSystem):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.variable_system = variable_binding_system
        
        # 上下文管理
        self.contexts: Dict[str, ParameterContext] = {}
        self.current_context: Optional[ParameterContext] = None
        self.context_stack: List[ParameterContext] = []
        
        # 参数定义注册表
        self.parameter_definitions: Dict[str, Dict[str, ParameterDefinition]] = {}
        
        # 类型转换器
        self.type_converters: Dict[Tuple[type, type], Callable] = {}
        
        # 统计信息
        self.stats = {
            'parameter_bindings': 0,
            'type_conversions': 0,
            'context_creations': 0,
            'validation_failures': 0
        }
        
        # 线程安全
        self.lock = threading.RLock()
        
        # 注册默认类型转换器
        self._register_default_converters()
    
    def register_parameter_definition(self, function_name: str, parameter_def: ParameterDefinition):
        """注册参数定义"""
        with self.lock:
            if function_name not in self.parameter_definitions:
                self.parameter_definitions[function_name] = {}
            
            self.parameter_definitions[function_name][parameter_def.name] = parameter_def
            self.logger.debug(f"Registered parameter '{parameter_def.name}' for function '{function_name}'")
    
    def register_function_parameters(self, function_name: str, parameters: List[ParameterDefinition]):
        """批量注册函数参数"""
        with self.lock:
            for param_def in parameters:
                self.register_parameter_definition(function_name, param_def)
    
    def create_parameter_context(self, context_id: str = None, 
                                parent_context: Optional[ParameterContext] = None) -> ParameterContext:
        """创建参数上下文"""
        with self.lock:
            if context_id is None:
                context_id = str(uuid.uuid4())
            
            if context_id in self.contexts:
                raise ValueError(f"Parameter context '{context_id}' already exists")
            
            if parent_context is None:
                parent_context = self.current_context
            
            context = ParameterContext(context_id, parent_context)
            self.contexts[context_id] = context
            self.stats['context_creations'] += 1
            
            self.logger.debug(f"Created parameter context '{context_id}'")
            return context
    
    def enter_context(self, context: Union[ParameterContext, str]):
        """进入参数上下文"""
        with self.lock:
            if isinstance(context, str):
                context = self.contexts.get(context)
                if not context:
                    raise ValueError(f"Parameter context '{context}' not found")
            
            self.context_stack.append(self.current_context)
            self.current_context = context
            
            self.logger.debug(f"Entered parameter context '{context.context_id}'")
    
    def exit_context(self) -> Optional[ParameterContext]:
        """退出参数上下文"""
        with self.lock:
            if not self.context_stack:
                self.logger.warning("No parameter context to exit")
                return None
            
            previous_context = self.current_context
            self.current_context = self.context_stack.pop()
            
            # 复制输出参数
            if previous_context:
                previous_context.copy_out_parameters()
            
            self.logger.debug(f"Exited parameter context '{previous_context.context_id if previous_context else None}'")
            return previous_context
    
    def bind_parameters(self, function_name: str, arguments: Dict[str, Any], 
                       context: Optional[ParameterContext] = None) -> Dict[str, ParameterBinding]:
        """绑定函数参数"""
        with self.lock:
            target_context = context or self.current_context
            if not target_context:
                raise ValueError("No active parameter context")
            
            if function_name not in self.parameter_definitions:
                raise ValueError(f"No parameter definitions found for function '{function_name}'")
            
            param_defs = self.parameter_definitions[function_name]
            bindings = {}
            
            # 绑定提供的参数
            for param_name, param_def in param_defs.items():
                if param_name in arguments:
                    value = arguments[param_name]
                    
                    # 类型转换
                    converted_value = self._convert_type(value, param_def.parameter_type)
                    
                    # 获取源变量
                    source_variable = None
                    if isinstance(value, str) and value.startswith('$'):
                        var_name = value[1:]
                        source_variable = self.variable_system.get_variable(var_name)
                        if source_variable:
                            converted_value = source_variable.get_value()
                    
                    try:
                        binding = target_context.bind_parameter(param_def, converted_value, source_variable)
                        bindings[param_name] = binding
                        self.stats['parameter_bindings'] += 1
                    except ValueError as e:
                        self.stats['validation_failures'] += 1
                        self.logger.error(f"Failed to bind parameter '{param_name}': {e}")
                        raise e
                
                elif param_def.is_required and param_def.default_value is None:
                    raise ValueError(f"Required parameter '{param_name}' not provided")
            
            self.logger.debug(f"Bound {len(bindings)} parameters for function '{function_name}'")
            return bindings
    
    def get_parameter_value(self, name: str, context: Optional[ParameterContext] = None) -> Any:
        """获取参数值"""
        with self.lock:
            target_context = context or self.current_context
            if not target_context:
                raise ValueError("No active parameter context")
            
            return target_context.get_parameter_value(name)
    
    def set_return_value(self, value: Any, name: str = "default", 
                        context: Optional[ParameterContext] = None):
        """设置返回值"""
        with self.lock:
            target_context = context or self.current_context
            if not target_context:
                raise ValueError("No active parameter context")
            
            target_context.set_return_value(name, value)
    
    def get_return_value(self, name: str = "default", 
                        context: Optional[ParameterContext] = None) -> Any:
        """获取返回值"""
        with self.lock:
            target_context = context or self.current_context
            if not target_context:
                return None
            
            return target_context.get_return_value(name)
    
    def register_type_converter(self, from_type: type, to_type: type, converter: Callable):
        """注册类型转换器"""
        self.type_converters[(from_type, to_type)] = converter
        self.logger.debug(f"Registered type converter from {from_type.__name__} to {to_type.__name__}")
    
    def _convert_type(self, value: Any, target_type: VariableType) -> Any:
        """类型转换"""
        if target_type == VariableType.PRIMITIVE:
            return value  # 基本类型不需要转换
        
        source_type = type(value)
        
        # 查找类型转换器
        for (from_type, to_type), converter in self.type_converters.items():
            if isinstance(value, from_type) and to_type.__name__.lower() == target_type.value:
                try:
                    converted_value = converter(value)
                    self.stats['type_conversions'] += 1
                    return converted_value
                except Exception as e:
                    self.logger.warning(f"Type conversion failed: {e}")
        
        return value  # 如果没有找到转换器，返回原值
    
    def _register_default_converters(self):
        """注册默认类型转换器"""
        # 字符串转换
        self.register_type_converter(str, int, lambda x: int(x) if x.isdigit() else 0)
        self.register_type_converter(str, float, lambda x: float(x) if x.replace('.', '').isdigit() else 0.0)
        self.register_type_converter(str, bool, lambda x: x.lower() in ['true', '1', 'yes', 'on'])
        
        # 数字转换
        self.register_type_converter(int, str, str)
        self.register_type_converter(float, str, str)
        self.register_type_converter(bool, str, str)
        
        # 列表转换
        self.register_type_converter(str, list, lambda x: x.split(',') if ',' in x else [x])
        self.register_type_converter(list, str, lambda x: ','.join(map(str, x)))
    
    def create_parameter_scope(self, function_name: str, arguments: Dict[str, Any]) -> Scope:
        """为函数调用创建参数作用域"""
        with self.lock:
            # 创建函数作用域
            scope_id = f"function_{function_name}_{uuid.uuid4().hex[:8]}"
            scope = self.variable_system.create_scope(scope_id, ScopeType.FUNCTION)
            
            # 绑定参数到作用域
            if function_name in self.parameter_definitions:
                param_defs = self.parameter_definitions[function_name]
                
                for param_name, param_def in param_defs.items():
                    if param_name in arguments:
                        value = arguments[param_name]
                        
                        # 类型转换
                        converted_value = self._convert_type(value, param_def.parameter_type)
                        
                        # 定义变量
                        access_mode = AccessMode.READ_ONLY if param_def.direction == ParameterDirection.INPUT else AccessMode.READ_WRITE
                        scope.define_variable(param_name, converted_value, param_def.parameter_type, access_mode)
                    
                    elif param_def.default_value is not None:
                        # 使用默认值
                        access_mode = AccessMode.READ_ONLY if param_def.direction == ParameterDirection.INPUT else AccessMode.READ_WRITE
                        scope.define_variable(param_name, param_def.default_value, param_def.parameter_type, access_mode)
            
            return scope
    
    def cleanup_context(self, context_id: str):
        """清理参数上下文"""
        with self.lock:
            if context_id not in self.contexts:
                return
            
            context = self.contexts[context_id]
            context.cleanup()
            del self.contexts[context_id]
            
            self.logger.debug(f"Cleaned up parameter context '{context_id}'")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.lock:
            stats = self.stats.copy()
            stats.update({
                'active_contexts': len(self.contexts),
                'registered_functions': len(self.parameter_definitions),
                'registered_converters': len(self.type_converters),
                'current_context': self.current_context.context_id if self.current_context else None,
                'context_stack_depth': len(self.context_stack)
            })
            return stats


# 全局参数管理器实例
parameter_manager = ParameterManager(variable_binding_system)
