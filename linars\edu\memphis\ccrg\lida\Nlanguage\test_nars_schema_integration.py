"""
NARS-图式集成系统测试文件
"""

import asyncio
import logging
import time
from unittest.mock import Mock, MagicMock
from typing import Dict, Any, List

from .NarsSchemaIntegration import (
    NarsReasoningEngine, SchemaReasoningIntegrator, ReasoningRequest, 
    ReasoningResult, ReasoningType, IntegrationType, SchemaReasoningBinding,
    SchemaToReasoningConverter, ReasoningToSchemaConverter,
    nars_reasoning_engine, schema_reasoning_integrator
)
from .TreeChart import TreeChart, SchemaType, ExecutionMode
from .SchemaExecutor import ExecutionContext, ExecutionResult
from .SchemaFactory import schema_factory

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_mock_nar():
    """创建模拟NARS实例"""
    nar = Mock()
    nar.memory = Mock()
    nar.narsese = Mock()
    nar.narsese.parse_term = Mock(return_value="parsed_term")
    return nar


async def test_nars_reasoning_engine():
    """测试NARS推理引擎"""
    print("\n=== 测试NARS推理引擎 ===")
    
    # 创建推理引擎
    mock_nar = create_mock_nar()
    engine = NarsReasoningEngine(mock_nar)
    
    results = []
    
    # 测试演绎推理
    print("1. 测试演绎推理")
    try:
        deduction_request = ReasoningRequest(
            request_id="deduction_test",
            reasoning_type=ReasoningType.DEDUCTION,
            premises=["所有人都会死", "苏格拉底是人"],
            goals=["苏格拉底会死"],
            confidence_threshold=0.6
        )
        
        deduction_result = await engine.perform_reasoning(deduction_request)
        
        print(f"推理请求ID: {deduction_result.request_id}")
        print(f"结论数量: {len(deduction_result.conclusions)}")
        print(f"置信度: {deduction_result.confidence}")
        print(f"执行时间: {deduction_result.execution_time:.4f}s")
        print(f"推理路径长度: {len(deduction_result.reasoning_path)}")
        
        results.append(len(deduction_result.conclusions) > 0)
        
    except Exception as e:
        logger.error(f"Deduction test failed: {e}")
        results.append(False)
    
    # 测试归纳推理
    print("\n2. 测试归纳推理")
    try:
        induction_request = ReasoningRequest(
            request_id="induction_test",
            reasoning_type=ReasoningType.INDUCTION,
            premises=["天鹅1是白色的", "天鹅2是白色的", "天鹅3是白色的"],
            goals=["所有天鹅都是白色的"],
            confidence_threshold=0.5
        )
        
        induction_result = await engine.perform_reasoning(induction_request)
        
        print(f"推理请求ID: {induction_result.request_id}")
        print(f"结论数量: {len(induction_result.conclusions)}")
        print(f"置信度: {induction_result.confidence}")
        print(f"执行时间: {induction_result.execution_time:.4f}s")
        
        results.append(len(induction_result.conclusions) > 0)
        
    except Exception as e:
        logger.error(f"Induction test failed: {e}")
        results.append(False)
    
    # 测试溯因推理
    print("\n3. 测试溯因推理")
    try:
        abduction_request = ReasoningRequest(
            request_id="abduction_test",
            reasoning_type=ReasoningType.ABDUCTION,
            premises=["草地是湿的"],
            goals=["找到草地湿的原因"],
            confidence_threshold=0.4
        )
        
        abduction_result = await engine.perform_reasoning(abduction_request)
        
        print(f"推理请求ID: {abduction_result.request_id}")
        print(f"结论数量: {len(abduction_result.conclusions)}")
        print(f"置信度: {abduction_result.confidence}")
        print(f"执行时间: {abduction_result.execution_time:.4f}s")
        
        results.append(len(abduction_result.conclusions) > 0)
        
    except Exception as e:
        logger.error(f"Abduction test failed: {e}")
        results.append(False)
    
    # 测试时序推理
    print("\n4. 测试时序推理")
    try:
        temporal_request = ReasoningRequest(
            request_id="temporal_test",
            reasoning_type=ReasoningType.TEMPORAL,
            premises=["事件A发生", "事件B发生", "事件C发生"],
            goals=["预测下一个事件"],
            confidence_threshold=0.5
        )
        
        temporal_result = await engine.perform_reasoning(temporal_request)
        
        print(f"推理请求ID: {temporal_result.request_id}")
        print(f"结论数量: {len(temporal_result.conclusions)}")
        print(f"置信度: {temporal_result.confidence}")
        print(f"执行时间: {temporal_result.execution_time:.4f}s")
        
        results.append(len(temporal_result.conclusions) > 0)
        
    except Exception as e:
        logger.error(f"Temporal test failed: {e}")
        results.append(False)
    
    # 获取统计信息
    stats = engine.get_statistics()
    print(f"\n5. 推理引擎统计信息:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    return all(results)


async def test_schema_reasoning_integrator():
    """测试图式推理集成器"""
    print("\n=== 测试图式推理集成器 ===")
    
    # 创建集成器
    mock_nar = create_mock_nar()
    engine = NarsReasoningEngine(mock_nar)
    integrator = SchemaReasoningIntegrator(engine)
    
    results = []
    
    # 测试基本集成
    print("1. 测试基本集成")
    try:
        # 创建测试图式
        test_schema = schema_factory.create_action_schema(
            action_type='test_action',
            action_params={'param1': 'value1', 'param2': 'value2'},
            schema_id='test_schema_1'
        )
        
        # 创建推理请求
        reasoning_request = ReasoningRequest(
            request_id="integration_test",
            reasoning_type=ReasoningType.DEDUCTION,
            premises=["前提1", "前提2"],
            goals=["目标1"],
            confidence_threshold=0.6
        )
        
        # 执行集成
        binding = await integrator.integrate_schema_with_reasoning(
            test_schema, reasoning_request, IntegrationType.BIDIRECTIONAL
        )
        
        print(f"绑定ID: {binding.binding_id}")
        print(f"集成类型: {binding.integration_type.value}")
        print(f"绑定强度: {binding.binding_strength}")
        print(f"推理结果: {binding.reasoning_result is not None}")
        
        if binding.reasoning_result:
            print(f"推理结论数量: {len(binding.reasoning_result.conclusions)}")
            print(f"推理置信度: {binding.reasoning_result.confidence}")
        
        results.append(binding.reasoning_result is not None)
        
    except Exception as e:
        logger.error(f"Basic integration test failed: {e}")
        results.append(False)
    
    # 测试推理驱动的图式执行
    print("\n2. 测试推理驱动的图式执行")
    try:
        # 创建推理结果
        mock_reasoning_result = ReasoningResult(
            result_id="mock_result",
            request_id="mock_request",
            conclusions=["结论1", "结论2", "结论3"],
            reasoning_path=[
                {"step": "premises", "content": ["前提1", "前提2"]},
                {"step": "reasoning", "content": "应用推理规则"},
                {"step": "conclusions", "content": ["结论1", "结论2", "结论3"]}
            ],
            confidence=0.8,
            execution_time=0.1
        )
        
        # 执行推理驱动的图式
        execution_result = await integrator.execute_reasoning_driven_schema(mock_reasoning_result)
        
        print(f"执行成功: {execution_result.success}")
        print(f"执行时间: {execution_result.execution_time:.4f}s")
        if execution_result.error:
            print(f"错误信息: {execution_result.error}")
        
        results.append(execution_result.success or execution_result.error is not None)
        
    except Exception as e:
        logger.error(f"Reasoning-driven execution test failed: {e}")
        results.append(False)
    
    # 测试图式驱动的推理
    print("\n3. 测试图式驱动的推理")
    try:
        # 创建测试图式
        test_schema = schema_factory.create_condition_schema(
            condition=schema_factory.create_action_schema(
                action_type='check_condition',
                action_params={'condition': 'test_condition'}
            ),
            then_branch=schema_factory.create_action_schema(
                action_type='then_action',
                action_params={'action': 'positive_action'}
            ),
            else_branch=schema_factory.create_action_schema(
                action_type='else_action',
                action_params={'action': 'negative_action'}
            ),
            schema_id='condition_schema'
        )
        
        # 创建执行结果
        mock_execution_result = ExecutionResult(
            success=True,
            result={'output': 'test_output', 'status': 'completed'},
            error=None,
            execution_time=0.2,
            metadata={'schema_type': 'condition'}
        )
        
        # 执行图式驱动的推理
        reasoning_result = await integrator.perform_schema_driven_reasoning(test_schema, mock_execution_result)
        
        print(f"推理成功: {len(reasoning_result.conclusions) > 0}")
        print(f"结论数量: {len(reasoning_result.conclusions)}")
        print(f"置信度: {reasoning_result.confidence}")
        print(f"执行时间: {reasoning_result.execution_time:.4f}s")
        
        results.append(len(reasoning_result.conclusions) > 0 or reasoning_result.confidence >= 0)
        
    except Exception as e:
        logger.error(f"Schema-driven reasoning test failed: {e}")
        results.append(False)
    
    # 测试不同集成类型
    print("\n4. 测试不同集成类型")
    try:
        integration_types = [
            IntegrationType.REASONING_TO_SCHEMA,
            IntegrationType.SCHEMA_TO_REASONING,
            IntegrationType.BIDIRECTIONAL,
            IntegrationType.FEEDBACK_LOOP
        ]
        
        type_results = []
        
        for integration_type in integration_types:
            test_schema = schema_factory.create_action_schema(
                action_type=f'test_{integration_type.value}',
                action_params={'type': integration_type.value},
                schema_id=f'schema_{integration_type.value}'
            )
            
            reasoning_request = ReasoningRequest(
                request_id=f"test_{integration_type.value}",
                reasoning_type=ReasoningType.DEDUCTION,
                premises=[f"前提_{integration_type.value}"],
                goals=[f"目标_{integration_type.value}"]
            )
            
            try:
                binding = await integrator.integrate_schema_with_reasoning(
                    test_schema, reasoning_request, integration_type
                )
                
                print(f"  {integration_type.value}: 绑定强度 {binding.binding_strength:.2f}")
                type_results.append(True)
                
            except Exception as e:
                print(f"  {integration_type.value}: 失败 - {e}")
                type_results.append(False)
        
        results.append(any(type_results))
        
    except Exception as e:
        logger.error(f"Integration types test failed: {e}")
        results.append(False)
    
    # 获取统计信息
    stats = integrator.get_statistics()
    print(f"\n5. 集成器统计信息:")
    for key, value in stats.items():
        if key != 'reasoning_engine_stats':
            print(f"  {key}: {value}")
    
    return all(results)


async def test_converters():
    """测试转换器"""
    print("\n=== 测试转换器 ===")
    
    results = []
    
    # 测试图式到推理转换器
    print("1. 测试图式到推理转换器")
    try:
        converter = SchemaToReasoningConverter()
        
        # 创建测试图式
        test_schema = schema_factory.create_sequence_schema(
            steps=[
                schema_factory.create_action_schema(
                    action_type='step1',
                    action_params={'param': 'value1'}
                ),
                schema_factory.create_action_schema(
                    action_type='step2',
                    action_params={'param': 'value2'}
                )
            ],
            schema_id='sequence_schema'
        )
        
        # 创建执行结果
        execution_result = ExecutionResult(
            success=True,
            result={'final_output': 'success'},
            error=None,
            execution_time=0.3
        )
        
        # 转换为推理请求
        reasoning_request = await converter.convert_schema_to_reasoning_request(test_schema, execution_result)
        
        print(f"推理请求ID: {reasoning_request.request_id}")
        print(f"推理类型: {reasoning_request.reasoning_type.value}")
        print(f"前提数量: {len(reasoning_request.premises)}")
        print(f"目标数量: {len(reasoning_request.goals)}")
        
        # 提取推理线索
        clues = await converter.extract_reasoning_clues(test_schema)
        print(f"推理线索数量: {len(clues)}")
        
        results.append(len(reasoning_request.premises) > 0)
        
    except Exception as e:
        logger.error(f"Schema to reasoning converter test failed: {e}")
        results.append(False)
    
    # 测试推理到图式转换器
    print("\n2. 测试推理到图式转换器")
    try:
        converter = ReasoningToSchemaConverter()
        
        # 创建推理结果
        reasoning_result = ReasoningResult(
            result_id="converter_test",
            request_id="converter_request",
            conclusions=[
                {"type": "conclusion", "content": "结论1", "confidence": 0.8},
                {"type": "conclusion", "content": "结论2", "confidence": 0.7},
                {"type": "conclusion", "content": "结论3", "confidence": 0.6}
            ],
            reasoning_path=[
                {"step": "analysis", "content": "分析前提"},
                {"step": "inference", "content": "应用推理规则"},
                {"step": "conclusion", "content": "得出结论"}
            ],
            confidence=0.75,
            execution_time=0.15
        )
        
        # 转换为图式
        converted_schema = await converter.convert_reasoning_to_schema(reasoning_result)
        
        print(f"转换后图式ID: {converted_schema.chart_id}")
        print(f"图式类型: {converted_schema.schema_type.value if hasattr(converted_schema, 'schema_type') else 'unknown'}")
        
        # 测试图式增强
        base_schema = schema_factory.create_action_schema(
            action_type='base_action',
            action_params={'base_param': 'base_value'},
            schema_id='base_schema'
        )
        
        enhanced_schema = await converter.enhance_schema_with_reasoning(base_schema, reasoning_result)
        
        print(f"增强后图式ID: {enhanced_schema.chart_id}")
        
        results.append(converted_schema is not None and enhanced_schema is not None)
        
    except Exception as e:
        logger.error(f"Reasoning to schema converter test failed: {e}")
        results.append(False)
    
    return all(results)


async def test_integration_scenarios():
    """测试集成场景"""
    print("\n=== 测试集成场景 ===")
    
    try:
        # 创建完整的集成环境
        mock_nar = create_mock_nar()
        engine = NarsReasoningEngine(mock_nar)
        integrator = SchemaReasoningIntegrator(engine)
        
        print("1. 测试复杂推理-图式集成场景")
        
        # 创建复杂的推理请求
        complex_reasoning_request = ReasoningRequest(
            request_id="complex_scenario",
            reasoning_type=ReasoningType.DEDUCTION,
            premises=[
                "如果天下雨，那么地面会湿",
                "如果地面湿，那么要小心行走",
                "现在天在下雨"
            ],
            goals=["确定是否需要小心行走"],
            confidence_threshold=0.7,
            max_steps=5
        )
        
        # 创建相应的图式
        weather_schema = schema_factory.create_condition_schema(
            condition=schema_factory.create_action_schema(
                action_type='check_weather',
                action_params={'condition': 'raining'}
            ),
            then_branch=schema_factory.create_sequence_schema(
                steps=[
                    schema_factory.create_action_schema(
                        action_type='observe_ground',
                        action_params={'check': 'wetness'}
                    ),
                    schema_factory.create_action_schema(
                        action_type='adjust_behavior',
                        action_params={'action': 'walk_carefully'}
                    )
                ]
            ),
            else_branch=schema_factory.create_action_schema(
                action_type='normal_walking',
                action_params={'action': 'walk_normally'}
            ),
            schema_id='weather_response_schema'
        )
        
        # 执行集成
        binding = await integrator.integrate_schema_with_reasoning(
            weather_schema, complex_reasoning_request, IntegrationType.FEEDBACK_LOOP
        )
        
        print(f"复杂场景绑定成功: {binding.reasoning_result is not None}")
        print(f"绑定强度: {binding.binding_strength:.2f}")
        
        if binding.reasoning_result:
            print(f"推理结论: {len(binding.reasoning_result.conclusions)}")
            print(f"推理置信度: {binding.reasoning_result.confidence:.2f}")
        
        print("\n2. 测试推理结果驱动的图式执行")
        
        # 使用推理结果执行图式
        if binding.reasoning_result:
            execution_result = await integrator.execute_reasoning_driven_schema(
                binding.reasoning_result, weather_schema
            )
            
            print(f"推理驱动执行成功: {execution_result.success}")
            print(f"执行时间: {execution_result.execution_time:.4f}s")
        
        print("\n3. 测试执行反馈推理")
        
        # 创建执行结果
        feedback_execution_result = ExecutionResult(
            success=True,
            result={
                'weather_checked': True,
                'ground_wet': True,
                'walking_adjusted': True
            },
            error=None,
            execution_time=0.25,
            metadata={'scenario': 'weather_response'}
        )
        
        # 基于执行结果进行推理
        feedback_reasoning = await integrator.perform_schema_driven_reasoning(
            weather_schema, feedback_execution_result
        )
        
        print(f"反馈推理成功: {len(feedback_reasoning.conclusions) > 0}")
        print(f"反馈推理结论数量: {len(feedback_reasoning.conclusions)}")
        print(f"反馈推理置信度: {feedback_reasoning.confidence:.2f}")
        
        # 获取最终统计
        final_stats = integrator.get_statistics()
        print(f"\n4. 最终统计信息:")
        for key, value in final_stats.items():
            if key != 'reasoning_engine_stats':
                print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        logger.error(f"Integration scenarios test failed: {e}")
        return False


async def main():
    """主测试函数"""
    print("开始NARS-图式集成系统测试...")
    
    tests = [
        ("NARS推理引擎", test_nars_reasoning_engine),
        ("图式推理集成器", test_schema_reasoning_integrator),
        ("转换器", test_converters),
        ("集成场景", test_integration_scenarios)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            print(f"运行测试: {test_name}")
            print(f"{'='*60}")
            
            start_time = time.time()
            result = await test_func()
            execution_time = time.time() - start_time
            
            results.append(result)
            status = "✓ 通过" if result else "✗ 失败"
            print(f"\n{test_name}: {status} (耗时: {execution_time:.4f}s)")
            
        except Exception as e:
            logger.error(f"测试 {test_name} 出现异常: {e}")
            results.append(False)
            print(f"\n{test_name}: ✗ 异常 (错误: {str(e)})")
    
    # 总结
    successful_tests = sum(results)
    total_tests = len(results)
    success_rate = successful_tests / total_tests * 100
    
    print(f"\n{'='*60}")
    print(f"测试总结")
    print(f"{'='*60}")
    print(f"成功测试: {successful_tests}/{total_tests}")
    print(f"成功率: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("🎉 测试整体通过！")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    return successful_tests == total_tests


if __name__ == "__main__":
    asyncio.run(main())
