"""
可执行图式系统综合测试运行入口
"""

import asyncio
import logging
import sys
import argparse
import time
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('schema_system_tests.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

# 导入测试框架
from .ComprehensiveTestFramework import comprehensive_test_suite, TestType, TestStatus


async def run_specific_test_suite(suite_name: str):
    """运行特定的测试套件"""
    logger.info(f"Running specific test suite: {suite_name}")
    
    try:
        report = await comprehensive_test_suite.test_runner.run_test_suite(suite_name)
        comprehensive_test_suite.test_runner.reporter.print_summary(report)
        
        # 保存报告
        timestamp = int(time.time())
        report_filename = f"test_report_{suite_name}_{timestamp}.json"
        comprehensive_test_suite.test_runner.reporter.save_report(report, report_filename)
        
        return report['summary']['success_rate'] >= 80
        
    except Exception as e:
        logger.error(f"Failed to run test suite {suite_name}: {e}")
        return False


async def run_all_tests():
    """运行所有测试"""
    logger.info("Starting comprehensive test execution...")
    
    try:
        reports = await comprehensive_test_suite.run_all_tests()
        
        # 保存综合报告
        timestamp = int(time.time())
        
        for i, report in enumerate(reports):
            suite_name = report['suite_info'].get('suite_id', f'suite_{i}')
            report_filename = f"test_report_{suite_name}_{timestamp}.json"
            comprehensive_test_suite.test_runner.reporter.save_report(report, report_filename)
        
        # 计算总体成功率
        total_tests = sum(r['summary']['total_tests'] for r in reports)
        total_passed = sum(r['summary']['passed'] for r in reports)
        overall_success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        logger.info(f"Overall test success rate: {overall_success_rate:.1f}%")
        
        return overall_success_rate >= 80
        
    except Exception as e:
        logger.error(f"Comprehensive test execution failed: {e}")
        return False


async def run_performance_tests():
    """运行性能测试"""
    logger.info("Running performance tests...")
    
    try:
        report = await comprehensive_test_suite.test_runner.run_test_suite("performance_tests")
        comprehensive_test_suite.test_runner.reporter.print_summary(report)
        
        # 分析性能测试结果
        performance_results = []
        for test_result in report['test_results']:
            if test_result['status'] == 'passed':
                performance_results.append({
                    'test_id': test_result['test_id'],
                    'execution_time': test_result['execution_time'],
                    'metrics': test_result.get('metrics', {})
                })
        
        print(f"\n性能测试详细结果:")
        for result in performance_results:
            print(f"  {result['test_id']}: {result['execution_time']:.4f}s")
        
        return len(performance_results) > 0
        
    except Exception as e:
        logger.error(f"Performance tests failed: {e}")
        return False


async def run_integration_tests():
    """运行集成测试"""
    logger.info("Running integration tests...")
    
    try:
        report = await comprehensive_test_suite.test_runner.run_test_suite("integration_tests")
        comprehensive_test_suite.test_runner.reporter.print_summary(report)
        
        return report['summary']['success_rate'] >= 70  # 集成测试要求稍低
        
    except Exception as e:
        logger.error(f"Integration tests failed: {e}")
        return False


async def run_unit_tests():
    """运行单元测试"""
    logger.info("Running unit tests...")
    
    try:
        report = await comprehensive_test_suite.test_runner.run_test_suite("core_functionality")
        comprehensive_test_suite.test_runner.reporter.print_summary(report)
        
        return report['summary']['success_rate'] >= 90  # 单元测试要求较高
        
    except Exception as e:
        logger.error(f"Unit tests failed: {e}")
        return False


def validate_system_requirements():
    """验证系统要求"""
    logger.info("Validating system requirements...")
    
    requirements_met = True
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        logger.error("Python 3.7+ is required")
        requirements_met = False
    else:
        logger.info(f"Python version: {sys.version}")
    
    # 检查必要的模块
    required_modules = [
        'asyncio', 'logging', 'json', 'uuid', 'threading',
        'collections', 'dataclasses', 'typing', 'enum'
    ]
    
    for module in required_modules:
        try:
            __import__(module)
            logger.debug(f"Module {module}: OK")
        except ImportError:
            logger.error(f"Required module {module} not found")
            requirements_met = False
    
    # 检查可选模块
    optional_modules = ['psutil', 'statistics']
    for module in optional_modules:
        try:
            __import__(module)
            logger.debug(f"Optional module {module}: Available")
        except ImportError:
            logger.warning(f"Optional module {module}: Not available")
    
    if requirements_met:
        logger.info("All system requirements met")
    else:
        logger.error("System requirements not met")
    
    return requirements_met


async def run_smoke_tests():
    """运行冒烟测试（快速验证基本功能）"""
    logger.info("Running smoke tests...")
    
    try:
        # 导入基本模块
        from .TreeChart import TreeChart, SchemaType
        from .SchemaFactory import schema_factory
        from .SchemaExecutor import ExecutionContext, schema_executor
        
        # 测试基本图式创建
        schema = schema_factory.create_action_schema(
            action_type='smoke_test',
            action_params={'test': 'smoke'},
            schema_id='smoke_test_schema'
        )
        
        if schema is None:
            logger.error("Smoke test failed: Schema creation failed")
            return False
        
        # 测试基本执行
        context = ExecutionContext(context_id="smoke_test")
        result = await schema_executor.execute(schema, context)
        
        if result is None:
            logger.error("Smoke test failed: Schema execution failed")
            return False
        
        logger.info("Smoke tests passed")
        return True
        
    except Exception as e:
        logger.error(f"Smoke tests failed: {e}")
        return False


def print_test_menu():
    """打印测试菜单"""
    print("\n" + "="*60)
    print("可执行图式系统测试菜单")
    print("="*60)
    print("1. 运行所有测试")
    print("2. 运行单元测试")
    print("3. 运行集成测试")
    print("4. 运行性能测试")
    print("5. 运行冒烟测试")
    print("6. 验证系统要求")
    print("7. 运行特定测试套件")
    print("0. 退出")
    print("="*60)


async def interactive_mode():
    """交互模式"""
    print("欢迎使用可执行图式系统测试框架！")
    
    while True:
        print_test_menu()
        
        try:
            choice = input("\n请选择操作 (0-7): ").strip()
            
            if choice == '0':
                print("退出测试框架")
                break
            elif choice == '1':
                success = await run_all_tests()
                print(f"\n所有测试 {'通过' if success else '失败'}")
            elif choice == '2':
                success = await run_unit_tests()
                print(f"\n单元测试 {'通过' if success else '失败'}")
            elif choice == '3':
                success = await run_integration_tests()
                print(f"\n集成测试 {'通过' if success else '失败'}")
            elif choice == '4':
                success = await run_performance_tests()
                print(f"\n性能测试 {'通过' if success else '失败'}")
            elif choice == '5':
                success = await run_smoke_tests()
                print(f"\n冒烟测试 {'通过' if success else '失败'}")
            elif choice == '6':
                success = validate_system_requirements()
                print(f"\n系统要求验证 {'通过' if success else '失败'}")
            elif choice == '7':
                print("\n可用的测试套件:")
                print("- core_functionality (核心功能)")
                print("- integration_tests (集成测试)")
                print("- performance_tests (性能测试)")
                
                suite_name = input("\n请输入测试套件名称: ").strip()
                if suite_name:
                    success = await run_specific_test_suite(suite_name)
                    print(f"\n测试套件 {suite_name} {'通过' if success else '失败'}")
                else:
                    print("无效的测试套件名称")
            else:
                print("无效的选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n\n用户中断，退出测试框架")
            break
        except Exception as e:
            logger.error(f"操作失败: {e}")
            print(f"操作失败: {e}")
        
        input("\n按回车键继续...")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="可执行图式系统综合测试")
    parser.add_argument('--mode', choices=['all', 'unit', 'integration', 'performance', 'smoke', 'interactive'],
                       default='interactive', help='测试模式')
    parser.add_argument('--suite', type=str, help='特定测试套件名称')
    parser.add_argument('--validate', action='store_true', help='验证系统要求')
    parser.add_argument('--quiet', action='store_true', help='静默模式')
    
    args = parser.parse_args()
    
    if args.quiet:
        logging.getLogger().setLevel(logging.WARNING)
    
    # 验证系统要求
    if args.validate or args.mode != 'interactive':
        if not validate_system_requirements():
            logger.error("System requirements not met, exiting")
            sys.exit(1)
    
    success = True
    
    try:
        if args.mode == 'interactive':
            await interactive_mode()
        elif args.mode == 'all':
            success = await run_all_tests()
        elif args.mode == 'unit':
            success = await run_unit_tests()
        elif args.mode == 'integration':
            success = await run_integration_tests()
        elif args.mode == 'performance':
            success = await run_performance_tests()
        elif args.mode == 'smoke':
            success = await run_smoke_tests()
        elif args.suite:
            success = await run_specific_test_suite(args.suite)
        
        if args.mode != 'interactive':
            sys.exit(0 if success else 1)
            
    except KeyboardInterrupt:
        logger.info("Test execution interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
