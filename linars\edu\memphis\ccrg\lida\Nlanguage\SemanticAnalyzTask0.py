#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
SemanticAnalyzTask0 class for the LIDA framework.

一维结构分析，如词序，分析结构为二维，即平面树状。
语义语法分析任务，将词句转化为语义树，多种方案：1、词序结构分离，2、词序结构合并
与pam区别：1、有预测。2、专注arg。3、构建树图。4、可有推理。
语法变量=关联问题和答案，通过蕴含等，蕴含有结构。语法成分可替换。原始词无结构=难推理
image和属性等，也可以是一种结构，如颜色，形状等。
预测有两种：意识级预测=通达注意=权重+情绪。激活级预测=结构
"""

import logging
import threading
from collections import defaultdict
from typing import Dict, List, Set, Any, Optional, Collection, Tuple

from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeImpl import NodeImpl
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.Nlanguage.ChartTreeSet import ChartTreeSet
from linars.edu.memphis.ccrg.lida.Nlanguage.TreeBag import TreeBag
from linars.edu.memphis.ccrg.lida.Nlanguage.TreeChart import TreeChart
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory
from linars.edu.memphis.ccrg.linars.term import Term


class SemanticAnalyzTask0(FrameworkTaskImpl):
    """
    语义分析任务，将词句转化为语义树
    """

    def __init__(self, link: Link, pam: PAMemory, links_size: int):
        """
        Initialize a SemanticAnalyzTask0.

        Args:
            link: The link to analyze
            pam: The PAMemory
            links_size: The size of links
        """
        super().__init__(1)
        self.pam = pam
        self.link = link
        self.links_size = links_size

        # 内听觉，外听觉，内视觉，外视觉，内文本，外文本，至少6个语句分析来源，来源汇总区分，统一到符号语义
        # 语言高频处理，需要常驻？分别尝试下
        self.listen_ns = pam.get_workspace_buffer("listen").get_buffer_content(None)
        self.yuyi_ns = pam.get_workspace_buffer("yuyi").get_buffer_content(None)

        self.yi_tree_bag = self.yuyi_ns.chart_set if isinstance(self.yuyi_ns, ChartTreeSet) else TreeBag(100, 100, 100)

        self.words = []
        self.lsize = 0  # 对应场景总边数量属性
        self.actlsize = 0  # 场景已激活的边的数量
        self.links = set()  # 当前词所在的所有构式边
        self.pos = None

        self.started = threading.Event()
        self.started.clear()

        self.mmcache0 = set()  # PamImpl0.smcache

        self.logger = logging.getLogger("SemanticAnalyzTask0")

        # 增强的语义分析参数
        self.construction_cache = {}  # 构式缓存
        self.semantic_patterns = {}   # 语义模式
        self.execution_context = {}   # 执行上下文
        self.parsing_strategies = []  # 解析策略列表

        # 构式匹配权重
        self.construction_weights = {
            "complete_match": 1.0,      # 完全匹配
            "partial_match": 0.7,       # 部分匹配
            "structural_match": 0.5,    # 结构匹配
            "semantic_match": 0.8       # 语义匹配
        }

        # 执行规划参数
        self.execution_threshold = 0.6  # 执行阈值
        self.max_nesting_depth = 6      # 最大嵌套深度
        self.complexity_limit = 100     # 复杂度限制

    def run_this_framework_task(self):
        """
        增强版语义分析任务执行
        基于Java版本的复杂语义分析逻辑
        """
        try:
            # 初始化执行上下文
            self.initialize_execution_context()

            # 多阶段语义分析流程
            analysis_result = self.multi_stage_semantic_analysis()

            # 构式匹配与树构建
            if analysis_result:
                tree_charts = self.enhanced_construction_matching(analysis_result)

                # 执行规划生成
                if tree_charts:
                    execution_plans = self.generate_execution_plans(tree_charts)

                    # 执行计划
                    self.execute_plans(execution_plans)

            # 传统流程保持兼容性
            self.actsence(self.link, None)

        except Exception as e:
            self.logger.error(f"Error in enhanced semantic analysis: {e}")
            # 回退到传统方法
            self.actsence(self.link, None)
        finally:
            self.cancel()

    def initialize_execution_context(self):
        """
        初始化执行上下文
        """
        try:
            # 获取当前消息
            from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
            if hasattr(AgentStarter, 'message'):
                self.execution_context['input_message'] = AgentStarter.message
                self.execution_context['message_length'] = len(AgentStarter.message) if AgentStarter.message else 0

            # 设置复杂度限制
            message_length = self.execution_context.get('message_length', 0)
            self.complexity_limit = max(message_length * 6, 100)

            # 初始化解析策略
            self.parsing_strategies = [
                'structural_parsing',    # 结构解析
                'semantic_parsing',      # 语义解析
                'construction_matching', # 构式匹配
                'execution_planning'     # 执行规划
            ]

        except Exception as e:
            self.logger.error(f"Error initializing execution context: {e}")

    def multi_stage_semantic_analysis(self):
        """
        多阶段语义分析

        Returns:
            dict: 分析结果
        """
        try:
            analysis_result = {
                'structural_features': {},
                'semantic_features': {},
                'construction_candidates': [],
                'execution_hints': []
            }

            # 阶段1：结构特征提取
            analysis_result['structural_features'] = self.extract_structural_features()

            # 阶段2：语义特征提取
            analysis_result['semantic_features'] = self.extract_semantic_features()

            # 阶段3：构式候选生成
            analysis_result['construction_candidates'] = self.generate_construction_candidates(
                analysis_result['structural_features'],
                analysis_result['semantic_features']
            )

            # 阶段4：执行提示生成
            analysis_result['execution_hints'] = self.generate_execution_hints(
                analysis_result['construction_candidates']
            )

            return analysis_result

        except Exception as e:
            self.logger.error(f"Error in multi-stage analysis: {e}")
            return None

    def extract_structural_features(self):
        """
        提取结构特征

        Returns:
            dict: 结构特征
        """
        try:
            features = {
                'node_types': set(),
                'link_types': set(),
                'depth_levels': {},
                'connectivity_patterns': []
            }

            # 分析链接的结构特征
            if self.link:
                sink = self.link.getSink() if hasattr(self.link, 'getSink') else None
                source = self.link.getSource() if hasattr(self.link, 'getSource') else None

                if sink:
                    sink_name = sink.get_node_name() if hasattr(sink, 'get_node_name') else ""
                    if hasattr(sink, 'getTNname'):
                        sink_name = sink.getTNname()

                    # 分析节点类型
                    if "_NP" in sink_name or "_VP" in sink_name or "_IP" in sink_name:
                        features['node_types'].add('grammar')
                    elif "arg" in sink_name:
                        features['node_types'].add('argument')
                    else:
                        features['node_types'].add('concept')

                # 分析链接类型
                if hasattr(self.link, 'getCategory'):
                    category = self.link.getCategory()
                    if category:
                        if hasattr(category, 'get_node_name'):
                            link_type = category.get_node_name()
                        elif hasattr(category, 'getName'):
                            link_type = category.getName()
                        elif isinstance(category, dict) and 'label' in category:
                            link_type = category['label']
                        else:
                            link_type = str(category)
                        features['link_types'].add(link_type)

            return features

        except Exception as e:
            self.logger.error(f"Error extracting structural features: {e}")
            return {}

    def extract_semantic_features(self):
        """
        提取语义特征

        Returns:
            dict: 语义特征
        """
        try:
            features = {
                'semantic_roles': [],
                'concept_relations': [],
                'modal_indicators': [],
                'temporal_markers': []
            }

            # 分析语义角色
            if self.link:
                sink = self.link.getSink() if hasattr(self.link, 'getSink') else None
                if sink:
                    sink_name = sink.get_node_name() if hasattr(sink, 'get_node_name') else ""
                    if hasattr(sink, 'getTNname'):
                        sink_name = sink.getTNname()

                    # 识别语义角色
                    if "agent" in sink_name.lower():
                        features['semantic_roles'].append('agent')
                    elif "patient" in sink_name.lower():
                        features['semantic_roles'].append('patient')
                    elif "action" in sink_name.lower() or "动作" in sink_name:
                        features['semantic_roles'].append('action')

                    # 识别情感标记
                    if "happy" in sink_name or "开心" in sink_name:
                        features['modal_indicators'].append('positive_emotion')
                    elif "sad" in sink_name or "伤心" in sink_name:
                        features['modal_indicators'].append('negative_emotion')

            return features

        except Exception as e:
            self.logger.error(f"Error extracting semantic features: {e}")
            return {}

    def generate_construction_candidates(self, structural_features, semantic_features):
        """
        生成构式候选

        Args:
            structural_features: 结构特征
            semantic_features: 语义特征

        Returns:
            list: 构式候选列表
        """
        try:
            candidates = []

            # 基于结构特征生成候选
            if 'grammar' in structural_features.get('node_types', set()):
                candidates.append({
                    'type': 'grammar_construction',
                    'confidence': 0.8,
                    'features': structural_features
                })

            # 基于语义特征生成候选
            if semantic_features.get('semantic_roles'):
                candidates.append({
                    'type': 'semantic_construction',
                    'confidence': 0.7,
                    'features': semantic_features
                })

            # 复合构式
            if (structural_features.get('node_types') and
                semantic_features.get('semantic_roles')):
                candidates.append({
                    'type': 'hybrid_construction',
                    'confidence': 0.9,
                    'features': {**structural_features, **semantic_features}
                })

            # 按置信度排序
            candidates.sort(key=lambda x: x['confidence'], reverse=True)

            return candidates

        except Exception as e:
            self.logger.error(f"Error generating construction candidates: {e}")
            return []

    def generate_execution_hints(self, construction_candidates):
        """
        生成执行提示

        Args:
            construction_candidates: 构式候选列表

        Returns:
            list: 执行提示列表
        """
        try:
            hints = []

            for candidate in construction_candidates:
                if candidate['type'] == 'grammar_construction':
                    hints.append({
                        'action': 'parse_grammar',
                        'priority': candidate['confidence'],
                        'parameters': candidate['features']
                    })
                elif candidate['type'] == 'semantic_construction':
                    hints.append({
                        'action': 'analyze_semantics',
                        'priority': candidate['confidence'],
                        'parameters': candidate['features']
                    })
                elif candidate['type'] == 'hybrid_construction':
                    hints.append({
                        'action': 'integrated_analysis',
                        'priority': candidate['confidence'],
                        'parameters': candidate['features']
                    })

            return hints

        except Exception as e:
            self.logger.error(f"Error generating execution hints: {e}")
            return []

    def enhanced_construction_matching(self, analysis_result):
        """
        增强的构式匹配

        Args:
            analysis_result: 分析结果

        Returns:
            list: TreeChart列表
        """
        try:
            tree_charts = []

            # 获取构式候选
            candidates = analysis_result.get('construction_candidates', [])

            for candidate in candidates:
                if candidate['confidence'] >= self.execution_threshold:
                    # 构建TreeChart
                    tree_chart = self.build_enhanced_tree_chart(candidate)
                    if tree_chart:
                        tree_charts.append(tree_chart)

            return tree_charts

        except Exception as e:
            self.logger.error(f"Error in enhanced construction matching: {e}")
            return []

    def build_enhanced_tree_chart(self, construction_candidate):
        """
        构建增强的TreeChart

        Args:
            construction_candidate: 构式候选

        Returns:
            TreeChart: 构建的树图表
        """
        try:
            # 获取基本信息
            sink = self.link.getSink() if hasattr(self.link, 'getSink') else None
            if not sink:
                return None

            sink_name = sink.get_node_name() if hasattr(sink, 'get_node_name') else ""
            if hasattr(sink, 'getTNname'):
                sink_name = sink.getTNname()

            # 创建预算
            from linars.edu.memphis.ccrg.lida.PAM.Budget import Budget
            budget = Budget(
                construction_candidate['confidence'],
                0.1,
                0.1
            )

            # 创建组件列表
            components = [self.link]
            found_list = [self.link]
            finding_list = []

            # 根据构式类型调整参数
            if construction_candidate['type'] == 'grammar_construction':
                # 语法构式需要更多的结构信息
                finding_list = self.generate_grammar_finding_list(sink_name)
            elif construction_candidate['type'] == 'semantic_construction':
                # 语义构式需要更多的语义信息
                finding_list = self.generate_semantic_finding_list(sink_name)

            # 创建TreeChart
            tree_chart = TreeChart(
                budget=budget,
                scene_root=sink,
                found_list=found_list,
                finding_list=finding_list
            )

            # 设置复杂度
            tree_chart.complexity = len(components)

            return tree_chart

        except Exception as e:
            self.logger.error(f"Error building enhanced tree chart: {e}")
            return None

    def generate_grammar_finding_list(self, sink_name):
        """
        生成语法查找列表

        Args:
            sink_name: 节点名称

        Returns:
            list: 查找列表
        """
        try:
            finding_list = []

            # 基于语法规则生成查找项
            if "_NP" in sink_name:
                # 名词短语可能需要修饰语
                finding_list.extend(self.search_grammar_components("adj", sink_name))
                finding_list.extend(self.search_grammar_components("det", sink_name))
            elif "_VP" in sink_name:
                # 动词短语可能需要宾语
                finding_list.extend(self.search_grammar_components("obj", sink_name))
                finding_list.extend(self.search_grammar_components("adv", sink_name))

            return finding_list

        except Exception as e:
            self.logger.error(f"Error generating grammar finding list: {e}")
            return []

    def generate_semantic_finding_list(self, sink_name):
        """
        生成语义查找列表

        Args:
            sink_name: 节点名称

        Returns:
            list: 查找列表
        """
        try:
            finding_list = []

            # 基于语义角色生成查找项
            if "action" in sink_name.lower() or "动作" in sink_name:
                # 动作需要主体和客体
                finding_list.extend(self.search_semantic_components("agent", sink_name))
                finding_list.extend(self.search_semantic_components("patient", sink_name))

            return finding_list

        except Exception as e:
            self.logger.error(f"Error generating semantic finding list: {e}")
            return []

    def search_grammar_components(self, component_type, context):
        """
        搜索语法组件

        Args:
            component_type: 组件类型
            context: 上下文

        Returns:
            list: 找到的组件列表
        """
        try:
            components = []

            # 使用Neo4j搜索相关组件
            try:
                from linars.edu.memphis.ccrg.lida.Data.neo_util import NeoUtil
                query = f"MATCH (n)-[r:{component_type}]->(m) WHERE n.name CONTAINS '{context}' RETURN r"
                result = NeoUtil.execute_query(query)

                if result:
                    while hasattr(result, "has_next") and result.has_next():
                        row = result.next()
                        for key in result.columns():
                            rel = row.get(key)
                            if rel:
                                link = NeoUtil.cast_neo_to_lida_link(rel) if hasattr(NeoUtil, 'cast_neo_to_lida_link') else None
                                if link:
                                    components.append(link)
            except Exception as e:
                self.logger.warning(f"Neo4j search failed, using fallback: {e}")

            return components

        except Exception as e:
            self.logger.error(f"Error searching grammar components: {e}")
            return []

    def search_semantic_components(self, role_type, context):
        """
        搜索语义组件

        Args:
            role_type: 角色类型
            context: 上下文

        Returns:
            list: 找到的组件列表
        """
        try:
            components = []

            # 使用Neo4j搜索语义角色
            try:
                from linars.edu.memphis.ccrg.lida.Data.neo_util import NeoUtil
                query = f"MATCH (n)-[r:{role_type}]->(m) WHERE n.name CONTAINS '{context}' RETURN r"
                result = NeoUtil.execute_query(query)

                if result:
                    while hasattr(result, "has_next") and result.has_next():
                        row = result.next()
                        for key in result.columns():
                            rel = row.get(key)
                            if rel:
                                link = NeoUtil.cast_neo_to_lida_link(rel) if hasattr(NeoUtil, 'cast_neo_to_lida_link') else None
                                if link:
                                    components.append(link)
            except Exception as e:
                self.logger.warning(f"Neo4j search failed for semantic components: {e}")

            return components

        except Exception as e:
            self.logger.error(f"Error searching semantic components: {e}")
            return []

    def generate_execution_plans(self, tree_charts):
        """
        生成执行计划

        Args:
            tree_charts: TreeChart列表

        Returns:
            list: 执行计划列表
        """
        try:
            execution_plans = []

            for tree_chart in tree_charts:
                plan = self.create_execution_plan(tree_chart)
                if plan:
                    execution_plans.append(plan)

            # 按优先级排序
            execution_plans.sort(key=lambda x: x.get('priority', 0), reverse=True)

            return execution_plans

        except Exception as e:
            self.logger.error(f"Error generating execution plans: {e}")
            return []

    def create_execution_plan(self, tree_chart):
        """
        创建执行计划

        Args:
            tree_chart: TreeChart对象

        Returns:
            dict: 执行计划
        """
        try:
            plan = {
                'id': f"plan_{id(tree_chart)}",
                'tree_chart': tree_chart,
                'priority': tree_chart.budget.get_priority() if tree_chart.budget else 0.5,
                'steps': [],
                'dependencies': [],
                'execution_context': {}
            }

            # 生成执行步骤
            if tree_chart.finding_list:
                # 如果有待查找项，需要先完成搜索
                plan['steps'].append({
                    'type': 'search',
                    'action': 'complete_construction',
                    'parameters': {
                        'finding_list': tree_chart.finding_list,
                        'scene_root': tree_chart.scene_root
                    }
                })

            if tree_chart.found_list:
                # 处理已找到的项
                plan['steps'].append({
                    'type': 'process',
                    'action': 'integrate_components',
                    'parameters': {
                        'found_list': tree_chart.found_list,
                        'scene_root': tree_chart.scene_root
                    }
                })

            # 最终执行步骤
            plan['steps'].append({
                'type': 'execute',
                'action': 'submit_to_workspace',
                'parameters': {
                    'tree_chart': tree_chart,
                    'target_module': ModuleName.CurrentSM
                }
            })

            return plan

        except Exception as e:
            self.logger.error(f"Error creating execution plan: {e}")
            return None

    def execute_plans(self, execution_plans):
        """
        执行计划

        Args:
            execution_plans: 执行计划列表
        """
        try:
            for plan in execution_plans:
                if plan['priority'] >= self.execution_threshold:
                    self.execute_single_plan(plan)

        except Exception as e:
            self.logger.error(f"Error executing plans: {e}")

    def execute_single_plan(self, plan):
        """
        执行单个计划

        Args:
            plan: 执行计划
        """
        try:
            for step in plan['steps']:
                if step['type'] == 'search':
                    self.execute_search_step(step)
                elif step['type'] == 'process':
                    self.execute_process_step(step)
                elif step['type'] == 'execute':
                    self.execute_final_step(step)

        except Exception as e:
            self.logger.error(f"Error executing single plan: {e}")

    def execute_search_step(self, step):
        """
        执行搜索步骤

        Args:
            step: 搜索步骤
        """
        try:
            # 触发搜索任务
            if hasattr(self.pam, 'task_spawner'):
                try:
                    from linars.edu.memphis.ccrg.lida.PAM.Tasks.SearchSB import SearchSB
                    search_task = SearchSB(
                        step['parameters']['finding_list'],
                        'semantic_construction',
                        self.pam
                    )
                    self.pam.task_spawner.add_task(search_task)
                except ImportError:
                    self.logger.warning("SearchSB not available for search step")

        except Exception as e:
            self.logger.error(f"Error executing search step: {e}")

    def execute_process_step(self, step):
        """
        执行处理步骤

        Args:
            step: 处理步骤
        """
        try:
            # 整合组件
            found_list = step['parameters']['found_list']
            for component in found_list:
                if hasattr(self.pam, 'get_listener'):
                    listener = self.pam.get_listener()
                    if hasattr(listener, 'receive_percept'):
                        listener.receive_percept(component, ModuleName.CurrentSM)

        except Exception as e:
            self.logger.error(f"Error executing process step: {e}")

    def execute_final_step(self, step):
        """
        执行最终步骤

        Args:
            step: 最终步骤
        """
        try:
            tree_chart = step['parameters']['tree_chart']
            target_module = step['parameters']['target_module']

            # 提交到工作空间
            if hasattr(self.pam, 'get_listener'):
                listener = self.pam.get_listener()
                if hasattr(listener, 'receive_percept'):
                    listener.receive_percept(tree_chart, target_module)

            # 添加到完整构式集合
            if hasattr(self.yi_tree_bag, 'complete_terms'):
                self.yi_tree_bag.complete_terms[str(tree_chart)] = tree_chart

        except Exception as e:
            self.logger.error(f"Error executing final step: {e}")

    def process_finding_list(self, match_tree_chart: TreeChart, parent: Link) -> bool:
        """
        处理findingList中的匹配链接

        Args:
            match_tree_chart: 当前匹配的树图表
            parent: 父链接

        Returns:
            是否找到匹配项
        """
        is_find = False
        # 判断当前key的value里，findingList是否包含当前parent链接，有则从findingList里移除，在foundList里添加
        # 无论是否嵌套，是否有匹配完整的嵌套，一旦符合根节点和有find边，都移除。是嵌套构式，要在parent替换整体子构式前移除
        if parent in match_tree_chart.finding_list:
            match_tree_chart.finding_list.remove(parent)
            is_find = True
        return is_find

    def check_found_status(self, parent: Link, insert_chart: TreeChart, match_tree_chart: TreeChart, is_find: bool) -> bool:
        """
        检查found状态并更新匹配尺寸

        Args:
            parent: 父链接
            insert_chart: 要插入的图表
            match_tree_chart: 当前匹配的树图表
            is_find: 是否找到匹配项

        Returns:
            是否找到匹配的源节点
        """
        is_found = False
        found_copy = []
        found_copy.extend(match_tree_chart.found_list)
        # 跟上面if不能合并，因为found变了，addall也不能放里面
        if not is_find:
            for link in found_copy:
                ss = link.get_source()
                if isinstance(ss, TreeChart):
                    rr = ss.scene_root.get_tn_name()
                    ssize = ss.cpsize
                else:
                    rr = ss.get_tn_name()
                    ssize = 1
                # 无论是否已嵌套，只要找到匹配的，就替换
                if rr == parent.get_source().get_tn_name():
                    is_found = True
                    link.set_source(insert_chart)  # 在原链接改
                    link.init(link.term)
                    match_tree_chart.cpsize = match_tree_chart.cpsize + insert_chart.cpsize - ssize
                    break
        self.rebuild_tree_chart(match_tree_chart, found_copy)
        return is_found

    def rebuild_tree_chart(self, match_tree_chart: TreeChart, found_copy: Collection[Term]):
        """
        重新构建树图表

        Args:
            match_tree_chart: 需要重建的树图表
            found_copy: 已找到的链接副本
        """
        # 将foundList和findingList里的链接整合为新list
        links2 = []
        for link in match_tree_chart.found_list:
            links2.append(link)
        for link in match_tree_chart.finding_list:
            links2.append(link)

        root = match_tree_chart.scene_root
        # 然后重新组装并更新matchTreeChart的sceneTerm
        match_tree_chart = self.build_tree(root, root.get_tn_name(), found_copy, len(match_tree_chart.found_list), links2)
        match_tree_chart.init(match_tree_chart.term)

    def actsence(self, parent: Link, insert_chart: Optional[TreeChart]):
        """
        处理场景激活

        Args:
            parent: 父链接
            insert_chart: 要插入的图表
        """
        sink = parent.get_sink()
        sname = sink.get_tn_name()
        is_act = False
        match_tree_chart = None

        # 遍历treebag里nameTable的keyset中，左括号前字符是否匹配sname，也就是是否已经有该sink，有则跳过，没有则加入
        for key in self.yi_tree_bag.name_table.keys():
            if key.split("(")[0] == sname:
                match_tree_chart = self.yi_tree_bag.name_table[key]
                is_find = self.process_finding_list(match_tree_chart, parent)
                is_found = False

                # 单字扩散可能边类型：isa等所有普通元素边类型，语义框架扩散边类型：顺承、isa、arg、内容、蕴含、感知模态等，可能双向
                # 目前默认+文本模态=文本语义，其他模态+学语言前=非文本含义，感知模态+学语言后=两类同时激活，视听文字理解后=文本语义
                # 感知激活非语言文本，与语言文本类似，也有嵌套结构和义法等？感知场景，文本场景，感知文本杂糅场景=内容杂糅。结构另算
                # 由文本激活感知，由感知激活文本，最终都是同时扩散。感知本身场景、感知文本互相激活场景、文本本身场景，都是场景处理方案

                # insertChart不为空，说明是中间层构式，来自下层激活，在未匹配完整时就要开始嵌套构建
                if insert_chart is not None:
                    self.handle_insert_chart(parent, insert_chart, is_find, match_tree_chart)
                    is_found = self.check_found_status(parent, insert_chart, match_tree_chart, is_find)

                # 上面加了，这里就不用加了。如已有完整嵌套构式，也要加入foundList，把已匹配的对应部分替换成parent
                if parent not in match_tree_chart.found_list and not is_found:
                    match_tree_chart.found_list.append(parent)

                self.process_complete_construction(match_tree_chart, sname)
                is_act = True
                break  # 构式根节点都唯一，找到一个就跳出

        self.process_size_attribute(sink)
        # 根据根节点sink判断buffer里是否有两条边或以上构式，有则尝试构建语yi树
        # 第一个词通常无完整构式，也有可能首词非句首=语句被截断，乱序输入等，越靠近句首=构式角色越靠前
        if not is_act:
            self.handle_incomplete_activation(sname, sink)

    def handle_incomplete_activation(self, sname: str, sink: Node):
        """
        处理未完全激活的情况

        Args:
            sname: 场景名称
            sink: 当前节点
        """
        # 未全激活，加入新链接后，看是否完整激活
        scene_ls = self.yuyi_ns.get_links_of_sink_t(sname) if isinstance(self.yuyi_ns, ChartTreeSet) else []
        self.actlsize = len(scene_ls)
        sss = self.actlsize / self.lsize if self.lsize > 0 else 0
        # 一个词也能匹配单构式？只有单个词如感叹+拟声等，连词=但和与？np=n等也是单构式
        if (sss == 0.5 and self.links_size < 500) or sss > 0.5:
            # 如果构式总边数与已激活边数达到一半，则直接激活整个框架为完整构式，优先级最高，放进确定集合里
            # 未匹配的可当预测，也可先不管，不完整构式不好嵌套，完整的嵌套能一次成型，无需每次更新总构式，再有新嵌套直接拼接
            from linars.edu.memphis.ccrg.lida.Data.neo_util import NeoUtil
            links0 = NeoUtil.get_some_links(sink, None, "<", None, None)
            links00 = list(links0)

            self.build_tree(sink, sname, scene_ls, self.lsize, links00)

    def process_size_attribute(self, sink: Node):
        """
        处理size属性获取

        Args:
            sink: 当前节点
        """
        size = sink.get_property("size")
        if size is not None:
            # 分别按字符串类型或long类型取int值
            if isinstance(size, str):
                self.lsize = int(size)
            else:
                self.lsize = int(size)
        else:
            self.lsize = 100  # 无size属性，说明是叶子节点或暂没设置，暂设为100，匹配率肯定低于0.5

    def process_complete_construction(self, match_tree_chart: TreeChart, sname: str) -> bool:
        """
        处理完整构式激活逻辑

        Args:
            match_tree_chart: 当前匹配的树图表
            sname: 场景名称

        Returns:
            是否执行了激活操作
        """
        # 如果findingList为空，说明该构式已经完整匹配，加入treebag的完整构式列表
        if not match_tree_chart.finding_list:
            # 不能无限嵌套，复杂度不能大于句子长度
            from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
            if match_tree_chart.complexity < len(AgentStarter.message) * 6:
                # 部分完整也可输出部分理解，通达意识。但既不是听觉也不是视觉，只是个语义框架
                # 判断是否完全解析理解，已纳入所有输入词语，并无多余成分等
                # 完全理解，则往下激活顺承，激活动机，不完全也可激活，但需要竞争，不能激活就执行

                # 递归给语义树子节点编号和提交
                match_tree_chart = self.number_tree(match_tree_chart, 0)
                self.yi_tree_bag.complete_terms[str(match_tree_chart)] = match_tree_chart
                self.activate_sub_constructions(match_tree_chart, sname)
        return True

    def activate_sub_constructions(self, match_tree_chart: TreeChart, sname: str):
        """
        激活下层构式

        Args:
            match_tree_chart: 当前匹配的树图表
            sname: 场景名称
        """
        # 继续往下激活，中间层构式，直接用成分而不是词性，虽然词性与成分一一对应
        node = NodeImpl(sname)
        # todo 直接搜，而不是pam扩散，有针对性，但丢了很多其他信息，如内涵等，节律也不够好
        # 多种结构和多种内涵时，需要判别筛选，如苹果。语法辅助（结构搭配）、内涵辅助（语义搭配）
        # 禁止性搭配，短路抑制机制，不能XX。先天规则层抑制、后天义法层抑制
        from linars.edu.memphis.ccrg.lida.Data.neo_util import NeoUtil
        links01 = NeoUtil.get_links(node)
        if links01 and links01:
            for link in links01:
                # 递归激活下层构式，暂时限制边类型arg
                if len(link.get_tn_name()) >= 3 and link.get_tn_name()[:3] == "arg":
                    self.actsence(link, match_tree_chart)

    @staticmethod
    def handle_insert_chart(parent: Link, insert_chart: TreeChart, is_find: bool, match_tree_chart: TreeChart) -> str:
        """
        处理插入图表时的嵌套构建逻辑

        Args:
            parent: 父链接
            insert_chart: 要插入的图表
            is_find: 是否找到匹配项
            match_tree_chart: 当前匹配的树图表

        Returns:
            源节点名称
        """
        source_name = parent.get_source().get_tn_name()
        # 如果是正在find的构式，不是已有匹配完整嵌套构式，直接加入构建新嵌套
        if is_find:
            # 用构式2整体替换foundList里的parent的source节点
            parent.set_source(insert_chart)
            parent.init(parent.term)
            # 如果是已有匹配完整嵌套构式，先移除foundlist的对应部分，再加入构建新嵌套，保留已有的
            match_tree_chart.found_list.append(parent)
            # 完成匹配链接总数
            match_tree_chart.cpsize = match_tree_chart.cpsize + insert_chart.cpsize - 1
        return source_name

    def number_tree(self, tree_chart: TreeChart, i: int) -> TreeChart:
        """
        给树图表编号

        Args:
            tree_chart: 树图表
            i: 起始编号

        Returns:
            编号后的树图表
        """
        # 遍历chart里的foundlist链接，找到order为0的节点，即最左边节点，然后递归给子节点编号
        # foundList是Collection，不能直接用get，需要转成list
        found_list0 = list(tree_chart.found_list)
        num = i  # 从i开始编号，num是累积编号，也是当前编号
        for j in range(len(found_list0)):
            # 找到order与j相等的节点
            # todo foundlist一开始就按order排序，不用遍历。另外有容错纠错需求，词序不对不一定错误
            for term in found_list0:
                link = term
                if int(link.get_property("order")) == j:
                    # 如果链接的source节点为chart，则递归给chart的子节点编号
                    if isinstance(link.get_source(), TreeChart):
                        tree_chart1 = link.get_source()
                        tree_chart1 = self.number_tree(tree_chart1, num)
                        num = tree_chart1.cpsize + num
                    else:
                        link.now_order = num
                        # 除了递归编号，还递归提交到语义图理解模块，无论是否完全匹配整句，相当于渐进式逐步理解
                        # link是arg结构，提交到边集，但不会提交到nars。点可提交到点集
                        # todo 整合到pam
                        self.pam.get_listener().receive_percept(link, ModuleName.CurrentSM)

                        from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
                        AgentStarter.nar.memory.add_default_node(link.get_source())
                        AgentStarter.nar.memory.add_default_link(link)

                        print(f"提交语义图理解模块：---------{link}")
                        num += 1
        return tree_chart

    def build_tree(self, scene_root: Term, root_name: str, found_list: Collection[Term],
                  lsize: int, links00: List[Link]) -> TreeChart:
        """
        构建树图表

        Args:
            scene_root: 场景根节点
            root_name: 根节点名称
            found_list: 已找到的链接
            lsize: 链接大小
            links00: 链接列表

        Returns:
            构建的树图表
        """
        finding_list = []
        components = [None] * lsize  # 无关系元素集合，次序关系在元素属性里，可能有子节点
        order_map = {}

        # 图搜索得到的都是链接，暂不提取转term数组的方法，term数组还要遍历，这里现场实时遍历更便捷
        for i in range(lsize):
            if i >= len(links00):
                break

            ll = links00[i]
            if not ll.get_tn_name().startswith("ar"):
                continue

            components[i] = ll  # 只是遍历次序，实际次序在元素属性里

            self.pam.get_listener().receive_percept(self.link, ModuleName.CurrentSM)

            from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
            AgentStarter.nar.memory.add_default_node(self.link.get_source())
            AgentStarter.nar.memory.add_default_link(self.link)

            if ll not in found_list:
                finding_list.append(ll)  # 未激活的边，都放预测边集里

        # 生成场景文本序列=产生式规则，场景为左部，构式其他成分为右部，要注意词语次序，有些框架本身带有嵌套
        # 两种方案，原始单产生式直存到场景节点；或实时生成。后者更好，因为存储费空间，而且不好改，再者嵌套需要实时生成

        from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
        from linars.edu.memphis.ccrg.lida.PAM.Budget import Budget

        tree_chart = TreeChart(Budget(0.99, 0.1, 0.1), scene_root, components, found_list, finding_list)

        self.yi_tree_bag.put_back(tree_chart, 10.0, AgentStarter.nar.memory)

        return tree_chart
