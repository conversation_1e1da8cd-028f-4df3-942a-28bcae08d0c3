"""
优化后的循环任务
"""

import asyncio
import logging
import time
from typing import List, Optional, Any, Dict, Set
from collections import deque

from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory
from linars.edu.memphis.ccrg.lida.Data.neo_util import NeoUtil
from linars.edu.memphis.ccrg.lida.Nlanguage.TreeChart import TreeChart, SchemaType
from linars.edu.memphis.ccrg.lida.Nlanguage.SchemaFactory import schema_factory
from linars.edu.memphis.ccrg.lida.Nlanguage.SchemaExecutor import schema_executor, ExecutionContext
from .OptimizedControlFlowTask import OptimizedControlFlowTask, ControlFlowType, TaskStatus


class LoopType:
    """循环类型常量"""
    DO_WHILE = "do_while"
    WHILE = "while"
    FOR = "for"
    FOREACH = "foreach"


class OptimizedForEachTask(OptimizedControlFlowTask):
    """
    优化后的循环任务
    
    主要改进：
    1. 支持多种循环类型（do-while, while, for, foreach）
    2. 智能的循环条件评估
    3. 循环变量管理和作用域控制
    4. 防止无限循环的安全机制
    5. 性能优化和并发控制
    """
    
    def __init__(self, link: Link, pam: PAMemory, seq_ns: Optional[NodeStructure] = None,
                 scene_ns: Optional[NodeStructure] = None, act_stamp: Optional[str] = None, 
                 loop_type: str = LoopType.DO_WHILE, **kwargs):
        super().__init__(ControlFlowType.LOOP, 1, "optimized_foreach")
        
        self.link = link
        self.pam = pam
        self.seq_ns = seq_ns
        self.scene_ns = scene_ns
        self.act_stamp = act_stamp
        self.loop_type = loop_type
        
        # 循环相关属性
        self.loop_schema = None
        self.condition_link = None
        self.loop_body_link = None
        self.loop_variables = {}
        self.iteration_count = 0
        self.iteration_results = []
        
        # 循环控制
        self.loop_state = {
            'is_running': False,
            'should_continue': True,
            'break_requested': False,
            'continue_requested': False
        }
        
        # 性能监控
        self.loop_stats = {
            'total_iterations': 0,
            'successful_iterations': 0,
            'failed_iterations': 0,
            'average_iteration_time': 0.0,
            'condition_evaluations': 0,
            'condition_evaluation_time': 0.0
        }
        
        # 配置选项
        self.config.update({
            'max_iterations': kwargs.get('max_iterations', 1000),
            'iteration_timeout': kwargs.get('iteration_timeout', 60),
            'condition_timeout': kwargs.get('condition_timeout', 30),
            'enable_early_termination': kwargs.get('enable_early_termination', True),
            'enable_iteration_caching': kwargs.get('enable_iteration_caching', False),
            'parallel_iterations': kwargs.get('parallel_iterations', False),
            'max_parallel_iterations': kwargs.get('max_parallel_iterations', 5),
            'break_on_error': kwargs.get('break_on_error', False)
        })
        
        # 初始化循环结构
        self._initialize_loop_structure()
    
    def _initialize_loop_structure(self):
        """初始化循环结构"""
        try:
            # 构建循环结构
            self._build_loop_structure()
            
            # 创建循环图式
            self._create_loop_schema()
            
            self.status = TaskStatus.READY
            self.logger.info(f"Initialized {self.loop_type} loop structure")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize loop structure: {e}")
            self.status = TaskStatus.FAILED
            self.error_info = str(e)
    
    def _build_loop_structure(self):
        """构建循环结构"""
        # 获取工作空间缓冲区
        if not self.seq_ns:
            self.seq_ns = self.pam.get_workspace_buffer("seq").get_buffer_content(None)
        
        # 存储上位时序以便回溯
        if self.seq_ns and self.act_stamp:
            path_map = self.seq_ns.get_do_main_path_map()
            if self.act_stamp in path_map:
                path_map[self.act_stamp].append(self.link)
        
        # 查找循环条件
        self._find_loop_condition()
        
        # 设置循环体
        self.loop_body_link = self.link
        
        # 初始化循环变量
        self._initialize_loop_variables()
    
    def _find_loop_condition(self):
        """查找循环条件"""
        try:
            if self.link and hasattr(self.link, 'get_sink'):
                sink = self.link.get_sink()
                if hasattr(sink, 'get_node_id'):
                    node_id = sink.get_node_id()
                    query = f"match (m)-[r:循环条件]->(i) where id(m) = {node_id} return r"
                    
                    self.condition_link = NeoUtil.get_link_cypher(query)
                    
                    if self.condition_link:
                        self.logger.debug(f"Found loop condition: {self.condition_link}")
                    else:
                        self.logger.warning("No loop condition found, using default condition")
                        
        except Exception as e:
            self.logger.warning(f"Failed to find loop condition: {e}")
            self.condition_link = None
    
    def _initialize_loop_variables(self):
        """初始化循环变量"""
        # 设置默认循环变量
        self.loop_variables = {
            'iteration': 0,
            'max_iterations': self.config.get('max_iterations', 1000),
            'continue_loop': True,
            'loop_start_time': time.time()
        }
        
        # 如果是for循环，可以设置更多变量
        if self.loop_type == LoopType.FOR:
            self.loop_variables.update({
                'start': 0,
                'end': 10,
                'step': 1,
                'current': 0
            })
        elif self.loop_type == LoopType.FOREACH:
            self.loop_variables.update({
                'items': [],
                'current_item': None,
                'current_index': 0
            })
    
    def _create_loop_schema(self):
        """创建循环图式"""
        # 创建条件评估图式
        condition_action = schema_factory.create_action_schema(
            action_type='evaluate_loop_condition',
            action_params={
                'condition_link': self.condition_link,
                'loop_type': self.loop_type,
                'loop_variables': self.loop_variables,
                'pam': self.pam,
                'act_stamp': self.act_stamp
            },
            schema_id=f"{self.task_id}_condition"
        )
        
        # 创建循环体图式
        body_action = schema_factory.create_action_schema(
            action_type='execute_loop_body',
            action_params={
                'body_link': self.loop_body_link,
                'loop_type': self.loop_type,
                'loop_variables': self.loop_variables,
                'pam': self.pam,
                'act_stamp': self.act_stamp
            },
            schema_id=f"{self.task_id}_body"
        )
        
        # 创建循环图式
        self.loop_schema = schema_factory.create_loop_schema(
            condition=condition_action,
            body=body_action,
            loop_type=self.loop_type,
            schema_id=f"{self.task_id}_loop",
            max_iterations=self.config.get('max_iterations', 1000)
        )
    
    async def _execute_control_flow(self) -> Any:
        """执行控制流逻辑"""
        if not self.loop_schema:
            raise ValueError("Loop schema not initialized")
        
        # 创建执行上下文
        execution_context = ExecutionContext(
            context_id=f"{self.task_id}_execution"
        )
        
        # 设置执行变量
        execution_context.set_variable('pam', self.pam)
        execution_context.set_variable('act_stamp', self.act_stamp)
        execution_context.set_variable('loop_variables', self.loop_variables)
        execution_context.set_variable('loop_state', self.loop_state)
        
        # 注册循环处理器
        self._register_loop_handlers()
        
        # 根据配置选择执行方式
        if self.config.get('parallel_iterations', False):
            result = await self._execute_parallel_loop(execution_context)
        else:
            result = await self._execute_sequential_loop(execution_context)
        
        return result
    
    async def _execute_sequential_loop(self, context: ExecutionContext) -> Any:
        """顺序执行循环"""
        self.logger.info(f"Starting sequential {self.loop_type} loop execution")
        
        self.loop_state['is_running'] = True
        start_time = time.time()
        
        try:
            # 使用图式执行器执行循环
            result = await schema_executor.execute(self.loop_schema, context)
            
            execution_time = time.time() - start_time
            
            if result.success:
                self.logger.info(f"Loop completed successfully in {execution_time:.4f}s after {self.iteration_count} iterations")
                self._process_successful_loop(result)
            else:
                self.logger.error(f"Loop execution failed: {result.error}")
                self._process_failed_loop(result)
            
            return result
            
        finally:
            self.loop_state['is_running'] = False
    
    async def _execute_parallel_loop(self, context: ExecutionContext) -> Any:
        """并行执行循环（适用于独立的迭代）"""
        max_parallel = self.config.get('max_parallel_iterations', 5)
        self.logger.info(f"Starting parallel loop execution with max {max_parallel} concurrent iterations")
        
        # 预先确定迭代次数（仅适用于for循环等确定次数的循环）
        if self.loop_type in [LoopType.FOR, LoopType.FOREACH]:
            iterations = self._calculate_iteration_count()
            
            # 创建并行任务
            tasks = []
            semaphore = asyncio.Semaphore(max_parallel)
            
            for i in range(min(iterations, self.config.get('max_iterations', 1000))):
                task = self._execute_single_iteration_with_semaphore(i, context, semaphore)
                tasks.append(task)
            
            # 等待所有任务完成
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            successful_results = []
            failed_results = []
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    failed_results.append(result)
                    self.logger.error(f"Iteration {i} failed with exception: {result}")
                elif hasattr(result, 'success') and result.success:
                    successful_results.append(result)
                else:
                    failed_results.append(result)
            
            self.iteration_count = len(results)
            self.loop_stats['successful_iterations'] = len(successful_results)
            self.loop_stats['failed_iterations'] = len(failed_results)
            
            return {
                'success': len(successful_results) > 0,
                'total_iterations': len(results),
                'successful_iterations': len(successful_results),
                'failed_iterations': len(failed_results),
                'results': successful_results
            }
        else:
            # 对于条件循环，回退到顺序执行
            return await self._execute_sequential_loop(context)
    
    def _calculate_iteration_count(self) -> int:
        """计算迭代次数"""
        if self.loop_type == LoopType.FOR:
            start = self.loop_variables.get('start', 0)
            end = self.loop_variables.get('end', 10)
            step = self.loop_variables.get('step', 1)
            return max(0, (end - start + step - 1) // step)
        elif self.loop_type == LoopType.FOREACH:
            items = self.loop_variables.get('items', [])
            return len(items)
        else:
            return self.config.get('max_iterations', 1000)
    
    async def _execute_single_iteration_with_semaphore(self, iteration: int, 
                                                     context: ExecutionContext, 
                                                     semaphore: asyncio.Semaphore):
        """使用信号量控制的单次迭代执行"""
        async with semaphore:
            iteration_context = context.create_child_context(f"{context.context_id}_iter_{iteration}")
            iteration_context.set_variable('current_iteration', iteration)
            
            # 更新循环变量
            if self.loop_type == LoopType.FOR:
                current_value = self.loop_variables['start'] + iteration * self.loop_variables['step']
                iteration_context.set_variable('current_value', current_value)
            elif self.loop_type == LoopType.FOREACH:
                items = self.loop_variables.get('items', [])
                if iteration < len(items):
                    iteration_context.set_variable('current_item', items[iteration])
                    iteration_context.set_variable('current_index', iteration)
            
            # 执行循环体
            body_schema = self.loop_schema.sub_charts[1] if len(self.loop_schema.sub_charts) > 1 else None
            if body_schema:
                return await schema_executor.execute(body_schema, iteration_context)
            else:
                return {'success': True, 'iteration': iteration, 'message': 'Empty loop body'}
    
    def _register_loop_handlers(self):
        """注册循环处理器"""
        from linars.edu.memphis.ccrg.lida.Nlanguage.SchemaExecutor import ActionHandler
        
        class LoopConditionHandler(ActionHandler):
            def __init__(self, task_instance):
                self.task = task_instance
            
            async def execute(self, action_params: Dict[str, Any], context: ExecutionContext) -> Any:
                condition_link = action_params.get('condition_link')
                loop_type = action_params.get('loop_type')
                loop_variables = action_params.get('loop_variables', {})
                
                return await self.task._evaluate_loop_condition(condition_link, loop_type, loop_variables, context)
            
            def validate_params(self, action_params: Dict[str, Any]) -> bool:
                return 'loop_type' in action_params
        
        class LoopBodyHandler(ActionHandler):
            def __init__(self, task_instance):
                self.task = task_instance
            
            async def execute(self, action_params: Dict[str, Any], context: ExecutionContext) -> Any:
                body_link = action_params.get('body_link')
                loop_type = action_params.get('loop_type')
                loop_variables = action_params.get('loop_variables', {})
                pam = action_params.get('pam')
                act_stamp = action_params.get('act_stamp')
                
                return await self.task._execute_loop_body(body_link, loop_type, loop_variables, pam, act_stamp, context)
            
            def validate_params(self, action_params: Dict[str, Any]) -> bool:
                return 'body_link' in action_params and 'pam' in action_params
        
        # 注册处理器
        schema_executor.register_action_handler('evaluate_loop_condition', LoopConditionHandler(self))
        schema_executor.register_action_handler('execute_loop_body', LoopBodyHandler(self))
    
    async def _evaluate_loop_condition(self, condition_link: Optional[Link], 
                                     loop_type: str, loop_variables: Dict[str, Any], 
                                     context: ExecutionContext) -> Any:
        """评估循环条件"""
        start_time = time.time()
        self.loop_stats['condition_evaluations'] += 1
        
        try:
            # 检查基本循环控制
            if self.loop_state.get('break_requested', False):
                return {'continue_loop': False, 'reason': 'Break requested'}
            
            if self.iteration_count >= self.config.get('max_iterations', 1000):
                return {'continue_loop': False, 'reason': 'Max iterations reached'}
            
            # 根据循环类型评估条件
            if loop_type == LoopType.DO_WHILE:
                # do-while循环：先执行一次，然后检查条件
                if self.iteration_count == 0:
                    return {'continue_loop': True, 'reason': 'First iteration of do-while'}
                else:
                    return await self._evaluate_condition_link(condition_link, context)
            
            elif loop_type == LoopType.WHILE:
                # while循环：先检查条件
                return await self._evaluate_condition_link(condition_link, context)
            
            elif loop_type == LoopType.FOR:
                # for循环：基于计数器
                current = loop_variables.get('current', 0)
                end = loop_variables.get('end', 10)
                return {'continue_loop': current < end, 'reason': f'For loop: {current} < {end}'}
            
            elif loop_type == LoopType.FOREACH:
                # foreach循环：基于集合
                items = loop_variables.get('items', [])
                current_index = loop_variables.get('current_index', 0)
                return {'continue_loop': current_index < len(items), 'reason': f'Foreach: {current_index} < {len(items)}'}
            
            else:
                return {'continue_loop': False, 'reason': f'Unknown loop type: {loop_type}'}
                
        finally:
            evaluation_time = time.time() - start_time
            self.loop_stats['condition_evaluation_time'] += evaluation_time
    
    async def _evaluate_condition_link(self, condition_link: Optional[Link], 
                                     context: ExecutionContext) -> Dict[str, Any]:
        """评估条件链接"""
        if not condition_link:
            # 没有条件链接，使用默认条件
            return {'continue_loop': self.iteration_count < 10, 'reason': 'Default condition'}
        
        try:
            # 这里可以实现具体的条件评估逻辑
            # 目前返回基于激活度的简单评估
            condition_node = condition_link.get_sink()
            if hasattr(condition_node, 'get_activation'):
                activation = condition_node.get_activation()
                threshold = 0.5
                continue_loop = activation > threshold
                return {
                    'continue_loop': continue_loop,
                    'activation': activation,
                    'threshold': threshold,
                    'reason': f'Activation {activation} {">" if continue_loop else "<="} threshold {threshold}'
                }
            else:
                return {'continue_loop': True, 'reason': 'No activation method available'}
                
        except Exception as e:
            self.logger.warning(f"Condition evaluation failed: {e}")
            return {'continue_loop': False, 'reason': f'Evaluation error: {str(e)}'}
    
    async def _execute_loop_body(self, body_link: Optional[Link], loop_type: str, 
                               loop_variables: Dict[str, Any], pam: PAMemory, 
                               act_stamp: Optional[str], context: ExecutionContext) -> Any:
        """执行循环体"""
        iteration_start_time = time.time()
        self.iteration_count += 1
        self.loop_stats['total_iterations'] += 1
        
        try:
            self.logger.debug(f"Executing loop body iteration {self.iteration_count}")
            
            # 更新循环变量
            self._update_loop_variables(loop_type, loop_variables, context)
            
            # 创建检查点
            if self.config.get('enable_checkpoints', True):
                self.execution_context.create_checkpoint(f'iteration_{self.iteration_count}')
            
            # 执行循环体
            if body_link:
                # 处理循环体链接
                body_node = body_link.get_sink()
                
                # 接收感知
                pam.get_listener().receive_percept(body_node, ModuleName.SeqGraph)
                pam.get_listener().receive_percept(body_link, ModuleName.SeqGraph)
                
                # 执行循环体
                pam.get_act_root(body_link, False, True, act_stamp)
                
                result = {
                    'iteration': self.iteration_count,
                    'success': True,
                    'executed_link': body_link,
                    'loop_variables': loop_variables.copy()
                }
            else:
                result = {
                    'iteration': self.iteration_count,
                    'success': True,
                    'message': 'Empty loop body executed'
                }
            
            # 记录结果
            self.iteration_results.append(result)
            self.loop_stats['successful_iterations'] += 1
            
            return result
            
        except Exception as e:
            self.logger.error(f"Loop body execution failed at iteration {self.iteration_count}: {e}")
            self.loop_stats['failed_iterations'] += 1
            
            error_result = {
                'iteration': self.iteration_count,
                'success': False,
                'error': str(e)
            }
            
            self.iteration_results.append(error_result)
            
            # 根据配置决定是否中断循环
            if self.config.get('break_on_error', False):
                self.loop_state['break_requested'] = True
            
            return error_result
            
        finally:
            iteration_time = time.time() - iteration_start_time
            
            # 更新平均迭代时间
            total_iterations = self.loop_stats['total_iterations']
            current_avg = self.loop_stats['average_iteration_time']
            self.loop_stats['average_iteration_time'] = (
                (current_avg * (total_iterations - 1) + iteration_time) / total_iterations
            )
    
    def _update_loop_variables(self, loop_type: str, loop_variables: Dict[str, Any], 
                             context: ExecutionContext):
        """更新循环变量"""
        loop_variables['iteration'] = self.iteration_count
        
        if loop_type == LoopType.FOR:
            current = loop_variables.get('current', 0)
            step = loop_variables.get('step', 1)
            loop_variables['current'] = current + step
            context.set_variable('current_value', loop_variables['current'])
        
        elif loop_type == LoopType.FOREACH:
            items = loop_variables.get('items', [])
            current_index = loop_variables.get('current_index', 0)
            
            if current_index < len(items):
                loop_variables['current_item'] = items[current_index]
                context.set_variable('current_item', items[current_index])
                context.set_variable('current_index', current_index)
            
            loop_variables['current_index'] = current_index + 1
        
        # 更新上下文变量
        context.set_variable('loop_variables', loop_variables)
        context.set_variable('iteration_count', self.iteration_count)
    
    def _process_successful_loop(self, result: Any):
        """处理成功的循环执行"""
        self.execution_result = result
        self.logger.info(f"Loop completed successfully after {self.iteration_count} iterations")
    
    def _process_failed_loop(self, result: Any):
        """处理失败的循环执行"""
        self.execution_result = result
        self.error_info = str(result.error) if hasattr(result, 'error') else "Unknown error"
        self.logger.error(f"Loop failed after {self.iteration_count} iterations")
    
    def request_break(self):
        """请求中断循环"""
        self.loop_state['break_requested'] = True
        self.logger.info("Loop break requested")
    
    def request_continue(self):
        """请求继续下一次迭代"""
        self.loop_state['continue_requested'] = True
        self.logger.debug("Loop continue requested")
    
    def get_loop_stats(self) -> Dict[str, Any]:
        """获取循环统计信息"""
        return {
            'task_id': self.task_id,
            'loop_type': self.loop_type,
            'iteration_count': self.iteration_count,
            'status': self.status.value,
            'loop_state': self.loop_state.copy(),
            'loop_stats': self.loop_stats.copy(),
            'results_count': len(self.iteration_results),
            'loop_variables': self.loop_variables.copy()
        }
