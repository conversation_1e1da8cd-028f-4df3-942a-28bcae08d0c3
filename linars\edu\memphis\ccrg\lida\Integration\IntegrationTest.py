"""
集成测试模块
测试所有优化模块的集成效果
"""

import logging
import time
from typing import List, Dict, Any

# 导入优化模块
from linars.edu.memphis.ccrg.lida.PAM.PamImpl0 import PamImpl0
from linars.edu.memphis.ccrg.lida.Nlanguage.SemanticAnalyzTask0 import SemanticAnalyzTask0
from linars.edu.memphis.ccrg.lida.PAM.Tasks.SearchSB import SearchSB
from linars.edu.memphis.ccrg.lida.Integration.NarsLidaBridge import NarsLidaBridge
from linars.edu.memphis.ccrg.lida.Data.EnhancedNeoUtil import EnhancedNeoUtil, get_enhanced_neo_util


class IntegrationTest:
    """
    集成测试类
    测试各个优化模块的协同工作效果
    """

    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.test_results = {}
        
        # 初始化组件
        self.pam = None
        self.semantic_analyzer = None
        self.search_engine = None
        self.nars_lida_bridge = None
        self.enhanced_neo_util = None
        
        self.setup_components()

    def setup_components(self):
        """设置测试组件"""
        try:
            # 初始化PAM
            self.pam = PamImpl0()
            
            # 初始化语义分析器
            self.semantic_analyzer = SemanticAnalyzTask0(None, self.pam)
            
            # 初始化NARS-LIDA桥接
            self.nars_lida_bridge = NarsLidaBridge(None, self.pam)
            
            # 初始化增强Neo4j工具
            self.enhanced_neo_util = get_enhanced_neo_util()
            
            self.logger.info("Integration test components initialized")
            
        except Exception as e:
            self.logger.error(f"Error setting up components: {e}")

    def test_activation_propagation_enhancement(self):
        """测试激活扩散机制增强"""
        try:
            self.logger.info("Testing activation propagation enhancement...")
            
            start_time = time.time()
            
            # 创建测试节点
            from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeImpl import NodeImpl
            test_node = NodeImpl()
            test_node.set_node_name("测试节点_开心")
            test_node.setActivation(0.8)
            
            # 测试动态深度控算
            threshold = self.pam.calculate_dynamic_threshold(test_node, "feel")
            assert threshold > 6, "Dynamic threshold should be increased for emotion nodes"
            
            # 测试差异化传播量计算
            from linars.edu.memphis.ccrg.lida.Framework.Shared.LinkImpl import LinkImpl
            test_link = LinkImpl()
            
            propagation_amount = self.pam.calculate_propagation_amount(
                test_node, test_link, test_node, "feel"
            )
            assert propagation_amount > 0, "Propagation amount should be positive"
            
            execution_time = time.time() - start_time
            
            self.test_results['activation_propagation'] = {
                'status': 'PASSED',
                'execution_time': execution_time,
                'dynamic_threshold': threshold,
                'propagation_amount': propagation_amount
            }
            
            self.logger.info(f"Activation propagation test passed in {execution_time:.3f}s")
            
        except Exception as e:
            self.logger.error(f"Activation propagation test failed: {e}")
            self.test_results['activation_propagation'] = {
                'status': 'FAILED',
                'error': str(e)
            }

    def test_semantic_analysis_enhancement(self):
        """测试语义分析增强"""
        try:
            self.logger.info("Testing semantic analysis enhancement...")
            
            start_time = time.time()
            
            # 初始化执行上下文
            self.semantic_analyzer.initialize_execution_context()
            
            # 测试多阶段语义分析
            analysis_result = self.semantic_analyzer.multi_stage_semantic_analysis()
            
            assert analysis_result is not None, "Analysis result should not be None"
            assert 'structural_features' in analysis_result, "Should have structural features"
            assert 'semantic_features' in analysis_result, "Should have semantic features"
            assert 'construction_candidates' in analysis_result, "Should have construction candidates"
            
            execution_time = time.time() - start_time
            
            self.test_results['semantic_analysis'] = {
                'status': 'PASSED',
                'execution_time': execution_time,
                'features_extracted': len(analysis_result.get('construction_candidates', []))
            }
            
            self.logger.info(f"Semantic analysis test passed in {execution_time:.3f}s")
            
        except Exception as e:
            self.logger.error(f"Semantic analysis test failed: {e}")
            self.test_results['semantic_analysis'] = {
                'status': 'FAILED',
                'error': str(e)
            }

    def test_search_mechanism_enhancement(self):
        """测试搜索机制增强"""
        try:
            self.logger.info("Testing search mechanism enhancement...")
            
            start_time = time.time()
            
            # 创建测试搜索目标
            from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeImpl import NodeImpl
            search_targets = []
            for i in range(3):
                node = NodeImpl()
                node.set_node_name(f"搜索目标_{i}")
                search_targets.append(node)
            
            # 创建搜索任务
            search_task = SearchSB(search_targets, "concept", self.pam)
            
            # 测试多策略搜索
            search_results = search_task.multi_strategy_search()
            
            assert isinstance(search_results, dict), "Search results should be a dictionary"
            
            # 测试结果合并和排序
            final_results = search_task.merge_and_rank_results(search_results)
            
            execution_time = time.time() - start_time
            
            self.test_results['search_mechanism'] = {
                'status': 'PASSED',
                'execution_time': execution_time,
                'strategies_used': len(search_results),
                'final_results_count': len(final_results)
            }
            
            self.logger.info(f"Search mechanism test passed in {execution_time:.3f}s")
            
        except Exception as e:
            self.logger.error(f"Search mechanism test failed: {e}")
            self.test_results['search_mechanism'] = {
                'status': 'FAILED',
                'error': str(e)
            }

    def test_nars_lida_integration(self):
        """测试NARS-LIDA集成"""
        try:
            self.logger.info("Testing NARS-LIDA integration...")
            
            start_time = time.time()
            
            # 创建推理上下文
            context = self.nars_lida_bridge.create_reasoning_context("test_context")
            assert context is not None, "Reasoning context should be created"
            
            # 测试转换功能（模拟）
            from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeImpl import NodeImpl
            test_node = NodeImpl()
            test_node.set_node_name("测试概念")
            
            # 测试LIDA到NARS转换
            nars_term = self.nars_lida_bridge.lida_to_nars_conversion(test_node, context.context_id)
            
            # 测试双向推理搜索
            start_terms = ["概念A"]
            goal_terms = ["概念B"]
            
            bidirectional_result = self.nars_lida_bridge.bidirectional_reasoning_search(
                start_terms, goal_terms, context.context_id
            )
            
            assert isinstance(bidirectional_result, dict), "Bidirectional result should be a dictionary"
            
            execution_time = time.time() - start_time
            
            self.test_results['nars_lida_integration'] = {
                'status': 'PASSED',
                'execution_time': execution_time,
                'context_created': context.context_id,
                'reasoning_paths': len(bidirectional_result.get('reasoning_paths', []))
            }
            
            self.logger.info(f"NARS-LIDA integration test passed in {execution_time:.3f}s")
            
        except Exception as e:
            self.logger.error(f"NARS-LIDA integration test failed: {e}")
            self.test_results['nars_lida_integration'] = {
                'status': 'FAILED',
                'error': str(e)
            }

    def test_neo4j_enhancement(self):
        """测试Neo4j增强功能"""
        try:
            self.logger.info("Testing Neo4j enhancement...")
            
            start_time = time.time()
            
            # 测试缓存功能
            test_query = "RETURN 'test' as result"
            
            # 第一次查询（应该缓存）
            result1 = self.enhanced_neo_util.execute_query(test_query, use_cache=True)
            
            # 第二次查询（应该从缓存获取）
            result2 = self.enhanced_neo_util.execute_query(test_query, use_cache=True)
            
            # 测试批量查询
            batch_queries = [
                ("RETURN 1 as num", {}),
                ("RETURN 2 as num", {}),
                ("RETURN 3 as num", {})
            ]
            
            batch_results = self.enhanced_neo_util.batch_execute_queries(batch_queries)
            
            # 测试语义搜索
            search_results = self.enhanced_neo_util.semantic_search(["测试"], use_cache=True)
            
            # 获取统计信息
            stats = self.enhanced_neo_util.get_statistics()
            
            execution_time = time.time() - start_time
            
            self.test_results['neo4j_enhancement'] = {
                'status': 'PASSED',
                'execution_time': execution_time,
                'cache_hits': stats.get('cache_hits', 0),
                'batch_results_count': len(batch_results),
                'search_results_count': len(search_results)
            }
            
            self.logger.info(f"Neo4j enhancement test passed in {execution_time:.3f}s")
            
        except Exception as e:
            self.logger.error(f"Neo4j enhancement test failed: {e}")
            self.test_results['neo4j_enhancement'] = {
                'status': 'FAILED',
                'error': str(e)
            }

    def test_integrated_workflow(self):
        """测试集成工作流"""
        try:
            self.logger.info("Testing integrated workflow...")
            
            start_time = time.time()
            
            # 模拟完整的认知处理流程
            
            # 1. 激活扩散
            from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeImpl import NodeImpl
            input_node = NodeImpl()
            input_node.set_node_name("输入_开心的人")
            input_node.setActivation(0.9)
            
            # 触发激活扩散
            activated_nodes = [input_node]
            self.pam.integrate_with_search_mechanism(activated_nodes)
            
            # 2. 语义分析
            analysis_result = self.semantic_analyzer.multi_stage_semantic_analysis()
            
            # 3. 搜索相关概念
            if analysis_result and analysis_result.get('construction_candidates'):
                search_task = SearchSB(
                    analysis_result['construction_candidates'][:2], 
                    "integrated", 
                    self.pam
                )
                search_results = search_task.multi_strategy_search()
            
            # 4. NARS推理
            reasoning_context = self.nars_lida_bridge.create_reasoning_context("integrated_test")
            
            # 5. Neo4j查询
            related_nodes = self.enhanced_neo_util.semantic_search(["开心", "人"], use_cache=True)
            
            execution_time = time.time() - start_time
            
            self.test_results['integrated_workflow'] = {
                'status': 'PASSED',
                'execution_time': execution_time,
                'workflow_steps_completed': 5,
                'related_nodes_found': len(related_nodes)
            }
            
            self.logger.info(f"Integrated workflow test passed in {execution_time:.3f}s")
            
        except Exception as e:
            self.logger.error(f"Integrated workflow test failed: {e}")
            self.test_results['integrated_workflow'] = {
                'status': 'FAILED',
                'error': str(e)
            }

    def run_all_tests(self):
        """运行所有测试"""
        try:
            self.logger.info("Starting comprehensive integration tests...")
            
            overall_start_time = time.time()
            
            # 运行各个测试
            self.test_activation_propagation_enhancement()
            self.test_semantic_analysis_enhancement()
            self.test_search_mechanism_enhancement()
            self.test_nars_lida_integration()
            self.test_neo4j_enhancement()
            self.test_integrated_workflow()
            
            overall_execution_time = time.time() - overall_start_time
            
            # 生成测试报告
            self.generate_test_report(overall_execution_time)
            
        except Exception as e:
            self.logger.error(f"Error running integration tests: {e}")

    def generate_test_report(self, total_time: float):
        """生成测试报告"""
        try:
            passed_tests = sum(1 for result in self.test_results.values() if result.get('status') == 'PASSED')
            total_tests = len(self.test_results)
            
            print("\n" + "="*80)
            print("LINARS Python版本优化完善 - 集成测试报告")
            print("="*80)
            print(f"总测试数: {total_tests}")
            print(f"通过测试: {passed_tests}")
            print(f"失败测试: {total_tests - passed_tests}")
            print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
            print(f"总执行时间: {total_time:.3f}秒")
            print("-"*80)
            
            for test_name, result in self.test_results.items():
                status = result.get('status', 'UNKNOWN')
                exec_time = result.get('execution_time', 0)
                
                print(f"{test_name:30} | {status:8} | {exec_time:8.3f}s")
                
                if status == 'FAILED':
                    error = result.get('error', 'Unknown error')
                    print(f"  错误: {error}")
                elif status == 'PASSED':
                    # 显示一些关键指标
                    for key, value in result.items():
                        if key not in ['status', 'execution_time', 'error']:
                            print(f"  {key}: {value}")
            
            print("="*80)
            
            if passed_tests == total_tests:
                print("🎉 所有测试通过！Python版本优化完善成功！")
            else:
                print(f"⚠️  {total_tests - passed_tests} 个测试失败，需要进一步调试")
            
            print("="*80)
            
        except Exception as e:
            self.logger.error(f"Error generating test report: {e}")


def main():
    """主函数"""
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 运行集成测试
    integration_test = IntegrationTest()
    integration_test.run_all_tests()


if __name__ == "__main__":
    main()
