"""
图式执行引擎 - 统一的可执行图式执行系统
"""

import asyncio
import logging
import time
import uuid
from abc import ABC, abstractmethod
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Callable, Union
from enum import Enum

from .TreeChart import TreeChart, SchemaType, ExecutionMode, ChartStatus


class ExecutionStatus(Enum):
    """执行状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    SUSPENDED = "suspended"


@dataclass
class ExecutionContext:
    """执行上下文"""
    context_id: str
    variables: Dict[str, Any] = field(default_factory=dict)
    execution_stack: List[Dict[str, Any]] = field(default_factory=list)
    execution_mode: ExecutionMode = ExecutionMode.ADAPTIVE
    start_time: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)
    parent_context: Optional['ExecutionContext'] = None
    child_contexts: List['ExecutionContext'] = field(default_factory=list)
    
    def create_child_context(self, child_id: str = None) -> 'ExecutionContext':
        """创建子上下文"""
        child_id = child_id or str(uuid.uuid4())
        child = ExecutionContext(
            context_id=child_id,
            variables=self.variables.copy(),  # 继承变量
            execution_mode=self.execution_mode,
            parent_context=self
        )
        self.child_contexts.append(child)
        return child
    
    def set_variable(self, name: str, value: Any):
        """设置变量"""
        self.variables[name] = value
    
    def get_variable(self, name: str, default: Any = None) -> Any:
        """获取变量，支持作用域查找"""
        if name in self.variables:
            return self.variables[name]
        elif self.parent_context:
            return self.parent_context.get_variable(name, default)
        else:
            return default
    
    def push_execution(self, schema: TreeChart):
        """压入执行栈"""
        self.execution_stack.append({
            'schema': schema,
            'start_time': time.time(),
            'status': ExecutionStatus.RUNNING.value
        })
    
    def pop_execution(self) -> Optional[Dict[str, Any]]:
        """弹出执行栈"""
        if self.execution_stack:
            return self.execution_stack.pop()
        return None
    
    def get_execution_depth(self) -> int:
        """获取执行深度"""
        return len(self.execution_stack)


@dataclass
class ExecutionResult:
    """执行结果"""
    success: bool
    result: Any = None
    error: Exception = None
    execution_time: float = 0.0
    status: ExecutionStatus = ExecutionStatus.PENDING
    metadata: Dict[str, Any] = field(default_factory=dict)
    partial_results: Dict[str, Any] = field(default_factory=dict)
    
    def merge(self, other: 'ExecutionResult'):
        """合并执行结果"""
        if other.success and not self.success:
            self.success = True
        
        if other.partial_results:
            self.partial_results.update(other.partial_results)
        
        self.execution_time += other.execution_time
        
        # 合并元数据
        if other.metadata:
            self.metadata.update(other.metadata)


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.execution_stats = {}
        self.active_executions = {}
    
    def start_execution(self, schema_id: str):
        """开始执行监控"""
        self.active_executions[schema_id] = time.time()
    
    def end_execution(self, schema_id: str, success: bool):
        """结束执行监控"""
        if schema_id in self.active_executions:
            execution_time = time.time() - self.active_executions[schema_id]
            del self.active_executions[schema_id]
            
            if schema_id not in self.execution_stats:
                self.execution_stats[schema_id] = {
                    'total_executions': 0,
                    'successful_executions': 0,
                    'failed_executions': 0,
                    'total_time': 0.0,
                    'average_time': 0.0
                }
            
            stats = self.execution_stats[schema_id]
            stats['total_executions'] += 1
            stats['total_time'] += execution_time
            stats['average_time'] = stats['total_time'] / stats['total_executions']
            
            if success:
                stats['successful_executions'] += 1
            else:
                stats['failed_executions'] += 1
    
    def get_stats(self, schema_id: str = None) -> Dict[str, Any]:
        """获取统计信息"""
        if schema_id:
            return self.execution_stats.get(schema_id, {})
        return self.execution_stats


class ActionHandler(ABC):
    """动作处理器抽象基类"""
    
    @abstractmethod
    async def execute(self, action_params: Dict[str, Any], context: ExecutionContext) -> Any:
        """执行动作"""
        pass
    
    @abstractmethod
    def validate_params(self, action_params: Dict[str, Any]) -> bool:
        """验证参数"""
        pass


class DefaultActionHandler(ActionHandler):
    """默认动作处理器"""
    
    async def execute(self, action_params: Dict[str, Any], context: ExecutionContext) -> Any:
        """执行默认动作"""
        action_type = action_params.get('action_type', 'default')
        
        if action_type == 'print':
            message = action_params.get('message', 'Hello World')
            print(f"[Action] {message}")
            return message
        elif action_type == 'set_variable':
            var_name = action_params.get('variable_name')
            var_value = action_params.get('variable_value')
            if var_name:
                context.set_variable(var_name, var_value)
                return f"Set {var_name} = {var_value}"
        elif action_type == 'get_variable':
            var_name = action_params.get('variable_name')
            if var_name:
                return context.get_variable(var_name)
        else:
            return f"Executed action: {action_type}"
    
    def validate_params(self, action_params: Dict[str, Any]) -> bool:
        """验证参数"""
        return isinstance(action_params, dict)


class SchemaExecutor:
    """图式执行器"""
    
    def __init__(self, max_workers: int = 4, cache_size: int = 1000):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.action_handlers = {}
        self.execution_cache = {}  # 执行结果缓存
        self.cache_size = cache_size
        self.performance_monitor = PerformanceMonitor()
        
        # 注册默认动作处理器
        self.register_action_handler('default', DefaultActionHandler())
        self.register_action_handler('print', DefaultActionHandler())
        self.register_action_handler('set_variable', DefaultActionHandler())
        self.register_action_handler('get_variable', DefaultActionHandler())
        
        # 执行统计
        self.execution_stats = {
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'average_execution_time': 0.0,
            'cache_hits': 0,
            'cache_misses': 0
        }
    
    def register_action_handler(self, action_type: str, handler: ActionHandler):
        """注册动作处理器"""
        self.action_handlers[action_type] = handler
        self.logger.info(f"Registered action handler for type: {action_type}")
    
    async def execute(self, schema: TreeChart, context: ExecutionContext = None) -> ExecutionResult:
        """
        执行图式
        
        Args:
            schema: 要执行的图式
            context: 执行上下文
            
        Returns:
            执行结果
        """
        if context is None:
            context = ExecutionContext(context_id=str(uuid.uuid4()))
        
        start_time = time.time()
        
        # 检查缓存
        cache_key = self._generate_cache_key(schema, context)
        if cache_key in self.execution_cache:
            self.execution_stats['cache_hits'] += 1
            cached_result = self.execution_cache[cache_key]
            cached_result.metadata['from_cache'] = True
            return cached_result
        
        self.execution_stats['cache_misses'] += 1
        context.push_execution(schema)
        
        try:
            # 验证图式
            is_valid, errors = schema.validate()
            if not is_valid:
                raise ValueError(f"Schema validation failed: {errors}")
            
            # 标记为执行中
            schema.mark_as_executing()
            
            # 性能监控开始
            self.performance_monitor.start_execution(schema.chart_id)
            
            # 根据执行模式选择执行策略
            if context.execution_mode == ExecutionMode.MACHINE:
                result = await self._execute_precise(schema, context)
            elif context.execution_mode == ExecutionMode.HUMAN:
                result = await self._execute_fuzzy(schema, context)
            else:
                result = await self._execute_adaptive(schema, context)
            
            result.execution_time = time.time() - start_time
            result.metadata['execution_mode'] = context.execution_mode.value
            result.metadata['schema_type'] = schema.schema_type.value
            result.status = ExecutionStatus.COMPLETED if result.success else ExecutionStatus.FAILED
            
            # 更新图式状态
            if result.success:
                schema.mark_as_completed()
            else:
                schema.mark_as_failed(str(result.error) if result.error else "Unknown error")
            
            # 更新执行统计
            schema.update_execution_stats(result.success, result.execution_time)
            
            # 缓存结果
            self._cache_result(cache_key, result)
            
            # 更新统计
            self._update_stats(result)
            
            # 性能监控结束
            self.performance_monitor.end_execution(schema.chart_id, result.success)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error executing schema {schema.chart_id}: {e}")
            result = ExecutionResult(False, error=e, status=ExecutionStatus.FAILED)
            result.execution_time = time.time() - start_time
            result.metadata['execution_mode'] = context.execution_mode.value
            result.metadata['schema_type'] = schema.schema_type.value
            
            schema.mark_as_failed(str(e))
            schema.update_execution_stats(False, result.execution_time)
            
            self._update_stats(result)
            self.performance_monitor.end_execution(schema.chart_id, False)
            
            return result
        finally:
            context.pop_execution()
    
    def _generate_cache_key(self, schema: TreeChart, context: ExecutionContext) -> str:
        """生成缓存键"""
        # 简化的缓存键生成，实际应用中可能需要更复杂的逻辑
        return f"{schema.chart_id}_{hash(str(sorted(context.variables.items())))}"
    
    def _cache_result(self, cache_key: str, result: ExecutionResult):
        """缓存结果"""
        if len(self.execution_cache) >= self.cache_size:
            # 简单的LRU策略：移除最旧的缓存项
            oldest_key = next(iter(self.execution_cache))
            del self.execution_cache[oldest_key]
        
        # 只缓存成功的结果
        if result.success:
            self.execution_cache[cache_key] = result
    
    def _update_stats(self, result: ExecutionResult):
        """更新执行统计"""
        self.execution_stats['total_executions'] += 1
        if result.success:
            self.execution_stats['successful_executions'] += 1
        else:
            self.execution_stats['failed_executions'] += 1
        
        # 更新平均执行时间
        total = self.execution_stats['total_executions']
        current_avg = self.execution_stats['average_execution_time']
        self.execution_stats['average_execution_time'] = (
            (current_avg * (total - 1) + result.execution_time) / total
        )

    async def _execute_precise(self, schema: TreeChart, context: ExecutionContext) -> ExecutionResult:
        """精确执行模式（机算模式）"""
        if schema.schema_type == SchemaType.SEQUENCE:
            return await self._execute_sequence_precise(schema, context)
        elif schema.schema_type == SchemaType.CONDITION:
            return await self._execute_condition_precise(schema, context)
        elif schema.schema_type == SchemaType.LOOP:
            return await self._execute_loop_precise(schema, context)
        elif schema.schema_type == SchemaType.ACTION:
            return await self._execute_action_precise(schema, context)
        elif schema.schema_type == SchemaType.SCENE:
            return await self._execute_scene_precise(schema, context)
        else:
            raise ValueError(f"Unsupported schema type: {schema.schema_type}")

    async def _execute_fuzzy(self, schema: TreeChart, context: ExecutionContext) -> ExecutionResult:
        """模糊执行模式（人算模式）"""
        # 人算模式允许部分失败，具有容错能力
        try:
            return await self._execute_precise(schema, context)
        except Exception as e:
            # 在人算模式下，尝试降级执行或部分执行
            self.logger.warning(f"Fuzzy execution fallback for {schema.chart_id}: {e}")
            return await self._execute_fallback(schema, context, e)

    async def _execute_adaptive(self, schema: TreeChart, context: ExecutionContext) -> ExecutionResult:
        """自适应执行模式"""
        # 根据图式复杂度和上下文选择最适合的执行模式
        complexity = schema.metadata.complexity

        if complexity > 0.8:
            context.execution_mode = ExecutionMode.HUMAN
            return await self._execute_fuzzy(schema, context)
        else:
            context.execution_mode = ExecutionMode.MACHINE
            return await self._execute_precise(schema, context)

    async def _execute_sequence_precise(self, schema: TreeChart, context: ExecutionContext) -> ExecutionResult:
        """精确执行顺序图式"""
        results = []

        for i, step in enumerate(schema.sub_charts):
            step_context = context.create_child_context(f"{context.context_id}_step_{i}")
            step_result = await self.execute(step, step_context)
            results.append(step_result)

            if not step_result.success:
                # 如果不允许部分失败，则立即返回失败
                if not schema.execution_constraints.get('allow_partial_failure', False):
                    return ExecutionResult(False, error=step_result.error,
                                         partial_results={'completed_steps': results})

        # 检查是否有足够的成功步骤
        successful_steps = sum(1 for r in results if r.success)
        min_success_rate = schema.execution_constraints.get('min_success_rate', 1.0)
        required_successes = len(results) * min_success_rate

        success = successful_steps >= required_successes
        return ExecutionResult(success, result=results,
                             partial_results={'successful_steps': successful_steps})

    async def _execute_condition_precise(self, schema: TreeChart, context: ExecutionContext) -> ExecutionResult:
        """精确执行条件图式"""
        if len(schema.sub_charts) < 2:
            return ExecutionResult(False, error=ValueError("Condition schema requires at least 2 sub-charts"))

        condition_schema = schema.sub_charts[0]
        then_branch = schema.sub_charts[1]
        else_branch = schema.sub_charts[2] if len(schema.sub_charts) > 2 else None

        # 执行条件判断
        condition_context = context.create_child_context(f"{context.context_id}_condition")
        condition_result = await self.execute(condition_schema, condition_context)

        if not condition_result.success:
            # 如果条件执行失败，根据配置选择默认分支
            default_branch = schema.execution_constraints.get('default_branch', 'then')
            if default_branch == 'then':
                branch_context = context.create_child_context(f"{context.context_id}_then")
                return await self.execute(then_branch, branch_context)
            elif default_branch == 'else' and else_branch:
                branch_context = context.create_child_context(f"{context.context_id}_else")
                return await self.execute(else_branch, branch_context)
            else:
                return ExecutionResult(False, error=condition_result.error)

        # 根据条件结果选择分支
        if self._evaluate_condition(condition_result.result):
            branch_context = context.create_child_context(f"{context.context_id}_then")
            return await self.execute(then_branch, branch_context)
        elif else_branch:
            branch_context = context.create_child_context(f"{context.context_id}_else")
            return await self.execute(else_branch, branch_context)
        else:
            return ExecutionResult(True, result=None)

    async def _execute_loop_precise(self, schema: TreeChart, context: ExecutionContext) -> ExecutionResult:
        """精确执行循环图式"""
        if len(schema.sub_charts) < 2:
            return ExecutionResult(False, error=ValueError("Loop schema requires at least 2 sub-charts"))

        condition_schema = schema.sub_charts[0]
        body_schema = schema.sub_charts[1]

        results = []
        max_iterations = schema.execution_constraints.get('max_iterations', 1000)
        loop_type = schema.get_parameter('loop_type', 'do_while')
        iteration = 0

        if loop_type == "do_while":
            # do-while循环：先执行一次循环体
            while iteration < max_iterations:
                # 执行循环体
                body_context = context.create_child_context(f"{context.context_id}_body_{iteration}")
                body_result = await self.execute(body_schema, body_context)
                results.append(body_result)
                iteration += 1

                if not body_result.success:
                    break

                # 检查循环条件
                condition_context = context.create_child_context(f"{context.context_id}_condition_{iteration}")
                condition_result = await self.execute(condition_schema, condition_context)
                if not condition_result.success or not self._evaluate_condition(condition_result.result):
                    break

        elif loop_type == "while":
            # while循环：先检查条件
            while iteration < max_iterations:
                condition_context = context.create_child_context(f"{context.context_id}_condition_{iteration}")
                condition_result = await self.execute(condition_schema, condition_context)
                if not condition_result.success or not self._evaluate_condition(condition_result.result):
                    break

                body_context = context.create_child_context(f"{context.context_id}_body_{iteration}")
                body_result = await self.execute(body_schema, body_context)
                results.append(body_result)
                iteration += 1

                if not body_result.success:
                    break

        return ExecutionResult(True, result=results,
                             partial_results={'iterations': iteration, 'results': results})

    async def _execute_action_precise(self, schema: TreeChart, context: ExecutionContext) -> ExecutionResult:
        """精确执行动作图式"""
        action_type = schema.get_parameter('action_type', 'default')
        action_params = schema.get_parameter('action_params', {})

        if action_type not in self.action_handlers:
            return ExecutionResult(False, error=ValueError(f"Unknown action type: {action_type}"))

        handler = self.action_handlers[action_type]

        # 验证参数
        if not handler.validate_params(action_params):
            return ExecutionResult(False, error=ValueError(f"Invalid parameters for action type: {action_type}"))

        try:
            # 执行动作
            result = await handler.execute(action_params, context)
            return ExecutionResult(True, result=result)
        except Exception as e:
            return ExecutionResult(False, error=e)

    async def _execute_scene_precise(self, schema: TreeChart, context: ExecutionContext) -> ExecutionResult:
        """精确执行场景图式"""
        parallel_execution = schema.get_parameter('parallel_execution', True)

        if parallel_execution:
            # 并行执行所有场景元素
            tasks = []
            for i, element in enumerate(schema.sub_charts):
                element_context = context.create_child_context(f"{context.context_id}_element_{i}")
                task = asyncio.create_task(self.execute(element, element_context))
                tasks.append(task)

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果
            successful_results = []
            failed_results = []

            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    failed_results.append(ExecutionResult(False, error=result))
                elif isinstance(result, ExecutionResult):
                    if result.success:
                        successful_results.append(result)
                    else:
                        failed_results.append(result)

            # 判断整体成功
            min_success_rate = schema.execution_constraints.get('min_success_rate', 0.5)
            required_successes = len(schema.sub_charts) * min_success_rate
            success = len(successful_results) >= required_successes

            return ExecutionResult(success, result=successful_results,
                                 partial_results={'successful': len(successful_results),
                                                'failed': len(failed_results)})
        else:
            # 顺序执行场景元素
            return await self._execute_sequence_precise(schema, context)

    async def _execute_fallback(self, schema: TreeChart, context: ExecutionContext,
                               original_error: Exception) -> ExecutionResult:
        """降级执行"""
        # 在人算模式下的降级策略
        if schema.schema_type == SchemaType.SEQUENCE:
            # 顺序图式：尝试执行部分步骤
            return await self._execute_sequence_partial(schema, context)
        elif schema.schema_type == SchemaType.CONDITION:
            # 条件图式：使用默认分支
            return await self._execute_condition_default(schema, context)
        elif schema.schema_type == SchemaType.SCENE:
            # 场景图式：尝试部分执行
            return await self._execute_scene_partial(schema, context)
        else:
            return ExecutionResult(False, error=original_error)

    async def _execute_sequence_partial(self, schema: TreeChart, context: ExecutionContext) -> ExecutionResult:
        """部分执行顺序图式"""
        results = []
        successful_steps = 0

        for i, step in enumerate(schema.sub_charts):
            try:
                step_context = context.create_child_context(f"{context.context_id}_step_{i}")
                step_result = await self.execute(step, step_context)
                results.append(step_result)
                if step_result.success:
                    successful_steps += 1
            except Exception as e:
                results.append(ExecutionResult(False, error=e))

        # 如果至少有一半步骤成功，认为部分成功
        min_success_rate = schema.execution_constraints.get('min_success_rate', 0.5)
        required_successes = len(schema.sub_charts) * min_success_rate
        success = successful_steps >= required_successes

        return ExecutionResult(success, result=results,
                             partial_results={'successful_steps': successful_steps})

    async def _execute_condition_default(self, schema: TreeChart, context: ExecutionContext) -> ExecutionResult:
        """使用默认分支执行条件图式"""
        if len(schema.sub_charts) < 2:
            return ExecutionResult(False, error=ValueError("Condition schema requires at least 2 sub-charts"))

        # 在无法判断条件时，优先执行then分支
        then_branch = schema.sub_charts[1]
        else_branch = schema.sub_charts[2] if len(schema.sub_charts) > 2 else None

        try:
            branch_context = context.create_child_context(f"{context.context_id}_then_default")
            return await self.execute(then_branch, branch_context)
        except Exception:
            if else_branch:
                branch_context = context.create_child_context(f"{context.context_id}_else_default")
                return await self.execute(else_branch, branch_context)
            else:
                return ExecutionResult(True, result=None)

    async def _execute_scene_partial(self, schema: TreeChart, context: ExecutionContext) -> ExecutionResult:
        """部分执行场景图式"""
        results = []
        successful_elements = 0

        for i, element in enumerate(schema.sub_charts):
            try:
                element_context = context.create_child_context(f"{context.context_id}_element_{i}")
                element_result = await self.execute(element, element_context)
                results.append(element_result)
                if element_result.success:
                    successful_elements += 1
            except Exception as e:
                results.append(ExecutionResult(False, error=e))

        # 如果至少有一半元素成功，认为部分成功
        min_success_rate = schema.execution_constraints.get('min_success_rate', 0.5)
        required_successes = len(schema.sub_charts) * min_success_rate
        success = successful_elements >= required_successes

        return ExecutionResult(success, result=results,
                             partial_results={'successful_elements': successful_elements})

    def _evaluate_condition(self, condition_result: Any) -> bool:
        """评估条件结果"""
        if isinstance(condition_result, bool):
            return condition_result
        elif isinstance(condition_result, (int, float)):
            return condition_result > 0
        elif isinstance(condition_result, str):
            return condition_result.lower() in ['true', 'yes', '1', 'success']
        elif condition_result is None:
            return False
        else:
            return bool(condition_result)

    def get_execution_stats(self) -> Dict[str, Any]:
        """获取执行统计"""
        return {
            **self.execution_stats,
            'performance_stats': self.performance_monitor.get_stats(),
            'cache_size': len(self.execution_cache),
            'active_executions': len(self.performance_monitor.active_executions)
        }

    def clear_cache(self):
        """清空缓存"""
        self.execution_cache.clear()
        self.execution_stats['cache_hits'] = 0
        self.execution_stats['cache_misses'] = 0

    def shutdown(self):
        """关闭执行器"""
        self.executor.shutdown(wait=True)
        self.clear_cache()


# 全局执行器实例
schema_executor = SchemaExecutor()
