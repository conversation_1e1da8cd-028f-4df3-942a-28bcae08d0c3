"""
图式工厂类 - 用于创建不同类型的可执行图式
"""

import logging
from typing import Dict, List, Any, Optional, Union
from .TreeChart import TreeChart, SchemaType, ExecutionMode, ChartStatus
from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.linars.term import Term


class SchemaFactory:
    """
    图式工厂类，负责创建和管理不同类型的可执行图式
    """
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.schema_templates = {}
        self.validation_rules = {}
        self._register_default_templates()
    
    def _register_default_templates(self):
        """注册默认的图式模板"""
        # 顺序图式模板
        self.schema_templates[SchemaType.SEQUENCE] = {
            'min_sub_charts': 2,
            'max_sub_charts': 100,
            'default_constraints': {
                'max_steps': 100,
                'timeout': 300,
                'allow_partial_failure': False
            }
        }
        
        # 条件图式模板
        self.schema_templates[SchemaType.CONDITION] = {
            'min_sub_charts': 2,
            'max_sub_charts': 3,  # condition, then, else(optional)
            'default_constraints': {
                'condition_timeout': 30,
                'branch_timeout': 300,
                'default_branch': 'then'
            }
        }
        
        # 循环图式模板
        self.schema_templates[SchemaType.LOOP] = {
            'min_sub_charts': 2,
            'max_sub_charts': 2,  # condition, body
            'default_constraints': {
                'max_iterations': 1000,
                'condition_timeout': 30,
                'body_timeout': 300,
                'loop_type': 'do_while'
            }
        }
        
        # 动作图式模板
        self.schema_templates[SchemaType.ACTION] = {
            'min_sub_charts': 0,
            'max_sub_charts': 0,
            'default_constraints': {
                'action_timeout': 60,
                'retry_count': 3,
                'retry_delay': 1.0
            }
        }
        
        # 场景图式模板
        self.schema_templates[SchemaType.SCENE] = {
            'min_sub_charts': 1,
            'max_sub_charts': 50,
            'default_constraints': {
                'scene_timeout': 600,
                'parallel_execution': True,
                'max_parallel_tasks': 10
            }
        }
    
    def create_sequence_schema(self, steps: List[TreeChart], 
                             schema_id: str = None,
                             execution_mode: ExecutionMode = ExecutionMode.ADAPTIVE,
                             **kwargs) -> TreeChart:
        """
        创建顺序执行图式
        
        Args:
            steps: 执行步骤列表
            schema_id: 图式ID
            execution_mode: 执行模式
            **kwargs: 其他参数
            
        Returns:
            顺序执行图式
        """
        if len(steps) < 2:
            raise ValueError("Sequence schema requires at least 2 steps")
        
        schema = TreeChart(
            schema_type=SchemaType.SEQUENCE,
            execution_mode=execution_mode,
            chart_id=schema_id
        )
        
        # 添加步骤
        for step in steps:
            schema.add_sub_chart(step)
        
        # 设置约束
        template = self.schema_templates[SchemaType.SEQUENCE]
        schema.execution_constraints.update(template['default_constraints'])
        schema.execution_constraints.update(kwargs)
        
        # 设置参数
        schema.set_parameter('step_count', len(steps))
        schema.set_parameter('execution_order', 'sequential')
        
        schema.mark_as_ready()
        return schema
    
    def create_condition_schema(self, condition: TreeChart, 
                              then_branch: TreeChart,
                              else_branch: TreeChart = None,
                              schema_id: str = None,
                              execution_mode: ExecutionMode = ExecutionMode.ADAPTIVE,
                              **kwargs) -> TreeChart:
        """
        创建条件执行图式
        
        Args:
            condition: 条件图式
            then_branch: then分支
            else_branch: else分支（可选）
            schema_id: 图式ID
            execution_mode: 执行模式
            **kwargs: 其他参数
            
        Returns:
            条件执行图式
        """
        schema = TreeChart(
            schema_type=SchemaType.CONDITION,
            execution_mode=execution_mode,
            chart_id=schema_id
        )
        
        # 添加分支
        schema.add_sub_chart(condition)
        schema.add_sub_chart(then_branch)
        if else_branch:
            schema.add_sub_chart(else_branch)
        
        # 设置约束
        template = self.schema_templates[SchemaType.CONDITION]
        schema.execution_constraints.update(template['default_constraints'])
        schema.execution_constraints.update(kwargs)
        
        # 设置参数
        schema.set_parameter('has_else_branch', else_branch is not None)
        schema.set_parameter('condition_type', 'boolean')
        
        schema.mark_as_ready()
        return schema
    
    def create_loop_schema(self, condition: TreeChart,
                          body: TreeChart,
                          loop_type: str = 'do_while',
                          schema_id: str = None,
                          execution_mode: ExecutionMode = ExecutionMode.ADAPTIVE,
                          **kwargs) -> TreeChart:
        """
        创建循环执行图式
        
        Args:
            condition: 循环条件
            body: 循环体
            loop_type: 循环类型 ('do_while', 'while', 'for')
            schema_id: 图式ID
            execution_mode: 执行模式
            **kwargs: 其他参数
            
        Returns:
            循环执行图式
        """
        if loop_type not in ['do_while', 'while', 'for']:
            raise ValueError(f"Unsupported loop type: {loop_type}")
        
        schema = TreeChart(
            schema_type=SchemaType.LOOP,
            execution_mode=execution_mode,
            chart_id=schema_id
        )
        
        # 添加条件和循环体
        schema.add_sub_chart(condition)
        schema.add_sub_chart(body)
        
        # 设置约束
        template = self.schema_templates[SchemaType.LOOP]
        schema.execution_constraints.update(template['default_constraints'])
        schema.execution_constraints['loop_type'] = loop_type
        schema.execution_constraints.update(kwargs)
        
        # 设置参数
        schema.set_parameter('loop_type', loop_type)
        schema.set_parameter('max_iterations', kwargs.get('max_iterations', 1000))
        
        schema.mark_as_ready()
        return schema
    
    def create_action_schema(self, action_type: str,
                           action_params: Dict[str, Any] = None,
                           schema_id: str = None,
                           execution_mode: ExecutionMode = ExecutionMode.ADAPTIVE,
                           **kwargs) -> TreeChart:
        """
        创建动作执行图式
        
        Args:
            action_type: 动作类型
            action_params: 动作参数
            schema_id: 图式ID
            execution_mode: 执行模式
            **kwargs: 其他参数
            
        Returns:
            动作执行图式
        """
        schema = TreeChart(
            schema_type=SchemaType.ACTION,
            execution_mode=execution_mode,
            chart_id=schema_id
        )
        
        # 设置约束
        template = self.schema_templates[SchemaType.ACTION]
        schema.execution_constraints.update(template['default_constraints'])
        schema.execution_constraints.update(kwargs)
        
        # 设置参数
        schema.set_parameter('action_type', action_type)
        schema.set_parameter('action_params', action_params or {})
        
        schema.mark_as_ready()
        return schema
    
    def create_scene_schema(self, scene_elements: List[TreeChart],
                          scene_root: Term = None,
                          schema_id: str = None,
                          execution_mode: ExecutionMode = ExecutionMode.ADAPTIVE,
                          **kwargs) -> TreeChart:
        """
        创建场景执行图式
        
        Args:
            scene_elements: 场景元素列表
            scene_root: 场景根节点
            schema_id: 图式ID
            execution_mode: 执行模式
            **kwargs: 其他参数
            
        Returns:
            场景执行图式
        """
        schema = TreeChart(
            schema_type=SchemaType.SCENE,
            execution_mode=execution_mode,
            chart_id=schema_id,
            scene_root=scene_root
        )
        
        # 添加场景元素
        for element in scene_elements:
            schema.add_sub_chart(element)
        
        # 设置约束
        template = self.schema_templates[SchemaType.SCENE]
        schema.execution_constraints.update(template['default_constraints'])
        schema.execution_constraints.update(kwargs)
        
        # 设置参数
        schema.set_parameter('element_count', len(scene_elements))
        schema.set_parameter('parallel_execution', kwargs.get('parallel_execution', True))
        
        schema.mark_as_ready()
        return schema
    
    def create_from_template(self, schema_type: SchemaType,
                           template_name: str,
                           **kwargs) -> TreeChart:
        """
        从模板创建图式
        
        Args:
            schema_type: 图式类型
            template_name: 模板名称
            **kwargs: 模板参数
            
        Returns:
            创建的图式
        """
        # 这里可以实现从预定义模板创建图式的逻辑
        # 目前返回基本图式
        return TreeChart(
            schema_type=schema_type,
            execution_mode=kwargs.get('execution_mode', ExecutionMode.ADAPTIVE),
            chart_id=kwargs.get('schema_id')
        )
    
    def validate_schema(self, schema: TreeChart) -> tuple[bool, List[str]]:
        """
        验证图式
        
        Args:
            schema: 要验证的图式
            
        Returns:
            (is_valid, error_messages)
        """
        return schema.validate()
    
    def register_template(self, schema_type: SchemaType, template: Dict[str, Any]):
        """注册新的图式模板"""
        self.schema_templates[schema_type] = template
    
    def register_validation_rule(self, schema_type: SchemaType, rule: callable):
        """注册验证规则"""
        if schema_type not in self.validation_rules:
            self.validation_rules[schema_type] = []
        self.validation_rules[schema_type].append(rule)


# 全局工厂实例
schema_factory = SchemaFactory()
