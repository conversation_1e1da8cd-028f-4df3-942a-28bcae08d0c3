"""
图式搜索集成系统测试文件
"""

import asyncio
import logging
import time
from unittest.mock import Mock, MagicMock
from typing import Dict, Any, List

from .SchemaSearchIntegration import (
    SchemaSearchIntegrator, SearchRequest, SearchResult, SearchTriggerType, 
    SearchScope, SchemaBasedSearchStrategy, ActivationGuidedSearchStrategy,
    SemanticSearchStrategy, SearchDrivenSchemaExecutor, search_driven_executor
)
from .TreeChart import TreeChart, SchemaType, ExecutionMode
from .SchemaExecutor import ExecutionContext, ExecutionResult
from .SchemaFactory import schema_factory

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_mock_node(name: str, node_id: int = None, activation: float = 0.5):
    """创建模拟节点"""
    node = Mock()
    node.get_tn_name.return_value = name
    node.get_node_id.return_value = node_id or hash(name)
    node.get_activation.return_value = activation
    return node


def create_mock_pam():
    """创建模拟PAM"""
    pam = Mock()
    
    # 模拟高激活节点
    high_nodes = [
        create_mock_node("高激活节点1", 1, 0.8),
        create_mock_node("高激活节点2", 2, 0.7),
        create_mock_node("搜索目标", 3, 0.9)
    ]
    
    pam.get_high_activation_nodes.return_value = high_nodes
    return pam


async def test_search_strategies():
    """测试搜索策略"""
    print("\n=== 测试搜索策略 ===")
    
    # 创建测试请求
    request = SearchRequest(
        request_id="test_request_1",
        trigger_type=SearchTriggerType.SCHEMA_EXECUTION,
        search_scope=SearchScope.CONTEXTUAL,
        query_terms=["测试", "搜索", "图式"],
        context_data={"test_context": "value"},
        priority=0.7,
        max_results=10
    )
    
    results = []
    
    # 测试基于图式的搜索策略
    print("1. 测试基于图式的搜索策略")
    try:
        schema_strategy = SchemaBasedSearchStrategy()
        schema_result = await schema_strategy.search(request)
        
        print(f"策略名称: {schema_strategy.get_strategy_name()}")
        print(f"相关性: {schema_strategy.calculate_relevance(request)}")
        print(f"搜索结果: {len(schema_result.results)} 个")
        print(f"置信度: {schema_result.confidence}")
        print(f"执行时间: {schema_result.execution_time:.4f}s")
        
        results.append(schema_result.confidence > 0)
        
    except Exception as e:
        logger.error(f"Schema-based strategy test failed: {e}")
        results.append(False)
    
    # 测试激活引导搜索策略
    print("\n2. 测试激活引导搜索策略")
    try:
        mock_pam = create_mock_pam()
        activation_strategy = ActivationGuidedSearchStrategy(mock_pam)
        activation_result = await activation_strategy.search(request)
        
        print(f"策略名称: {activation_strategy.get_strategy_name()}")
        print(f"相关性: {activation_strategy.calculate_relevance(request)}")
        print(f"搜索结果: {len(activation_result.results)} 个")
        print(f"置信度: {activation_result.confidence}")
        print(f"执行时间: {activation_result.execution_time:.4f}s")
        
        results.append(activation_result.confidence >= 0)
        
    except Exception as e:
        logger.error(f"Activation-guided strategy test failed: {e}")
        results.append(False)
    
    # 测试语义搜索策略
    print("\n3. 测试语义搜索策略")
    try:
        semantic_strategy = SemanticSearchStrategy()
        semantic_result = await semantic_strategy.search(request)
        
        print(f"策略名称: {semantic_strategy.get_strategy_name()}")
        print(f"相关性: {semantic_strategy.calculate_relevance(request)}")
        print(f"搜索结果: {len(semantic_result.results)} 个")
        print(f"置信度: {semantic_result.confidence}")
        print(f"执行时间: {semantic_result.execution_time:.4f}s")
        
        results.append(semantic_result.confidence >= 0)
        
    except Exception as e:
        logger.error(f"Semantic strategy test failed: {e}")
        results.append(False)
    
    return all(results)


async def test_schema_search_integrator():
    """测试图式搜索集成器"""
    print("\n=== 测试图式搜索集成器 ===")
    
    # 创建集成器
    mock_pam = create_mock_pam()
    integrator = SchemaSearchIntegrator(mock_pam)
    
    try:
        # 启动集成器
        await integrator.start()
        
        print("1. 测试基本搜索功能")
        
        # 创建搜索请求
        request = SearchRequest(
            request_id="integrator_test_1",
            trigger_type=SearchTriggerType.SCHEMA_EXECUTION,
            search_scope=SearchScope.GLOBAL,
            query_terms=["集成测试", "搜索"],
            priority=0.8,
            max_results=20
        )
        
        # 执行搜索
        result = await integrator.search(request)
        
        print(f"搜索请求ID: {result.request_id}")
        print(f"结果数量: {len(result.results)}")
        print(f"置信度: {result.confidence}")
        print(f"执行时间: {result.execution_time:.4f}s")
        print(f"元数据: {result.metadata}")
        
        # 测试缓存功能
        print("\n2. 测试缓存功能")
        
        start_time = time.time()
        cached_result = await integrator.search(request)  # 相同请求，应该使用缓存
        cache_time = time.time() - start_time
        
        print(f"缓存查询时间: {cache_time:.4f}s")
        print(f"结果一致性: {result.result_id != cached_result.result_id}")  # 应该是不同的结果ID但内容相同
        
        # 测试不同优先级的请求
        print("\n3. 测试不同优先级的搜索")
        
        high_priority_request = SearchRequest(
            request_id="high_priority_test",
            trigger_type=SearchTriggerType.ERROR_RECOVERY,
            search_scope=SearchScope.SEMANTIC,
            query_terms=["紧急", "恢复"],
            priority=0.9,
            max_results=15
        )
        
        low_priority_request = SearchRequest(
            request_id="low_priority_test",
            trigger_type=SearchTriggerType.ADAPTIVE_OPTIMIZATION,
            search_scope=SearchScope.LOCAL,
            query_terms=["优化", "调整"],
            priority=0.3,
            max_results=5
        )
        
        # 并行执行不同优先级的搜索
        high_result, low_result = await asyncio.gather(
            integrator.search(high_priority_request),
            integrator.search(low_priority_request)
        )
        
        print(f"高优先级搜索: {len(high_result.results)} 结果, 置信度 {high_result.confidence}")
        print(f"低优先级搜索: {len(low_result.results)} 结果, 置信度 {low_result.confidence}")
        
        # 获取统计信息
        stats = integrator.get_statistics()
        print(f"\n4. 集成器统计信息:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        # 停止集成器
        await integrator.stop()
        
        return True
        
    except Exception as e:
        logger.error(f"Schema search integrator test failed: {e}")
        return False


async def test_search_driven_executor():
    """测试搜索驱动的图式执行器"""
    print("\n=== 测试搜索驱动的图式执行器 ===")
    
    try:
        # 创建模拟的搜索集成器
        mock_integrator = Mock()
        
        # 模拟搜索结果
        mock_search_result = SearchResult(
            result_id="mock_result_1",
            request_id="mock_request_1",
            results=[
                {"node": create_mock_node("相关节点1"), "activation": 0.8},
                {"node": create_mock_node("相关节点2"), "activation": 0.6}
            ],
            confidence=0.7,
            execution_time=0.1
        )
        
        mock_integrator.search.return_value = mock_search_result
        
        # 创建搜索驱动执行器
        executor = SearchDrivenSchemaExecutor(mock_integrator)
        
        print("1. 测试带搜索引导的图式执行")
        
        # 创建测试图式
        test_schema = schema_factory.create_action_schema(
            action_type='test_action',
            action_params={'param1': 'value1', 'param2': 'value2'},
            schema_id='test_schema_1'
        )
        
        # 创建执行上下文
        context = ExecutionContext(context_id="test_context_1")
        context.set_variable("test_var", "test_value")
        
        # 执行图式（带搜索引导）
        result = await executor.execute_with_search_guidance(test_schema, context, search_guidance=True)
        
        print(f"执行成功: {result.success}")
        print(f"执行时间: {result.execution_time:.4f}s")
        if result.error:
            print(f"错误信息: {result.error}")
        
        # 检查上下文是否被搜索结果增强
        search_results = context.get_variable('search_results')
        search_confidence = context.get_variable('search_confidence')
        
        print(f"搜索结果已集成: {search_results is not None}")
        print(f"搜索置信度: {search_confidence}")
        
        print("\n2. 测试错误恢复搜索")
        
        # 创建会失败的图式
        failing_schema = schema_factory.create_action_schema(
            action_type='failing_action',
            action_params={'cause_error': True},
            schema_id='failing_schema_1'
        )
        
        # 模拟失败的执行结果
        mock_integrator.search.return_value = SearchResult(
            result_id="recovery_result",
            request_id="recovery_request",
            results=[{"recovery_suggestion": "try_alternative_approach"}],
            confidence=0.6,
            execution_time=0.2
        )
        
        # 执行可能失败的图式
        failing_result = await executor.execute_with_search_guidance(failing_schema, context, search_guidance=True)
        
        print(f"失败处理执行: {failing_result.success}")
        print(f"恢复尝试: {context.get_variable('recovery_attempted', False)}")
        
        print("\n3. 测试执行统计")
        
        # 获取执行统计
        stats = executor.get_execution_statistics()
        
        print("执行统计信息:")
        for key, value in stats.items():
            if key != 'recent_executions':
                print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        logger.error(f"Search-driven executor test failed: {e}")
        return False


async def test_integration_scenarios():
    """测试集成场景"""
    print("\n=== 测试集成场景 ===")
    
    try:
        # 创建完整的集成环境
        mock_pam = create_mock_pam()
        integrator = SchemaSearchIntegrator(mock_pam)
        executor = SearchDrivenSchemaExecutor(integrator)
        
        await integrator.start()
        
        print("1. 测试复杂图式的搜索引导执行")
        
        # 创建复杂的条件图式
        condition_action = schema_factory.create_action_schema(
            action_type='evaluate_condition',
            action_params={'condition': 'complex_condition'},
            schema_id='condition_check'
        )
        
        then_action = schema_factory.create_action_schema(
            action_type='then_branch',
            action_params={'action': 'positive_action'},
            schema_id='then_branch'
        )
        
        else_action = schema_factory.create_action_schema(
            action_type='else_branch',
            action_params={'action': 'negative_action'},
            schema_id='else_branch'
        )
        
        complex_schema = schema_factory.create_condition_schema(
            condition=condition_action,
            then_branch=then_action,
            else_branch=else_action,
            schema_id='complex_condition_schema'
        )
        
        # 创建丰富的执行上下文
        rich_context = ExecutionContext(context_id="rich_context")
        rich_context.set_variable("user_input", "复杂查询")
        rich_context.set_variable("system_state", "active")
        rich_context.set_variable("priority_level", "high")
        
        # 执行复杂图式
        complex_result = await executor.execute_with_search_guidance(complex_schema, rich_context)
        
        print(f"复杂图式执行: {complex_result.success}")
        print(f"执行时间: {complex_result.execution_time:.4f}s")
        
        print("\n2. 测试循环图式的搜索优化")
        
        # 创建循环图式
        loop_body = schema_factory.create_action_schema(
            action_type='loop_iteration',
            action_params={'iteration_logic': 'process_item'},
            schema_id='loop_body'
        )
        
        loop_condition = schema_factory.create_action_schema(
            action_type='loop_condition',
            action_params={'condition': 'has_more_items'},
            schema_id='loop_condition'
        )
        
        loop_schema = schema_factory.create_loop_schema(
            condition=loop_condition,
            body=loop_body,
            loop_type='while',
            schema_id='optimized_loop'
        )
        
        # 执行循环图式
        loop_context = ExecutionContext(context_id="loop_context")
        loop_context.set_variable("items", ["item1", "item2", "item3"])
        loop_context.set_variable("current_index", 0)
        
        loop_result = await executor.execute_with_search_guidance(loop_schema, loop_context)
        
        print(f"循环图式执行: {loop_result.success}")
        print(f"执行时间: {loop_result.execution_time:.4f}s")
        
        print("\n3. 测试并发搜索和执行")
        
        # 创建多个并发任务
        concurrent_tasks = []
        
        for i in range(5):
            task_schema = schema_factory.create_action_schema(
                action_type='concurrent_task',
                action_params={'task_id': i, 'data': f'task_data_{i}'},
                schema_id=f'concurrent_task_{i}'
            )
            
            task_context = ExecutionContext(context_id=f"concurrent_context_{i}")
            task_context.set_variable("task_priority", 0.5 + i * 0.1)
            
            task = executor.execute_with_search_guidance(task_schema, task_context)
            concurrent_tasks.append(task)
        
        # 等待所有并发任务完成
        concurrent_results = await asyncio.gather(*concurrent_tasks, return_exceptions=True)
        
        successful_tasks = sum(1 for result in concurrent_results 
                             if not isinstance(result, Exception) and result.success)
        
        print(f"并发任务执行: {successful_tasks}/5 成功")
        
        # 获取最终统计
        final_integrator_stats = integrator.get_statistics()
        final_executor_stats = executor.get_execution_statistics()
        
        print(f"\n4. 最终统计信息:")
        print("搜索集成器:")
        for key, value in final_integrator_stats.items():
            if not isinstance(value, (list, dict)):
                print(f"  {key}: {value}")
        
        print("搜索驱动执行器:")
        for key, value in final_executor_stats.items():
            if key != 'recent_executions':
                print(f"  {key}: {value}")
        
        await integrator.stop()
        
        return True
        
    except Exception as e:
        logger.error(f"Integration scenarios test failed: {e}")
        return False


async def main():
    """主测试函数"""
    print("开始图式搜索集成系统测试...")
    
    tests = [
        ("搜索策略", test_search_strategies),
        ("图式搜索集成器", test_schema_search_integrator),
        ("搜索驱动执行器", test_search_driven_executor),
        ("集成场景", test_integration_scenarios)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            print(f"运行测试: {test_name}")
            print(f"{'='*60}")
            
            start_time = time.time()
            result = await test_func()
            execution_time = time.time() - start_time
            
            results.append(result)
            status = "✓ 通过" if result else "✗ 失败"
            print(f"\n{test_name}: {status} (耗时: {execution_time:.4f}s)")
            
        except Exception as e:
            logger.error(f"测试 {test_name} 出现异常: {e}")
            results.append(False)
            print(f"\n{test_name}: ✗ 异常 (错误: {str(e)})")
    
    # 总结
    successful_tests = sum(results)
    total_tests = len(results)
    success_rate = successful_tests / total_tests * 100
    
    print(f"\n{'='*60}")
    print(f"测试总结")
    print(f"{'='*60}")
    print(f"成功测试: {successful_tests}/{total_tests}")
    print(f"成功率: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("🎉 测试整体通过！")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    return successful_tests == total_tests


if __name__ == "__main__":
    asyncio.run(main())
