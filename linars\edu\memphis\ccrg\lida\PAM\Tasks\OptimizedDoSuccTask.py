"""
优化后的顺序执行任务
"""

import asyncio
import logging
from typing import List, Optional, Any, Dict
from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory
from linars.edu.memphis.ccrg.lida.Data.neo_util import NeoUtil
from linars.edu.memphis.ccrg.lida.Nlanguage.TreeChart import TreeChart, SchemaType
from linars.edu.memphis.ccrg.lida.Nlanguage.SchemaFactory import schema_factory
from linars.edu.memphis.ccrg.lida.Nlanguage.SchemaExecutor import schema_executor, ExecutionContext
from .OptimizedControlFlowTask import OptimizedControlFlowTask, ControlFlowType, TaskStatus


class OptimizedDoSuccTask(OptimizedControlFlowTask):
    """
    优化后的顺序执行任务
    
    主要改进：
    1. 基于图式的执行模型
    2. 支持并行和串行执行
    3. 完善的错误处理和回溯
    4. 性能优化和缓存
    5. 可配置的执行策略
    """
    
    def __init__(self, sink: Node, source: Node, pam: PAMemory, 
                 ticks_per_run: int = 1, act_stamp: Optional[str] = None,
                 execution_mode: str = "sequential", **kwargs):
        super().__init__(ControlFlowType.SEQUENCE, ticks_per_run, "optimized_succ")
        
        self.sink = sink
        self.source = source
        self.pam = pam
        self.act_stamp = act_stamp
        self.execution_mode = execution_mode  # "sequential", "parallel", "adaptive"
        
        # 执行相关属性
        self.sequence_schema = None
        self.execution_steps = []
        self.current_step = 0
        self.step_results = []
        
        # 缓存和优化
        self.query_cache = {}
        self.link_cache = {}
        
        # 配置选项
        self.config.update({
            'allow_partial_failure': kwargs.get('allow_partial_failure', False),
            'min_success_rate': kwargs.get('min_success_rate', 1.0),
            'step_timeout': kwargs.get('step_timeout', 60),
            'enable_parallel': kwargs.get('enable_parallel', False),
            'max_parallel_steps': kwargs.get('max_parallel_steps', 5)
        })
        
        # 初始化执行步骤
        self._initialize_execution_steps()
    
    def _initialize_execution_steps(self):
        """初始化执行步骤"""
        try:
            # 构建执行步骤序列
            self._build_execution_sequence()
            
            # 创建图式表示
            self._create_sequence_schema()
            
            self.status = TaskStatus.READY
            self.logger.info(f"Initialized {len(self.execution_steps)} execution steps")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize execution steps: {e}")
            self.status = TaskStatus.FAILED
            self.error_info = str(e)
    
    def _build_execution_sequence(self):
        """构建执行序列"""
        # 获取工作空间缓冲区
        seq_ns = self.pam.get_workspace_buffer("seq").get_buffer_content(None)
        goal_ns = self.pam.get_workspace_buffer("goal").get_buffer_content(None)
        
        # 构建查询
        h_name = self.source.get_tn_name()
        t_name = self.sink.get_tn_name()
        
        # 查找顺承关系
        query = f"match (n:场景)-[r:时序]->(m:场景)<-[r0:顺承]-(i:场景) where n.name = '{h_name}' and i.name = '{t_name}' return r"
        
        # 使用缓存
        if query in self.query_cache:
            links = self.query_cache[query]
        else:
            links = self._execute_query_with_retry(query)
            self.query_cache[query] = links
        
        # 构建执行步骤
        for i, link in enumerate(links):
            step = {
                'step_id': i,
                'link': link,
                'source': link.get_source() if link else self.source,
                'sink': link.get_sink() if link else self.sink,
                'type': 'sequence_step',
                'dependencies': [i-1] if i > 0 else [],
                'timeout': self.config.get('step_timeout', 60)
            }
            self.execution_steps.append(step)
        
        # 如果没有找到链接，创建默认步骤
        if not self.execution_steps:
            default_step = {
                'step_id': 0,
                'link': None,
                'source': self.source,
                'sink': self.sink,
                'type': 'default_step',
                'dependencies': [],
                'timeout': self.config.get('step_timeout', 60)
            }
            self.execution_steps.append(default_step)
    
    def _execute_query_with_retry(self, query: str, max_retries: int = 3) -> List[Link]:
        """带重试的查询执行"""
        for attempt in range(max_retries):
            try:
                link = NeoUtil.get_link_cypher(query)
                return [link] if link else []
            except Exception as e:
                self.logger.warning(f"Query attempt {attempt + 1} failed: {e}")
                if attempt == max_retries - 1:
                    raise e
        return []
    
    def _create_sequence_schema(self):
        """创建序列图式"""
        # 为每个步骤创建动作图式
        step_schemas = []
        
        for step in self.execution_steps:
            action_schema = schema_factory.create_action_schema(
                action_type='execute_step',
                action_params={
                    'step_id': step['step_id'],
                    'link': step['link'],
                    'source': step['source'],
                    'sink': step['sink'],
                    'pam': self.pam,
                    'act_stamp': self.act_stamp
                },
                schema_id=f"{self.task_id}_step_{step['step_id']}"
            )
            step_schemas.append(action_schema)
        
        # 创建序列图式
        if step_schemas:
            self.sequence_schema = schema_factory.create_sequence_schema(
                steps=step_schemas,
                schema_id=f"{self.task_id}_sequence",
                allow_partial_failure=self.config.get('allow_partial_failure', False)
            )
        else:
            # 创建空的序列图式
            self.sequence_schema = schema_factory.create_sequence_schema(
                steps=[],
                schema_id=f"{self.task_id}_empty_sequence"
            )
    
    async def _execute_control_flow(self) -> Any:
        """执行控制流逻辑"""
        if not self.sequence_schema:
            raise ValueError("Sequence schema not initialized")
        
        # 创建执行上下文
        execution_context = ExecutionContext(
            context_id=f"{self.task_id}_execution"
        )
        
        # 设置执行变量
        execution_context.set_variable('pam', self.pam)
        execution_context.set_variable('act_stamp', self.act_stamp)
        execution_context.set_variable('source', self.source)
        execution_context.set_variable('sink', self.sink)
        
        # 注册步骤执行处理器
        self._register_step_handler()
        
        # 执行序列图式
        if self.execution_mode == "parallel" and self.config.get('enable_parallel', False):
            result = await self._execute_parallel(execution_context)
        else:
            result = await self._execute_sequential(execution_context)
        
        return result
    
    async def _execute_sequential(self, context: ExecutionContext) -> Any:
        """顺序执行"""
        self.logger.info(f"Starting sequential execution of {len(self.execution_steps)} steps")
        
        # 使用图式执行器执行
        result = await schema_executor.execute(self.sequence_schema, context)
        
        if result.success:
            self.logger.info("Sequential execution completed successfully")
            self._process_successful_execution(result)
        else:
            self.logger.error(f"Sequential execution failed: {result.error}")
            self._process_failed_execution(result)
        
        return result
    
    async def _execute_parallel(self, context: ExecutionContext) -> Any:
        """并行执行"""
        max_parallel = self.config.get('max_parallel_steps', 5)
        self.logger.info(f"Starting parallel execution with max {max_parallel} concurrent steps")
        
        # 将序列图式转换为并行执行
        tasks = []
        semaphore = asyncio.Semaphore(max_parallel)
        
        for step_schema in self.sequence_schema.sub_charts:
            task = self._execute_step_with_semaphore(step_schema, context, semaphore)
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        successful_results = []
        failed_results = []
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                failed_results.append(result)
                self.logger.error(f"Step {i} failed with exception: {result}")
            elif hasattr(result, 'success') and result.success:
                successful_results.append(result)
            else:
                failed_results.append(result)
        
        # 判断整体成功
        min_success_rate = self.config.get('min_success_rate', 1.0)
        required_successes = len(self.execution_steps) * min_success_rate
        overall_success = len(successful_results) >= required_successes
        
        self.logger.info(f"Parallel execution completed: {len(successful_results)} successful, {len(failed_results)} failed")
        
        return {
            'success': overall_success,
            'successful_results': successful_results,
            'failed_results': failed_results,
            'total_steps': len(self.execution_steps)
        }
    
    async def _execute_step_with_semaphore(self, step_schema: TreeChart, 
                                         context: ExecutionContext, 
                                         semaphore: asyncio.Semaphore):
        """使用信号量控制的步骤执行"""
        async with semaphore:
            step_context = context.create_child_context(f"{context.context_id}_step")
            return await schema_executor.execute(step_schema, step_context)
    
    def _register_step_handler(self):
        """注册步骤执行处理器"""
        from linars.edu.memphis.ccrg.lida.Nlanguage.SchemaExecutor import ActionHandler
        
        class StepExecutionHandler(ActionHandler):
            def __init__(self, task_instance):
                self.task = task_instance
            
            async def execute(self, action_params: Dict[str, Any], context: ExecutionContext) -> Any:
                step_id = action_params.get('step_id', 0)
                link = action_params.get('link')
                source = action_params.get('source')
                sink = action_params.get('sink')
                pam = action_params.get('pam')
                act_stamp = action_params.get('act_stamp')
                
                # 执行具体的步骤逻辑
                return await self.task._execute_single_step(step_id, link, source, sink, pam, act_stamp, context)
            
            def validate_params(self, action_params: Dict[str, Any]) -> bool:
                required_params = ['step_id', 'source', 'sink', 'pam']
                return all(param in action_params for param in required_params)
        
        # 注册处理器
        schema_executor.register_action_handler('execute_step', StepExecutionHandler(self))
    
    async def _execute_single_step(self, step_id: int, link: Optional[Link], 
                                 source: Node, sink: Node, pam: PAMemory, 
                                 act_stamp: Optional[str], context: ExecutionContext) -> Any:
        """执行单个步骤"""
        self.logger.debug(f"Executing step {step_id}: {source.get_tn_name()} -> {sink.get_tn_name()}")
        
        try:
            if link:
                # 处理链接
                to_node = link.get_sink()
                
                # 接收感知
                pam.get_listener().receive_percept(to_node, ModuleName.SeqGraph)
                pam.get_listener().receive_percept(link, ModuleName.SeqGraph)
                
                # 设置激励显著性
                to_node.set_incentive_salience(sink.get_incentive_salience())
                
                self.logger.debug(f"Processed link: {link}")
                
                # 继续执行后续步骤
                next_result = await self._continue_execution(link, pam, act_stamp, context)
                
                return {
                    'step_id': step_id,
                    'success': True,
                    'link': link,
                    'next_result': next_result
                }
            else:
                # 处理默认步骤
                self.logger.debug(f"Executing default step {step_id}")
                return {
                    'step_id': step_id,
                    'success': True,
                    'message': f"Default step {step_id} completed"
                }
                
        except Exception as e:
            self.logger.error(f"Step {step_id} execution failed: {e}")
            return {
                'step_id': step_id,
                'success': False,
                'error': str(e)
            }
    
    async def _continue_execution(self, link: Link, pam: PAMemory, 
                                act_stamp: Optional[str], context: ExecutionContext) -> Any:
        """继续执行后续步骤"""
        try:
            # 检查是否有后续时序
            if link:
                # 递归执行
                pam.get_act_root(link, False, False, act_stamp)
                return {'continued': True, 'link': link}
            else:
                # 检查循环条件
                return await self._check_loop_condition(context)
        except Exception as e:
            self.logger.error(f"Continue execution failed: {e}")
            return {'continued': False, 'error': str(e)}
    
    async def _check_loop_condition(self, context: ExecutionContext) -> Any:
        """检查循环条件"""
        # 这里可以实现循环条件检查逻辑
        # 目前返回简单的结果
        return {'loop_checked': True, 'continue_loop': False}
    
    def _process_successful_execution(self, result: Any):
        """处理成功执行"""
        self.execution_result = result
        self.current_step = len(self.execution_steps)
        
        # 更新PAM状态
        if hasattr(self.pam, 'update_execution_status'):
            self.pam.update_execution_status(self.act_stamp, 'completed')
    
    def _process_failed_execution(self, result: Any):
        """处理失败执行"""
        self.execution_result = result
        self.error_info = str(result.error) if hasattr(result, 'error') else "Unknown error"
        
        # 更新PAM状态
        if hasattr(self.pam, 'update_execution_status'):
            self.pam.update_execution_status(self.act_stamp, 'failed')
    
    def _attempt_error_recovery(self, error: Exception):
        """尝试错误恢复"""
        self.logger.info(f"Attempting error recovery for: {error}")
        
        # 尝试从最近的检查点恢复
        if self.config.get('enable_checkpoints', True):
            checkpoints = self.execution_context.checkpoints
            if checkpoints:
                latest_checkpoint = checkpoints[-1]
                self.logger.info(f"Restoring from checkpoint: {latest_checkpoint['name']}")
                self.execution_context.restore_checkpoint(latest_checkpoint['name'])
                return True
        
        return False
    
    def get_execution_progress(self) -> Dict[str, Any]:
        """获取执行进度"""
        return {
            'task_id': self.task_id,
            'total_steps': len(self.execution_steps),
            'current_step': self.current_step,
            'progress_percentage': (self.current_step / len(self.execution_steps)) * 100 if self.execution_steps else 0,
            'status': self.status.value,
            'execution_mode': self.execution_mode,
            'step_results_count': len(self.step_results)
        }
