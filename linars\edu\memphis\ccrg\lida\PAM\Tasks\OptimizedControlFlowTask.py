"""
优化后的控制流任务基类和具体实现
"""

import asyncio
import logging
import time
import uuid
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Callable, Union
from enum import Enum

from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory
from linars.edu.memphis.ccrg.lida.Nlanguage.TreeChart import TreeChart, SchemaType, ExecutionMode
from linars.edu.memphis.ccrg.lida.Nlanguage.SchemaExecutor import schema_executor, ExecutionContext, ExecutionResult


class ControlFlowType(Enum):
    """控制流类型枚举"""
    SEQUENCE = "sequence"
    CONDITION = "condition"
    LOOP = "loop"
    PARALLEL = "parallel"
    EXCEPTION = "exception"


class TaskStatus(Enum):
    """任务状态枚举"""
    CREATED = "created"
    READY = "ready"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    SUSPENDED = "suspended"


@dataclass
class TaskExecutionContext:
    """任务执行上下文"""
    task_id: str
    parent_task_id: Optional[str] = None
    variables: Dict[str, Any] = field(default_factory=dict)
    execution_stack: List[str] = field(default_factory=list)
    start_time: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)
    checkpoints: List[Dict[str, Any]] = field(default_factory=list)
    
    def create_checkpoint(self, name: str):
        """创建检查点"""
        checkpoint = {
            'name': name,
            'timestamp': time.time(),
            'variables': self.variables.copy(),
            'execution_stack': self.execution_stack.copy(),
            'metadata': self.metadata.copy()
        }
        self.checkpoints.append(checkpoint)
        return checkpoint
    
    def restore_checkpoint(self, name: str) -> bool:
        """恢复到指定检查点"""
        for checkpoint in reversed(self.checkpoints):
            if checkpoint['name'] == name:
                self.variables = checkpoint['variables'].copy()
                self.execution_stack = checkpoint['execution_stack'].copy()
                self.metadata = checkpoint['metadata'].copy()
                return True
        return False


class OptimizedControlFlowTask(FrameworkTaskImpl, ABC):
    """
    优化后的控制流任务基类
    
    主要改进：
    1. 统一的错误处理和恢复机制
    2. 支持检查点和回溯
    3. 异步执行支持
    4. 性能监控和统计
    5. 可配置的执行策略
    """
    
    def __init__(self, control_flow_type: ControlFlowType, ticks_per_run: int = 1, 
                 task_type: str = "control_flow", task_id: str = None):
        super().__init__(ticks_per_run, task_type)
        
        self.task_id = task_id or str(uuid.uuid4())
        self.control_flow_type = control_flow_type
        self.status = TaskStatus.CREATED
        self.logger = logging.getLogger(f"{self.__class__.__name__}_{self.task_id[:8]}")
        
        # 执行相关属性
        self.execution_context = TaskExecutionContext(self.task_id)
        self.execution_result = None
        self.error_info = None
        self.retry_count = 0
        self.max_retries = 3
        
        # 性能监控
        self.execution_stats = {
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'average_execution_time': 0.0,
            'retry_attempts': 0
        }
        
        # 配置选项
        self.config = {
            'enable_checkpoints': True,
            'enable_retry': True,
            'enable_async': True,
            'timeout': 300,  # 5分钟超时
            'checkpoint_interval': 10  # 每10步创建检查点
        }
    
    def run_this_framework_task(self):
        """框架任务执行入口"""
        try:
            self.status = TaskStatus.RUNNING
            self.execution_context.start_time = time.time()
            
            if self.config.get('enable_async', True):
                # 异步执行
                asyncio.create_task(self._async_execute())
            else:
                # 同步执行
                self._sync_execute()
                
        except Exception as e:
            self.logger.error(f"Task execution failed: {e}")
            self._handle_error(e)
        finally:
            self.cancel()
    
    async def _async_execute(self):
        """异步执行"""
        try:
            start_time = time.time()
            
            # 创建初始检查点
            if self.config.get('enable_checkpoints', True):
                self.execution_context.create_checkpoint('start')
            
            # 执行具体任务逻辑
            result = await self._execute_control_flow()
            
            execution_time = time.time() - start_time
            self._update_stats(True, execution_time)
            
            self.execution_result = result
            self.status = TaskStatus.COMPLETED
            
            self.logger.info(f"Task {self.task_id} completed successfully in {execution_time:.4f}s")
            
        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(False, execution_time)
            
            if self.config.get('enable_retry', True) and self.retry_count < self.max_retries:
                self.logger.warning(f"Task {self.task_id} failed, retrying ({self.retry_count + 1}/{self.max_retries})")
                await self._retry_execution(e)
            else:
                self._handle_error(e)
    
    def _sync_execute(self):
        """同步执行"""
        try:
            start_time = time.time()
            
            # 创建初始检查点
            if self.config.get('enable_checkpoints', True):
                self.execution_context.create_checkpoint('start')
            
            # 执行具体任务逻辑（同步版本）
            result = self._execute_control_flow_sync()
            
            execution_time = time.time() - start_time
            self._update_stats(True, execution_time)
            
            self.execution_result = result
            self.status = TaskStatus.COMPLETED
            
            self.logger.info(f"Task {self.task_id} completed successfully in {execution_time:.4f}s")
            
        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(False, execution_time)
            
            if self.config.get('enable_retry', True) and self.retry_count < self.max_retries:
                self.logger.warning(f"Task {self.task_id} failed, retrying ({self.retry_count + 1}/{self.max_retries})")
                self._retry_execution_sync(e)
            else:
                self._handle_error(e)
    
    @abstractmethod
    async def _execute_control_flow(self) -> Any:
        """异步执行控制流逻辑（子类实现）"""
        pass
    
    def _execute_control_flow_sync(self) -> Any:
        """同步执行控制流逻辑（默认实现，子类可重写）"""
        # 默认使用asyncio.run来执行异步方法
        return asyncio.run(self._execute_control_flow())
    
    async def _retry_execution(self, error: Exception):
        """异步重试执行"""
        self.retry_count += 1
        self.execution_stats['retry_attempts'] += 1
        
        # 恢复到开始检查点
        if self.config.get('enable_checkpoints', True):
            self.execution_context.restore_checkpoint('start')
        
        # 等待一段时间后重试
        await asyncio.sleep(min(2 ** self.retry_count, 10))  # 指数退避
        
        try:
            await self._async_execute()
        except Exception as e:
            if self.retry_count >= self.max_retries:
                self._handle_error(e)
            else:
                await self._retry_execution(e)
    
    def _retry_execution_sync(self, error: Exception):
        """同步重试执行"""
        self.retry_count += 1
        self.execution_stats['retry_attempts'] += 1
        
        # 恢复到开始检查点
        if self.config.get('enable_checkpoints', True):
            self.execution_context.restore_checkpoint('start')
        
        # 等待一段时间后重试
        time.sleep(min(2 ** self.retry_count, 10))  # 指数退避
        
        try:
            self._sync_execute()
        except Exception as e:
            if self.retry_count >= self.max_retries:
                self._handle_error(e)
            else:
                self._retry_execution_sync(e)
    
    def _handle_error(self, error: Exception):
        """处理错误"""
        self.status = TaskStatus.FAILED
        self.error_info = str(error)
        
        self.logger.error(f"Task {self.task_id} failed after {self.retry_count} retries: {error}")
        
        # 可以在这里添加错误恢复逻辑
        self._attempt_error_recovery(error)
    
    def _attempt_error_recovery(self, error: Exception):
        """尝试错误恢复"""
        # 子类可以重写此方法来实现特定的错误恢复逻辑
        pass
    
    def _update_stats(self, success: bool, execution_time: float):
        """更新执行统计"""
        self.execution_stats['total_executions'] += 1
        if success:
            self.execution_stats['successful_executions'] += 1
        else:
            self.execution_stats['failed_executions'] += 1
        
        # 更新平均执行时间
        total = self.execution_stats['total_executions']
        current_avg = self.execution_stats['average_execution_time']
        self.execution_stats['average_execution_time'] = (
            (current_avg * (total - 1) + execution_time) / total
        )
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """获取执行统计"""
        return {
            **self.execution_stats,
            'task_id': self.task_id,
            'control_flow_type': self.control_flow_type.value,
            'status': self.status.value,
            'retry_count': self.retry_count,
            'checkpoints_count': len(self.execution_context.checkpoints)
        }
    
    def suspend(self):
        """暂停任务"""
        self.status = TaskStatus.SUSPENDED
        if self.config.get('enable_checkpoints', True):
            self.execution_context.create_checkpoint('suspend')
    
    def resume(self):
        """恢复任务"""
        if self.status == TaskStatus.SUSPENDED:
            self.status = TaskStatus.RUNNING
            if self.config.get('enable_checkpoints', True):
                self.execution_context.restore_checkpoint('suspend')
    
    def set_config(self, key: str, value: Any):
        """设置配置选项"""
        self.config[key] = value
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """获取配置选项"""
        return self.config.get(key, default)
