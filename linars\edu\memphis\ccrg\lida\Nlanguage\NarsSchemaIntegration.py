"""
NARS-图式集成系统 - 实现推理结果驱动图式执行和执行反馈推理的双向集成
"""

import asyncio
import logging
import time
import uuid
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Callable, Union, Set, Tuple
from enum import Enum
from collections import defaultdict, deque
import threading

from .TreeChart import TreeChart, SchemaType, ExecutionMode, ChartStatus
from .SchemaExecutor import schema_executor, ExecutionContext, ExecutionResult
from .SchemaFactory import schema_factory
from .VariableBindingSystem import variable_binding_system, VariableType, ScopeType

# 尝试导入NARS相关模块
try:
    from linars.org.opennars.main.nar import Nar
    from linars.org.opennars.entity.task import Task
    from linars.org.opennars.entity.sentence import Sentence
    from linars.org.opennars.entity.truth_value import TruthValue
    from linars.org.opennars.entity.budget_value import BudgetValue
    from linars.edu.memphis.ccrg.linars.term import Term
    from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm
    NARS_AVAILABLE = True
except ImportError:
    NARS_AVAILABLE = False
    # 创建模拟类
    class Nar: pass
    class Task: pass
    class Sentence: pass
    class TruthValue: pass
    class BudgetValue: pass
    class Term: pass
    class CompoundTerm: pass


class ReasoningType(Enum):
    """推理类型"""
    DEDUCTION = "deduction"       # 演绎推理
    INDUCTION = "induction"       # 归纳推理
    ABDUCTION = "abduction"       # 溯因推理
    ANALOGY = "analogy"           # 类比推理
    TEMPORAL = "temporal"         # 时序推理
    PROCEDURAL = "procedural"     # 程序性推理


class IntegrationType(Enum):
    """集成类型"""
    REASONING_TO_SCHEMA = "reasoning_to_schema"    # 推理驱动图式
    SCHEMA_TO_REASONING = "schema_to_reasoning"    # 图式驱动推理
    BIDIRECTIONAL = "bidirectional"               # 双向集成
    FEEDBACK_LOOP = "feedback_loop"               # 反馈循环


@dataclass
class ReasoningRequest:
    """推理请求"""
    request_id: str
    reasoning_type: ReasoningType
    premises: List[Any]
    goals: List[Any] = field(default_factory=list)
    context_data: Dict[str, Any] = field(default_factory=dict)
    confidence_threshold: float = 0.5
    max_steps: int = 10
    timeout: float = 10.0
    created_time: float = field(default_factory=time.time)


@dataclass
class ReasoningResult:
    """推理结果"""
    result_id: str
    request_id: str
    conclusions: List[Any]
    reasoning_path: List[Dict[str, Any]]
    confidence: float
    execution_time: float
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SchemaReasoningBinding:
    """图式推理绑定"""
    binding_id: str
    schema: TreeChart
    reasoning_request: ReasoningRequest
    reasoning_result: Optional[ReasoningResult] = None
    integration_type: IntegrationType = IntegrationType.BIDIRECTIONAL
    binding_strength: float = 0.5
    created_time: float = field(default_factory=time.time)
    last_updated: float = field(default_factory=time.time)


class NarsReasoningEngine:
    """NARS推理引擎包装器"""
    
    def __init__(self, nar: Optional[Nar] = None):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.nar = nar
        self.reasoning_history = []
        self.concept_cache = {}
        
        # 推理参数
        self.reasoning_params = {
            'max_inference_steps': 100,
            'confidence_threshold': 0.5,
            'truth_threshold': 0.5,
            'budget_threshold': 0.1,
            'temporal_window': 10
        }
        
        # 统计信息
        self.stats = {
            'total_inferences': 0,
            'successful_inferences': 0,
            'failed_inferences': 0,
            'average_inference_time': 0.0
        }
    
    async def perform_reasoning(self, request: ReasoningRequest) -> ReasoningResult:
        """执行推理"""
        start_time = time.time()
        self.stats['total_inferences'] += 1
        
        try:
            # 根据推理类型选择推理方法
            if request.reasoning_type == ReasoningType.DEDUCTION:
                conclusions = await self._perform_deduction(request)
            elif request.reasoning_type == ReasoningType.INDUCTION:
                conclusions = await self._perform_induction(request)
            elif request.reasoning_type == ReasoningType.ABDUCTION:
                conclusions = await self._perform_abduction(request)
            elif request.reasoning_type == ReasoningType.ANALOGY:
                conclusions = await self._perform_analogy(request)
            elif request.reasoning_type == ReasoningType.TEMPORAL:
                conclusions = await self._perform_temporal_reasoning(request)
            elif request.reasoning_type == ReasoningType.PROCEDURAL:
                conclusions = await self._perform_procedural_reasoning(request)
            else:
                conclusions = await self._perform_general_reasoning(request)
            
            # 计算置信度
            confidence = self._calculate_reasoning_confidence(conclusions, request)
            
            # 构建推理路径
            reasoning_path = self._build_reasoning_path(request.premises, conclusions)
            
            execution_time = time.time() - start_time
            self.stats['successful_inferences'] += 1
            self._update_average_time(execution_time)
            
            result = ReasoningResult(
                result_id=str(uuid.uuid4()),
                request_id=request.request_id,
                conclusions=conclusions,
                reasoning_path=reasoning_path,
                confidence=confidence,
                execution_time=execution_time,
                metadata={
                    'reasoning_type': request.reasoning_type.value,
                    'premises_count': len(request.premises),
                    'conclusions_count': len(conclusions)
                }
            )
            
            # 记录推理历史
            self.reasoning_history.append(result)
            if len(self.reasoning_history) > 1000:
                self.reasoning_history = self.reasoning_history[-500:]
            
            return result
            
        except Exception as e:
            self.logger.error(f"Reasoning failed: {e}")
            self.stats['failed_inferences'] += 1
            
            return ReasoningResult(
                result_id=str(uuid.uuid4()),
                request_id=request.request_id,
                conclusions=[],
                reasoning_path=[],
                confidence=0.0,
                execution_time=time.time() - start_time,
                metadata={'error': str(e)}
            )
    
    async def _perform_deduction(self, request: ReasoningRequest) -> List[Any]:
        """执行演绎推理"""
        conclusions = []
        
        try:
            # 基本的三段论演绎推理
            for i, premise1 in enumerate(request.premises):
                for j, premise2 in enumerate(request.premises[i+1:], i+1):
                    # 尝试应用演绎规则
                    deduction_result = self._apply_deduction_rule(premise1, premise2)
                    if deduction_result:
                        conclusions.append(deduction_result)
            
            # 如果有NARS可用，使用NARS进行更复杂的演绎
            if NARS_AVAILABLE and self.nar:
                nars_conclusions = await self._nars_deduction(request.premises)
                conclusions.extend(nars_conclusions)
            
        except Exception as e:
            self.logger.warning(f"Deduction reasoning failed: {e}")
        
        return conclusions
    
    async def _perform_induction(self, request: ReasoningRequest) -> List[Any]:
        """执行归纳推理"""
        conclusions = []
        
        try:
            # 寻找模式和规律
            patterns = self._find_patterns(request.premises)
            
            for pattern in patterns:
                # 基于模式生成归纳结论
                induction_result = self._generalize_pattern(pattern)
                if induction_result:
                    conclusions.append(induction_result)
            
            # 使用NARS进行归纳推理
            if NARS_AVAILABLE and self.nar:
                nars_conclusions = await self._nars_induction(request.premises)
                conclusions.extend(nars_conclusions)
            
        except Exception as e:
            self.logger.warning(f"Induction reasoning failed: {e}")
        
        return conclusions
    
    async def _perform_abduction(self, request: ReasoningRequest) -> List[Any]:
        """执行溯因推理"""
        conclusions = []
        
        try:
            # 对于每个观察结果，寻找可能的原因
            for premise in request.premises:
                possible_causes = self._find_possible_causes(premise)
                conclusions.extend(possible_causes)
            
            # 使用NARS进行溯因推理
            if NARS_AVAILABLE and self.nar:
                nars_conclusions = await self._nars_abduction(request.premises)
                conclusions.extend(nars_conclusions)
            
        except Exception as e:
            self.logger.warning(f"Abduction reasoning failed: {e}")
        
        return conclusions
    
    async def _perform_analogy(self, request: ReasoningRequest) -> List[Any]:
        """执行类比推理"""
        conclusions = []
        
        try:
            # 寻找结构相似的情况
            analogies = self._find_analogies(request.premises)
            
            for analogy in analogies:
                # 基于类比生成结论
                analogy_result = self._apply_analogy(analogy)
                if analogy_result:
                    conclusions.append(analogy_result)
            
        except Exception as e:
            self.logger.warning(f"Analogy reasoning failed: {e}")
        
        return conclusions
    
    async def _perform_temporal_reasoning(self, request: ReasoningRequest) -> List[Any]:
        """执行时序推理"""
        conclusions = []
        
        try:
            # 分析时序关系
            temporal_relations = self._analyze_temporal_relations(request.premises)
            
            for relation in temporal_relations:
                # 基于时序关系推理
                temporal_result = self._infer_temporal_consequence(relation)
                if temporal_result:
                    conclusions.append(temporal_result)
            
        except Exception as e:
            self.logger.warning(f"Temporal reasoning failed: {e}")
        
        return conclusions
    
    async def _perform_procedural_reasoning(self, request: ReasoningRequest) -> List[Any]:
        """执行程序性推理"""
        conclusions = []
        
        try:
            # 分析程序性知识
            procedures = self._extract_procedures(request.premises)
            
            for procedure in procedures:
                # 基于程序性知识推理
                procedural_result = self._execute_procedure(procedure)
                if procedural_result:
                    conclusions.append(procedural_result)
            
        except Exception as e:
            self.logger.warning(f"Procedural reasoning failed: {e}")
        
        return conclusions
    
    async def _perform_general_reasoning(self, request: ReasoningRequest) -> List[Any]:
        """执行通用推理"""
        conclusions = []
        
        try:
            # 组合多种推理方法
            deduction_results = await self._perform_deduction(request)
            induction_results = await self._perform_induction(request)
            abduction_results = await self._perform_abduction(request)
            
            conclusions.extend(deduction_results)
            conclusions.extend(induction_results)
            conclusions.extend(abduction_results)
            
            # 去重和排序
            conclusions = self._deduplicate_and_rank_conclusions(conclusions)
            
        except Exception as e:
            self.logger.warning(f"General reasoning failed: {e}")
        
        return conclusions
    
    def _apply_deduction_rule(self, premise1: Any, premise2: Any) -> Optional[Any]:
        """应用演绎规则"""
        try:
            # 简单的三段论演绎规则
            # 如果 A -> B 且 B -> C，则 A -> C
            
            # 这里需要根据实际的前提格式实现具体的演绎逻辑
            # 目前返回一个模拟的结论
            return {
                'type': 'deduction',
                'premise1': premise1,
                'premise2': premise2,
                'conclusion': f"deduced_from_{premise1}_and_{premise2}",
                'confidence': 0.8
            }
            
        except Exception as e:
            self.logger.debug(f"Failed to apply deduction rule: {e}")
            return None
    
    def _find_patterns(self, premises: List[Any]) -> List[Dict[str, Any]]:
        """寻找模式"""
        patterns = []
        
        try:
            # 简单的模式识别
            # 寻找重复的结构或关系
            
            for i, premise in enumerate(premises):
                pattern = {
                    'type': 'simple_pattern',
                    'premise': premise,
                    'frequency': 1,
                    'confidence': 0.6
                }
                patterns.append(pattern)
            
        except Exception as e:
            self.logger.debug(f"Pattern finding failed: {e}")
        
        return patterns
    
    def _generalize_pattern(self, pattern: Dict[str, Any]) -> Optional[Any]:
        """泛化模式"""
        try:
            return {
                'type': 'generalization',
                'pattern': pattern,
                'generalized_rule': f"generalized_from_{pattern.get('type', 'unknown')}",
                'confidence': pattern.get('confidence', 0.5) * 0.8
            }
        except Exception as e:
            self.logger.debug(f"Pattern generalization failed: {e}")
            return None
    
    def _find_possible_causes(self, observation: Any) -> List[Any]:
        """寻找可能的原因"""
        causes = []
        
        try:
            # 基于观察寻找可能的原因
            cause = {
                'type': 'possible_cause',
                'observation': observation,
                'cause': f"possible_cause_of_{observation}",
                'confidence': 0.4
            }
            causes.append(cause)
            
        except Exception as e:
            self.logger.debug(f"Cause finding failed: {e}")
        
        return causes
    
    def _find_analogies(self, premises: List[Any]) -> List[Dict[str, Any]]:
        """寻找类比"""
        analogies = []
        
        try:
            # 简单的结构相似性检测
            for i, premise1 in enumerate(premises):
                for j, premise2 in enumerate(premises[i+1:], i+1):
                    analogy = {
                        'type': 'structural_analogy',
                        'source': premise1,
                        'target': premise2,
                        'similarity': 0.6
                    }
                    analogies.append(analogy)
                    
        except Exception as e:
            self.logger.debug(f"Analogy finding failed: {e}")
        
        return analogies
    
    def _apply_analogy(self, analogy: Dict[str, Any]) -> Optional[Any]:
        """应用类比"""
        try:
            return {
                'type': 'analogy_result',
                'analogy': analogy,
                'conclusion': f"analogical_conclusion_from_{analogy.get('source')}_to_{analogy.get('target')}",
                'confidence': analogy.get('similarity', 0.5)
            }
        except Exception as e:
            self.logger.debug(f"Analogy application failed: {e}")
            return None
    
    def _analyze_temporal_relations(self, premises: List[Any]) -> List[Dict[str, Any]]:
        """分析时序关系"""
        relations = []
        
        try:
            # 简单的时序关系分析
            for i, premise in enumerate(premises):
                relation = {
                    'type': 'temporal_sequence',
                    'premise': premise,
                    'order': i,
                    'timestamp': time.time() - i
                }
                relations.append(relation)
                
        except Exception as e:
            self.logger.debug(f"Temporal analysis failed: {e}")
        
        return relations
    
    def _infer_temporal_consequence(self, relation: Dict[str, Any]) -> Optional[Any]:
        """推理时序后果"""
        try:
            return {
                'type': 'temporal_inference',
                'relation': relation,
                'consequence': f"temporal_consequence_of_{relation.get('premise')}",
                'confidence': 0.6
            }
        except Exception as e:
            self.logger.debug(f"Temporal inference failed: {e}")
            return None
    
    def _extract_procedures(self, premises: List[Any]) -> List[Dict[str, Any]]:
        """提取程序性知识"""
        procedures = []
        
        try:
            # 简单的程序性知识提取
            for premise in premises:
                procedure = {
                    'type': 'simple_procedure',
                    'premise': premise,
                    'steps': [f"step_from_{premise}"],
                    'confidence': 0.5
                }
                procedures.append(procedure)
                
        except Exception as e:
            self.logger.debug(f"Procedure extraction failed: {e}")
        
        return procedures
    
    def _execute_procedure(self, procedure: Dict[str, Any]) -> Optional[Any]:
        """执行程序"""
        try:
            return {
                'type': 'procedure_result',
                'procedure': procedure,
                'result': f"result_of_{procedure.get('type')}",
                'confidence': procedure.get('confidence', 0.5)
            }
        except Exception as e:
            self.logger.debug(f"Procedure execution failed: {e}")
            return None
    
    def _deduplicate_and_rank_conclusions(self, conclusions: List[Any]) -> List[Any]:
        """去重和排序结论"""
        try:
            # 简单的去重和排序
            unique_conclusions = []
            seen = set()
            
            for conclusion in conclusions:
                conclusion_str = str(conclusion)
                if conclusion_str not in seen:
                    seen.add(conclusion_str)
                    unique_conclusions.append(conclusion)
            
            # 按置信度排序
            unique_conclusions.sort(
                key=lambda x: x.get('confidence', 0) if isinstance(x, dict) else 0,
                reverse=True
            )
            
            return unique_conclusions
            
        except Exception as e:
            self.logger.debug(f"Conclusion processing failed: {e}")
            return conclusions
    
    def _calculate_reasoning_confidence(self, conclusions: List[Any], request: ReasoningRequest) -> float:
        """计算推理置信度"""
        if not conclusions:
            return 0.0
        
        try:
            # 基于结论数量和质量计算置信度
            total_confidence = 0.0
            valid_conclusions = 0
            
            for conclusion in conclusions:
                if isinstance(conclusion, dict) and 'confidence' in conclusion:
                    total_confidence += conclusion['confidence']
                    valid_conclusions += 1
                else:
                    total_confidence += 0.5  # 默认置信度
                    valid_conclusions += 1
            
            if valid_conclusions > 0:
                avg_confidence = total_confidence / valid_conclusions
                # 根据结论数量调整置信度
                quantity_factor = min(1.0, len(conclusions) / 5.0)
                return avg_confidence * quantity_factor
            else:
                return 0.0
                
        except Exception as e:
            self.logger.debug(f"Confidence calculation failed: {e}")
            return 0.5
    
    def _build_reasoning_path(self, premises: List[Any], conclusions: List[Any]) -> List[Dict[str, Any]]:
        """构建推理路径"""
        path = []
        
        try:
            # 记录推理步骤
            path.append({
                'step': 'premises',
                'content': premises,
                'timestamp': time.time()
            })
            
            path.append({
                'step': 'reasoning_process',
                'content': 'Applied reasoning rules',
                'timestamp': time.time()
            })
            
            path.append({
                'step': 'conclusions',
                'content': conclusions,
                'timestamp': time.time()
            })
            
        except Exception as e:
            self.logger.debug(f"Path building failed: {e}")
        
        return path
    
    def _update_average_time(self, execution_time: float):
        """更新平均执行时间"""
        current_avg = self.stats['average_inference_time']
        total_inferences = self.stats['total_inferences']
        
        if total_inferences > 1:
            self.stats['average_inference_time'] = (
                (current_avg * (total_inferences - 1) + execution_time) / total_inferences
            )
        else:
            self.stats['average_inference_time'] = execution_time
    
    async def _nars_deduction(self, premises: List[Any]) -> List[Any]:
        """使用NARS进行演绎推理"""
        # 这里需要实现与NARS的具体交互
        # 目前返回空列表
        return []
    
    async def _nars_induction(self, premises: List[Any]) -> List[Any]:
        """使用NARS进行归纳推理"""
        # 这里需要实现与NARS的具体交互
        # 目前返回空列表
        return []
    
    async def _nars_abduction(self, premises: List[Any]) -> List[Any]:
        """使用NARS进行溯因推理"""
        # 这里需要实现与NARS的具体交互
        # 目前返回空列表
        return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            'reasoning_history_size': len(self.reasoning_history),
            'concept_cache_size': len(self.concept_cache),
            'nars_available': NARS_AVAILABLE
        }


class SchemaReasoningIntegrator:
    """图式推理集成器"""

    def __init__(self, reasoning_engine: NarsReasoningEngine):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.reasoning_engine = reasoning_engine

        # 绑定管理
        self.bindings: Dict[str, SchemaReasoningBinding] = {}
        self.binding_lock = threading.RLock()

        # 集成策略
        self.integration_strategies = {
            IntegrationType.REASONING_TO_SCHEMA: self._reasoning_to_schema_strategy,
            IntegrationType.SCHEMA_TO_REASONING: self._schema_to_reasoning_strategy,
            IntegrationType.BIDIRECTIONAL: self._bidirectional_strategy,
            IntegrationType.FEEDBACK_LOOP: self._feedback_loop_strategy
        }

        # 转换器
        self.schema_to_reasoning_converter = SchemaToReasoningConverter()
        self.reasoning_to_schema_converter = ReasoningToSchemaConverter()

        # 统计信息
        self.stats = {
            'total_integrations': 0,
            'successful_integrations': 0,
            'failed_integrations': 0,
            'reasoning_driven_executions': 0,
            'schema_driven_reasonings': 0,
            'feedback_loops': 0
        }

    async def integrate_schema_with_reasoning(self, schema: TreeChart,
                                           reasoning_request: ReasoningRequest,
                                           integration_type: IntegrationType = IntegrationType.BIDIRECTIONAL) -> SchemaReasoningBinding:
        """将图式与推理集成"""
        binding_id = str(uuid.uuid4())

        try:
            # 创建绑定
            binding = SchemaReasoningBinding(
                binding_id=binding_id,
                schema=schema,
                reasoning_request=reasoning_request,
                integration_type=integration_type
            )

            with self.binding_lock:
                self.bindings[binding_id] = binding

            # 执行推理
            reasoning_result = await self.reasoning_engine.perform_reasoning(reasoning_request)
            binding.reasoning_result = reasoning_result
            binding.last_updated = time.time()

            # 应用集成策略
            strategy = self.integration_strategies.get(integration_type)
            if strategy:
                await strategy(binding)

            self.stats['total_integrations'] += 1
            self.stats['successful_integrations'] += 1

            self.logger.info(f"Successfully integrated schema {schema.chart_id} with reasoning")
            return binding

        except Exception as e:
            self.logger.error(f"Integration failed: {e}")
            self.stats['total_integrations'] += 1
            self.stats['failed_integrations'] += 1
            raise e

    async def execute_reasoning_driven_schema(self, reasoning_result: ReasoningResult,
                                            base_schema: Optional[TreeChart] = None) -> ExecutionResult:
        """执行推理驱动的图式"""
        try:
            self.stats['reasoning_driven_executions'] += 1

            # 将推理结果转换为图式
            if base_schema:
                enhanced_schema = await self.reasoning_to_schema_converter.enhance_schema_with_reasoning(
                    base_schema, reasoning_result
                )
            else:
                enhanced_schema = await self.reasoning_to_schema_converter.convert_reasoning_to_schema(
                    reasoning_result
                )

            # 创建执行上下文
            context = ExecutionContext(context_id=f"reasoning_driven_{reasoning_result.result_id}")

            # 将推理结果添加到上下文
            context.set_variable('reasoning_result', reasoning_result)
            context.set_variable('reasoning_conclusions', reasoning_result.conclusions)
            context.set_variable('reasoning_confidence', reasoning_result.confidence)

            # 执行增强的图式
            execution_result = await schema_executor.execute(enhanced_schema, context)

            self.logger.info(f"Reasoning-driven schema execution completed: {execution_result.success}")
            return execution_result

        except Exception as e:
            self.logger.error(f"Reasoning-driven execution failed: {e}")
            return ExecutionResult(
                success=False,
                result=None,
                error=str(e),
                execution_time=0.0,
                metadata={'type': 'reasoning_driven_execution_error'}
            )

    async def perform_schema_driven_reasoning(self, schema: TreeChart,
                                            execution_result: ExecutionResult) -> ReasoningResult:
        """执行图式驱动的推理"""
        try:
            self.stats['schema_driven_reasonings'] += 1

            # 将图式和执行结果转换为推理请求
            reasoning_request = await self.schema_to_reasoning_converter.convert_schema_to_reasoning_request(
                schema, execution_result
            )

            # 执行推理
            reasoning_result = await self.reasoning_engine.perform_reasoning(reasoning_request)

            self.logger.info(f"Schema-driven reasoning completed: {len(reasoning_result.conclusions)} conclusions")
            return reasoning_result

        except Exception as e:
            self.logger.error(f"Schema-driven reasoning failed: {e}")
            return ReasoningResult(
                result_id=str(uuid.uuid4()),
                request_id="schema_driven_error",
                conclusions=[],
                reasoning_path=[],
                confidence=0.0,
                execution_time=0.0,
                metadata={'error': str(e)}
            )

    async def _reasoning_to_schema_strategy(self, binding: SchemaReasoningBinding):
        """推理到图式的集成策略"""
        try:
            if not binding.reasoning_result:
                return

            # 使用推理结果增强图式
            enhanced_schema = await self.reasoning_to_schema_converter.enhance_schema_with_reasoning(
                binding.schema, binding.reasoning_result
            )

            # 更新绑定中的图式
            binding.schema = enhanced_schema
            binding.binding_strength = min(1.0, binding.binding_strength + 0.2)

        except Exception as e:
            self.logger.warning(f"Reasoning-to-schema strategy failed: {e}")

    async def _schema_to_reasoning_strategy(self, binding: SchemaReasoningBinding):
        """图式到推理的集成策略"""
        try:
            # 从图式中提取推理线索
            reasoning_clues = await self.schema_to_reasoning_converter.extract_reasoning_clues(binding.schema)

            # 创建新的推理请求
            enhanced_request = ReasoningRequest(
                request_id=f"enhanced_{binding.reasoning_request.request_id}",
                reasoning_type=binding.reasoning_request.reasoning_type,
                premises=binding.reasoning_request.premises + reasoning_clues,
                goals=binding.reasoning_request.goals,
                context_data={
                    **binding.reasoning_request.context_data,
                    'schema_id': binding.schema.chart_id,
                    'schema_type': binding.schema.schema_type.value if hasattr(binding.schema, 'schema_type') else 'unknown'
                }
            )

            # 执行增强的推理
            enhanced_result = await self.reasoning_engine.perform_reasoning(enhanced_request)
            binding.reasoning_result = enhanced_result
            binding.binding_strength = min(1.0, binding.binding_strength + 0.2)

        except Exception as e:
            self.logger.warning(f"Schema-to-reasoning strategy failed: {e}")

    async def _bidirectional_strategy(self, binding: SchemaReasoningBinding):
        """双向集成策略"""
        try:
            # 先执行推理到图式
            await self._reasoning_to_schema_strategy(binding)

            # 再执行图式到推理
            await self._schema_to_reasoning_strategy(binding)

            # 增加绑定强度
            binding.binding_strength = min(1.0, binding.binding_strength + 0.3)

        except Exception as e:
            self.logger.warning(f"Bidirectional strategy failed: {e}")

    async def _feedback_loop_strategy(self, binding: SchemaReasoningBinding):
        """反馈循环策略"""
        try:
            self.stats['feedback_loops'] += 1

            max_iterations = 3
            for iteration in range(max_iterations):
                # 执行双向集成
                await self._bidirectional_strategy(binding)

                # 检查收敛条件
                if binding.reasoning_result and binding.reasoning_result.confidence > 0.8:
                    break

                # 等待一小段时间
                await asyncio.sleep(0.1)

            binding.binding_strength = min(1.0, binding.binding_strength + 0.4)

        except Exception as e:
            self.logger.warning(f"Feedback loop strategy failed: {e}")

    def get_binding(self, binding_id: str) -> Optional[SchemaReasoningBinding]:
        """获取绑定"""
        with self.binding_lock:
            return self.bindings.get(binding_id)

    def remove_binding(self, binding_id: str) -> bool:
        """移除绑定"""
        with self.binding_lock:
            if binding_id in self.bindings:
                del self.bindings[binding_id]
                return True
            return False

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.binding_lock:
            return {
                **self.stats,
                'active_bindings': len(self.bindings),
                'reasoning_engine_stats': self.reasoning_engine.get_statistics()
            }


class SchemaToReasoningConverter:
    """图式到推理转换器"""

    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)

    async def convert_schema_to_reasoning_request(self, schema: TreeChart,
                                                execution_result: ExecutionResult) -> ReasoningRequest:
        """将图式转换为推理请求"""
        try:
            # 从图式中提取前提
            premises = await self._extract_premises_from_schema(schema)

            # 从执行结果中提取目标
            goals = await self._extract_goals_from_execution_result(execution_result)

            # 确定推理类型
            reasoning_type = self._determine_reasoning_type(schema)

            return ReasoningRequest(
                request_id=f"schema_driven_{schema.chart_id}",
                reasoning_type=reasoning_type,
                premises=premises,
                goals=goals,
                context_data={
                    'schema_id': schema.chart_id,
                    'execution_success': execution_result.success,
                    'execution_time': execution_result.execution_time
                }
            )

        except Exception as e:
            self.logger.error(f"Schema to reasoning conversion failed: {e}")
            raise e

    async def extract_reasoning_clues(self, schema: TreeChart) -> List[Any]:
        """从图式中提取推理线索"""
        clues = []

        try:
            # 从图式参数中提取线索
            if hasattr(schema, 'action_params') and schema.action_params:
                for key, value in schema.action_params.items():
                    clue = {
                        'type': 'schema_parameter',
                        'key': key,
                        'value': value,
                        'source': 'action_params'
                    }
                    clues.append(clue)

            # 从子图式中提取线索
            if hasattr(schema, 'sub_charts') and schema.sub_charts:
                for sub_chart in schema.sub_charts:
                    sub_clues = await self.extract_reasoning_clues(sub_chart)
                    clues.extend(sub_clues)

        except Exception as e:
            self.logger.warning(f"Clue extraction failed: {e}")

        return clues

    async def _extract_premises_from_schema(self, schema: TreeChart) -> List[Any]:
        """从图式中提取前提"""
        premises = []

        try:
            # 基本前提：图式本身
            premise = {
                'type': 'schema_premise',
                'schema_id': schema.chart_id,
                'schema_type': schema.schema_type.value if hasattr(schema, 'schema_type') else 'unknown'
            }
            premises.append(premise)

            # 从动作参数中提取前提
            if hasattr(schema, 'action_params') and schema.action_params:
                for key, value in schema.action_params.items():
                    param_premise = {
                        'type': 'parameter_premise',
                        'parameter': key,
                        'value': value
                    }
                    premises.append(param_premise)

        except Exception as e:
            self.logger.warning(f"Premise extraction failed: {e}")

        return premises

    async def _extract_goals_from_execution_result(self, execution_result: ExecutionResult) -> List[Any]:
        """从执行结果中提取目标"""
        goals = []

        try:
            # 基本目标：执行成功
            goal = {
                'type': 'execution_goal',
                'target': 'successful_execution',
                'achieved': execution_result.success
            }
            goals.append(goal)

            # 从结果中提取更多目标
            if execution_result.result:
                result_goal = {
                    'type': 'result_goal',
                    'target': 'meaningful_result',
                    'result': execution_result.result
                }
                goals.append(result_goal)

        except Exception as e:
            self.logger.warning(f"Goal extraction failed: {e}")

        return goals

    def _determine_reasoning_type(self, schema: TreeChart) -> ReasoningType:
        """确定推理类型"""
        try:
            # 根据图式类型确定推理类型
            if hasattr(schema, 'schema_type'):
                if schema.schema_type == SchemaType.CONDITION:
                    return ReasoningType.DEDUCTION
                elif schema.schema_type == SchemaType.LOOP:
                    return ReasoningType.INDUCTION
                elif schema.schema_type == SchemaType.ACTION:
                    return ReasoningType.PROCEDURAL
                elif schema.schema_type == SchemaType.SEQUENCE:
                    return ReasoningType.TEMPORAL

            # 默认使用演绎推理
            return ReasoningType.DEDUCTION

        except Exception as e:
            self.logger.warning(f"Reasoning type determination failed: {e}")
            return ReasoningType.DEDUCTION


class ReasoningToSchemaConverter:
    """推理到图式转换器"""

    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)

    async def convert_reasoning_to_schema(self, reasoning_result: ReasoningResult) -> TreeChart:
        """将推理结果转换为图式"""
        try:
            # 根据推理结果创建动作图式
            actions = []

            for i, conclusion in enumerate(reasoning_result.conclusions):
                action = schema_factory.create_action_schema(
                    action_type='reasoning_conclusion',
                    action_params={
                        'conclusion': conclusion,
                        'confidence': reasoning_result.confidence,
                        'step': i
                    },
                    schema_id=f"conclusion_{i}_{reasoning_result.result_id}"
                )
                actions.append(action)

            # 创建序列图式
            if len(actions) == 1:
                return actions[0]
            else:
                return schema_factory.create_sequence_schema(
                    steps=actions,
                    schema_id=f"reasoning_schema_{reasoning_result.result_id}"
                )

        except Exception as e:
            self.logger.error(f"Reasoning to schema conversion failed: {e}")
            raise e

    async def enhance_schema_with_reasoning(self, base_schema: TreeChart,
                                         reasoning_result: ReasoningResult) -> TreeChart:
        """使用推理结果增强图式"""
        try:
            # 创建推理增强动作
            reasoning_action = schema_factory.create_action_schema(
                action_type='apply_reasoning_results',
                action_params={
                    'reasoning_result': reasoning_result,
                    'conclusions': reasoning_result.conclusions,
                    'confidence': reasoning_result.confidence
                },
                schema_id=f"reasoning_enhancement_{reasoning_result.result_id}"
            )

            # 将推理动作与原图式组合
            enhanced_schema = schema_factory.create_sequence_schema(
                steps=[reasoning_action, base_schema],
                schema_id=f"enhanced_{base_schema.chart_id}"
            )

            return enhanced_schema

        except Exception as e:
            self.logger.error(f"Schema enhancement failed: {e}")
            return base_schema


# 全局实例
nars_reasoning_engine = NarsReasoningEngine()
schema_reasoning_integrator = SchemaReasoningIntegrator(nars_reasoning_engine)
