"""
高级变量绑定与参数传递系统
"""

import logging
import time
import threading
from abc import ABC, abstractmethod
from collections import defaultdict, OrderedDict
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Callable, Union, Set, Tuple
from enum import Enum
from weakref import WeakValueDictionary
import copy


class VariableType(Enum):
    """变量类型枚举"""
    PRIMITIVE = "primitive"  # 基本类型
    OBJECT = "object"       # 对象类型
    REFERENCE = "reference" # 引用类型
    FUNCTION = "function"   # 函数类型
    SCHEMA = "schema"       # 图式类型


class ScopeType(Enum):
    """作用域类型枚举"""
    GLOBAL = "global"       # 全局作用域
    MODULE = "module"       # 模块作用域
    CLASS = "class"         # 类作用域
    FUNCTION = "function"   # 函数作用域
    BLOCK = "block"         # 块作用域
    LOOP = "loop"           # 循环作用域
    CONDITION = "condition" # 条件作用域


class AccessMode(Enum):
    """访问模式枚举"""
    READ_ONLY = "read_only"
    READ_WRITE = "read_write"
    WRITE_ONLY = "write_only"
    EXECUTE = "execute"


@dataclass
class VariableMetadata:
    """变量元数据"""
    variable_type: VariableType
    access_mode: AccessMode = AccessMode.READ_WRITE
    created_time: float = field(default_factory=time.time)
    last_accessed: float = field(default_factory=time.time)
    last_modified: float = field(default_factory=time.time)
    access_count: int = 0
    modification_count: int = 0
    tags: Set[str] = field(default_factory=set)
    description: str = ""
    source_location: Optional[str] = None
    
    def update_access(self):
        """更新访问信息"""
        self.last_accessed = time.time()
        self.access_count += 1
    
    def update_modification(self):
        """更新修改信息"""
        self.last_modified = time.time()
        self.modification_count += 1


@dataclass
class Variable:
    """变量对象"""
    name: str
    value: Any
    metadata: VariableMetadata
    scope_id: str
    parent_variable: Optional['Variable'] = None
    child_variables: List['Variable'] = field(default_factory=list)
    
    def get_value(self) -> Any:
        """获取变量值"""
        self.metadata.update_access()
        return self.value
    
    def set_value(self, value: Any):
        """设置变量值"""
        if self.metadata.access_mode == AccessMode.READ_ONLY:
            raise ValueError(f"Variable '{self.name}' is read-only")
        
        self.value = value
        self.metadata.update_modification()
    
    def clone(self) -> 'Variable':
        """克隆变量"""
        cloned_metadata = copy.deepcopy(self.metadata)
        cloned_value = copy.deepcopy(self.value)
        
        return Variable(
            name=self.name,
            value=cloned_value,
            metadata=cloned_metadata,
            scope_id=self.scope_id
        )


class Scope:
    """作用域类"""
    
    def __init__(self, scope_id: str, scope_type: ScopeType, parent_scope: Optional['Scope'] = None):
        self.scope_id = scope_id
        self.scope_type = scope_type
        self.parent_scope = parent_scope
        self.child_scopes: List['Scope'] = []
        self.variables: Dict[str, Variable] = {}
        self.created_time = time.time()
        self.access_count = 0
        self.is_active = True
        
        # 将自己添加到父作用域的子作用域列表中
        if parent_scope:
            parent_scope.child_scopes.append(self)
    
    def define_variable(self, name: str, value: Any, variable_type: VariableType = VariableType.PRIMITIVE,
                       access_mode: AccessMode = AccessMode.READ_WRITE, **metadata_kwargs) -> Variable:
        """定义变量"""
        if name in self.variables:
            raise ValueError(f"Variable '{name}' already exists in scope '{self.scope_id}'")
        
        metadata = VariableMetadata(
            variable_type=variable_type,
            access_mode=access_mode,
            **metadata_kwargs
        )
        
        variable = Variable(
            name=name,
            value=value,
            metadata=metadata,
            scope_id=self.scope_id
        )
        
        self.variables[name] = variable
        return variable
    
    def get_variable(self, name: str, search_parent: bool = True) -> Optional[Variable]:
        """获取变量"""
        self.access_count += 1
        
        if name in self.variables:
            return self.variables[name]
        
        if search_parent and self.parent_scope:
            return self.parent_scope.get_variable(name, search_parent)
        
        return None
    
    def set_variable(self, name: str, value: Any, create_if_not_exists: bool = False) -> bool:
        """设置变量值"""
        variable = self.get_variable(name, search_parent=True)
        
        if variable:
            variable.set_value(value)
            return True
        elif create_if_not_exists:
            self.define_variable(name, value)
            return True
        else:
            return False
    
    def delete_variable(self, name: str) -> bool:
        """删除变量"""
        if name in self.variables:
            del self.variables[name]
            return True
        return False
    
    def get_all_variables(self, include_parent: bool = False) -> Dict[str, Variable]:
        """获取所有变量"""
        all_vars = self.variables.copy()
        
        if include_parent and self.parent_scope:
            parent_vars = self.parent_scope.get_all_variables(include_parent=True)
            # 当前作用域的变量优先级更高
            for name, var in parent_vars.items():
                if name not in all_vars:
                    all_vars[name] = var
        
        return all_vars
    
    def cleanup(self):
        """清理作用域"""
        self.is_active = False
        self.variables.clear()
        
        # 从父作用域中移除自己
        if self.parent_scope and self in self.parent_scope.child_scopes:
            self.parent_scope.child_scopes.remove(self)


class VariableCache:
    """变量缓存系统"""
    
    def __init__(self, max_size: int = 1000, ttl: float = 3600):
        self.max_size = max_size
        self.ttl = ttl  # 生存时间（秒）
        self.cache: OrderedDict[str, Tuple[Any, float]] = OrderedDict()
        self.access_count = 0
        self.hit_count = 0
        self.miss_count = 0
        self.lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self.lock:
            self.access_count += 1
            
            if key in self.cache:
                value, timestamp = self.cache[key]
                
                # 检查是否过期
                if time.time() - timestamp <= self.ttl:
                    # 移动到末尾（LRU）
                    self.cache.move_to_end(key)
                    self.hit_count += 1
                    return value
                else:
                    # 过期，删除
                    del self.cache[key]
            
            self.miss_count += 1
            return None
    
    def put(self, key: str, value: Any):
        """放入缓存"""
        with self.lock:
            # 如果已存在，更新
            if key in self.cache:
                self.cache[key] = (value, time.time())
                self.cache.move_to_end(key)
                return
            
            # 检查缓存大小
            if len(self.cache) >= self.max_size:
                # 删除最旧的项
                self.cache.popitem(last=False)
            
            self.cache[key] = (value, time.time())
    
    def invalidate(self, key: str):
        """使缓存失效"""
        with self.lock:
            if key in self.cache:
                del self.cache[key]
    
    def clear(self):
        """清空缓存"""
        with self.lock:
            self.cache.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self.lock:
            hit_rate = self.hit_count / max(self.access_count, 1)
            return {
                'size': len(self.cache),
                'max_size': self.max_size,
                'access_count': self.access_count,
                'hit_count': self.hit_count,
                'miss_count': self.miss_count,
                'hit_rate': hit_rate,
                'ttl': self.ttl
            }


class VariableBindingSystem:
    """变量绑定系统"""
    
    def __init__(self, enable_caching: bool = True, cache_size: int = 1000, cache_ttl: float = 3600):
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 作用域管理
        self.scopes: Dict[str, Scope] = {}
        self.current_scope: Optional[Scope] = None
        self.scope_stack: List[Scope] = []
        
        # 缓存系统
        self.enable_caching = enable_caching
        self.variable_cache = VariableCache(cache_size, cache_ttl) if enable_caching else None
        self.resolution_cache = VariableCache(cache_size // 2, cache_ttl) if enable_caching else None
        
        # 变量解析器
        self.resolvers: Dict[str, Callable] = {}
        
        # 统计信息
        self.stats = {
            'variable_lookups': 0,
            'variable_assignments': 0,
            'scope_creations': 0,
            'scope_destructions': 0,
            'cache_operations': 0
        }
        
        # 线程安全
        self.lock = threading.RLock()
        
        # 创建全局作用域
        self.global_scope = self.create_scope("global", ScopeType.GLOBAL)
        self.current_scope = self.global_scope
    
    def create_scope(self, scope_id: str, scope_type: ScopeType, 
                    parent_scope: Optional[Scope] = None) -> Scope:
        """创建作用域"""
        with self.lock:
            if scope_id in self.scopes:
                raise ValueError(f"Scope '{scope_id}' already exists")
            
            if parent_scope is None:
                parent_scope = self.current_scope
            
            scope = Scope(scope_id, scope_type, parent_scope)
            self.scopes[scope_id] = scope
            self.stats['scope_creations'] += 1
            
            self.logger.debug(f"Created scope '{scope_id}' of type {scope_type.value}")
            return scope
    
    def enter_scope(self, scope: Union[Scope, str]):
        """进入作用域"""
        with self.lock:
            if isinstance(scope, str):
                scope = self.scopes.get(scope)
                if not scope:
                    raise ValueError(f"Scope '{scope}' not found")
            
            self.scope_stack.append(self.current_scope)
            self.current_scope = scope
            
            self.logger.debug(f"Entered scope '{scope.scope_id}'")
    
    def exit_scope(self) -> Optional[Scope]:
        """退出作用域"""
        with self.lock:
            if not self.scope_stack:
                self.logger.warning("No scope to exit")
                return None
            
            previous_scope = self.current_scope
            self.current_scope = self.scope_stack.pop()
            
            self.logger.debug(f"Exited scope '{previous_scope.scope_id}', returned to '{self.current_scope.scope_id}'")
            return previous_scope
    
    def destroy_scope(self, scope_id: str):
        """销毁作用域"""
        with self.lock:
            if scope_id not in self.scopes:
                return
            
            scope = self.scopes[scope_id]
            
            # 如果是当前作用域，先退出
            if self.current_scope == scope:
                self.exit_scope()
            
            # 清理作用域
            scope.cleanup()
            del self.scopes[scope_id]
            self.stats['scope_destructions'] += 1
            
            # 清理相关缓存
            if self.enable_caching:
                self._invalidate_scope_cache(scope_id)
            
            self.logger.debug(f"Destroyed scope '{scope_id}'")
    
    def define_variable(self, name: str, value: Any, variable_type: VariableType = VariableType.PRIMITIVE,
                       access_mode: AccessMode = AccessMode.READ_WRITE, scope: Optional[Scope] = None,
                       **metadata_kwargs) -> Variable:
        """定义变量"""
        with self.lock:
            target_scope = scope or self.current_scope
            if not target_scope:
                raise ValueError("No active scope")
            
            variable = target_scope.define_variable(name, value, variable_type, access_mode, **metadata_kwargs)
            
            # 更新缓存
            if self.enable_caching:
                cache_key = f"{target_scope.scope_id}:{name}"
                self.variable_cache.put(cache_key, variable)
                self.stats['cache_operations'] += 1
            
            self.logger.debug(f"Defined variable '{name}' in scope '{target_scope.scope_id}'")
            return variable
    
    def get_variable(self, name: str, scope: Optional[Scope] = None, 
                    search_parent: bool = True) -> Optional[Variable]:
        """获取变量"""
        with self.lock:
            self.stats['variable_lookups'] += 1
            
            target_scope = scope or self.current_scope
            if not target_scope:
                return None
            
            # 检查缓存
            if self.enable_caching:
                cache_key = f"{target_scope.scope_id}:{name}"
                cached_variable = self.variable_cache.get(cache_key)
                if cached_variable:
                    self.stats['cache_operations'] += 1
                    return cached_variable
            
            # 从作用域获取
            variable = target_scope.get_variable(name, search_parent)
            
            # 更新缓存
            if variable and self.enable_caching:
                cache_key = f"{target_scope.scope_id}:{name}"
                self.variable_cache.put(cache_key, variable)
                self.stats['cache_operations'] += 1
            
            return variable
    
    def set_variable(self, name: str, value: Any, scope: Optional[Scope] = None,
                    create_if_not_exists: bool = False) -> bool:
        """设置变量值"""
        with self.lock:
            self.stats['variable_assignments'] += 1
            
            target_scope = scope or self.current_scope
            if not target_scope:
                return False
            
            success = target_scope.set_variable(name, value, create_if_not_exists)
            
            # 更新缓存
            if success and self.enable_caching:
                cache_key = f"{target_scope.scope_id}:{name}"
                variable = target_scope.get_variable(name, search_parent=False)
                if variable:
                    self.variable_cache.put(cache_key, variable)
                    self.stats['cache_operations'] += 1
            
            return success
    
    def resolve_variable(self, expression: str, context: Optional[Dict[str, Any]] = None) -> Any:
        """解析变量表达式"""
        with self.lock:
            # 检查解析缓存
            if self.enable_caching:
                cache_key = f"resolve:{expression}"
                cached_result = self.resolution_cache.get(cache_key)
                if cached_result is not None:
                    return cached_result
            
            # 简单的变量解析（可以扩展为更复杂的表达式解析）
            if expression.startswith('$'):
                # 变量引用
                var_name = expression[1:]
                variable = self.get_variable(var_name)
                result = variable.get_value() if variable else None
            elif expression in self.resolvers:
                # 自定义解析器
                resolver = self.resolvers[expression]
                result = resolver(expression, context or {})
            else:
                # 直接返回表达式
                result = expression
            
            # 更新解析缓存
            if self.enable_caching:
                cache_key = f"resolve:{expression}"
                self.resolution_cache.put(cache_key, result)
            
            return result
    
    def register_resolver(self, pattern: str, resolver: Callable):
        """注册变量解析器"""
        self.resolvers[pattern] = resolver
        self.logger.debug(f"Registered resolver for pattern '{pattern}'")
    
    def bind_parameters(self, parameters: Dict[str, Any], target_scope: Optional[Scope] = None) -> Dict[str, Variable]:
        """批量绑定参数"""
        with self.lock:
            target_scope = target_scope or self.current_scope
            if not target_scope:
                raise ValueError("No active scope")
            
            bound_variables = {}
            
            for name, value in parameters.items():
                try:
                    # 解析值（如果是表达式）
                    if isinstance(value, str) and value.startswith('$'):
                        resolved_value = self.resolve_variable(value)
                    else:
                        resolved_value = value
                    
                    # 定义变量
                    variable = self.define_variable(name, resolved_value, scope=target_scope)
                    bound_variables[name] = variable
                    
                except Exception as e:
                    self.logger.warning(f"Failed to bind parameter '{name}': {e}")
            
            self.logger.debug(f"Bound {len(bound_variables)} parameters to scope '{target_scope.scope_id}'")
            return bound_variables
    
    def create_variable_snapshot(self, scope: Optional[Scope] = None) -> Dict[str, Any]:
        """创建变量快照"""
        with self.lock:
            target_scope = scope or self.current_scope
            if not target_scope:
                return {}
            
            snapshot = {}
            variables = target_scope.get_all_variables(include_parent=True)
            
            for name, variable in variables.items():
                try:
                    # 深拷贝变量值
                    snapshot[name] = copy.deepcopy(variable.get_value())
                except Exception as e:
                    self.logger.warning(f"Failed to snapshot variable '{name}': {e}")
                    snapshot[name] = None
            
            return snapshot
    
    def restore_variable_snapshot(self, snapshot: Dict[str, Any], scope: Optional[Scope] = None):
        """恢复变量快照"""
        with self.lock:
            target_scope = scope or self.current_scope
            if not target_scope:
                return
            
            for name, value in snapshot.items():
                try:
                    self.set_variable(name, value, target_scope, create_if_not_exists=True)
                except Exception as e:
                    self.logger.warning(f"Failed to restore variable '{name}': {e}")
            
            self.logger.debug(f"Restored {len(snapshot)} variables to scope '{target_scope.scope_id}'")
    
    def _invalidate_scope_cache(self, scope_id: str):
        """使作用域相关的缓存失效"""
        if not self.enable_caching:
            return
        
        # 简单实现：清空所有缓存
        # 更高效的实现可以只清空相关的缓存项
        self.variable_cache.clear()
        self.resolution_cache.clear()
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.lock:
            stats = self.stats.copy()
            stats.update({
                'active_scopes': len(self.scopes),
                'current_scope': self.current_scope.scope_id if self.current_scope else None,
                'scope_stack_depth': len(self.scope_stack)
            })
            
            if self.enable_caching:
                stats['variable_cache'] = self.variable_cache.get_stats()
                stats['resolution_cache'] = self.resolution_cache.get_stats()
            
            return stats
    
    def cleanup(self):
        """清理系统"""
        with self.lock:
            # 销毁所有作用域（除了全局作用域）
            scope_ids = list(self.scopes.keys())
            for scope_id in scope_ids:
                if scope_id != "global":
                    self.destroy_scope(scope_id)
            
            # 清理缓存
            if self.enable_caching:
                self.variable_cache.clear()
                self.resolution_cache.clear()
            
            # 重置统计
            self.stats = {key: 0 for key in self.stats}
            
            self.logger.info("Variable binding system cleaned up")


# 全局变量绑定系统实例
variable_binding_system = VariableBindingSystem()
