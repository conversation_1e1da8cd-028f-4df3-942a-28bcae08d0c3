"""
可执行图式系统综合测试和验证框架
"""

import asyncio
import logging
import time
import json
import uuid
import traceback
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Callable, Union, Set, Tuple
from enum import Enum
from collections import defaultdict, deque
import threading
import statistics

# 导入所有需要测试的模块
from .TreeChart import TreeChart, SchemaType, ExecutionMode, ChartStatus
from .SchemaExecutor import schema_executor, ExecutionContext, ExecutionResult
from .SchemaFactory import schema_factory
from .VariableBindingSystem import variable_binding_system, VariableType, ScopeType
from .ParameterManager import parameter_manager, ParameterDefinition, ParameterDirection
from .SchemaSearchIntegration import schema_search_integrator, search_driven_executor
from .NarsSchemaIntegration import nars_reasoning_engine, schema_reasoning_integrator


class TestType(Enum):
    """测试类型"""
    UNIT = "unit"                    # 单元测试
    INTEGRATION = "integration"      # 集成测试
    PERFORMANCE = "performance"      # 性能测试
    STRESS = "stress"               # 压力测试
    FUNCTIONAL = "functional"        # 功能测试
    REGRESSION = "regression"        # 回归测试
    END_TO_END = "end_to_end"       # 端到端测试


class TestStatus(Enum):
    """测试状态"""
    PENDING = "pending"
    RUNNING = "running"
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    ERROR = "error"


@dataclass
class TestCase:
    """测试用例"""
    test_id: str
    name: str
    description: str
    test_type: TestType
    test_function: Callable
    setup_function: Optional[Callable] = None
    teardown_function: Optional[Callable] = None
    timeout: float = 30.0
    retry_count: int = 0
    dependencies: List[str] = field(default_factory=list)
    tags: Set[str] = field(default_factory=set)
    expected_result: Any = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class TestResult:
    """测试结果"""
    test_id: str
    status: TestStatus
    execution_time: float
    result: Any = None
    error: Optional[str] = None
    traceback: Optional[str] = None
    metrics: Dict[str, Any] = field(default_factory=dict)
    logs: List[str] = field(default_factory=list)
    timestamp: float = field(default_factory=time.time)


@dataclass
class TestSuite:
    """测试套件"""
    suite_id: str
    name: str
    description: str
    test_cases: List[TestCase] = field(default_factory=list)
    setup_function: Optional[Callable] = None
    teardown_function: Optional[Callable] = None
    parallel_execution: bool = False
    max_parallel_tests: int = 5


class TestReporter:
    """测试报告器"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.reports = []
    
    def generate_report(self, test_results: List[TestResult], 
                       suite_info: Dict[str, Any] = None) -> Dict[str, Any]:
        """生成测试报告"""
        total_tests = len(test_results)
        passed_tests = sum(1 for r in test_results if r.status == TestStatus.PASSED)
        failed_tests = sum(1 for r in test_results if r.status == TestStatus.FAILED)
        error_tests = sum(1 for r in test_results if r.status == TestStatus.ERROR)
        skipped_tests = sum(1 for r in test_results if r.status == TestStatus.SKIPPED)
        
        execution_times = [r.execution_time for r in test_results if r.execution_time > 0]
        avg_execution_time = statistics.mean(execution_times) if execution_times else 0
        total_execution_time = sum(execution_times)
        
        report = {
            'summary': {
                'total_tests': total_tests,
                'passed': passed_tests,
                'failed': failed_tests,
                'errors': error_tests,
                'skipped': skipped_tests,
                'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0,
                'total_execution_time': total_execution_time,
                'average_execution_time': avg_execution_time
            },
            'test_results': [self._format_test_result(r) for r in test_results],
            'suite_info': suite_info or {},
            'generated_at': time.time()
        }
        
        self.reports.append(report)
        return report
    
    def _format_test_result(self, result: TestResult) -> Dict[str, Any]:
        """格式化测试结果"""
        return {
            'test_id': result.test_id,
            'status': result.status.value,
            'execution_time': result.execution_time,
            'error': result.error,
            'metrics': result.metrics,
            'timestamp': result.timestamp
        }
    
    def save_report(self, report: Dict[str, Any], filename: str):
        """保存报告到文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            self.logger.info(f"Test report saved to {filename}")
        except Exception as e:
            self.logger.error(f"Failed to save report: {e}")
    
    def print_summary(self, report: Dict[str, Any]):
        """打印测试摘要"""
        summary = report['summary']
        
        print(f"\n{'='*60}")
        print(f"测试执行摘要")
        print(f"{'='*60}")
        print(f"总测试数: {summary['total_tests']}")
        print(f"通过: {summary['passed']} ({summary['passed']/summary['total_tests']*100:.1f}%)")
        print(f"失败: {summary['failed']}")
        print(f"错误: {summary['errors']}")
        print(f"跳过: {summary['skipped']}")
        print(f"成功率: {summary['success_rate']:.1f}%")
        print(f"总执行时间: {summary['total_execution_time']:.4f}s")
        print(f"平均执行时间: {summary['average_execution_time']:.4f}s")
        
        # 显示失败的测试
        failed_tests = [r for r in report['test_results'] if r['status'] in ['failed', 'error']]
        if failed_tests:
            print(f"\n失败的测试:")
            for test in failed_tests:
                print(f"  - {test['test_id']}: {test['error']}")


class TestRunner:
    """测试运行器"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.reporter = TestReporter()
        self.test_suites: Dict[str, TestSuite] = {}
        self.global_setup_functions = []
        self.global_teardown_functions = []
        
        # 执行统计
        self.execution_stats = {
            'total_suites_run': 0,
            'total_tests_run': 0,
            'total_execution_time': 0.0,
            'setup_time': 0.0,
            'teardown_time': 0.0
        }
    
    def register_test_suite(self, test_suite: TestSuite):
        """注册测试套件"""
        self.test_suites[test_suite.suite_id] = test_suite
        self.logger.info(f"Registered test suite: {test_suite.name}")
    
    def add_global_setup(self, setup_function: Callable):
        """添加全局设置函数"""
        self.global_setup_functions.append(setup_function)
    
    def add_global_teardown(self, teardown_function: Callable):
        """添加全局清理函数"""
        self.global_teardown_functions.append(teardown_function)
    
    async def run_test_suite(self, suite_id: str) -> Dict[str, Any]:
        """运行测试套件"""
        if suite_id not in self.test_suites:
            raise ValueError(f"Test suite {suite_id} not found")
        
        suite = self.test_suites[suite_id]
        self.logger.info(f"Running test suite: {suite.name}")
        
        start_time = time.time()
        test_results = []
        
        try:
            # 执行全局设置
            await self._run_global_setup()
            
            # 执行套件设置
            if suite.setup_function:
                setup_start = time.time()
                await self._run_function(suite.setup_function)
                self.execution_stats['setup_time'] += time.time() - setup_start
            
            # 执行测试用例
            if suite.parallel_execution:
                test_results = await self._run_tests_parallel(suite.test_cases, suite.max_parallel_tests)
            else:
                test_results = await self._run_tests_sequential(suite.test_cases)
            
            # 执行套件清理
            if suite.teardown_function:
                teardown_start = time.time()
                await self._run_function(suite.teardown_function)
                self.execution_stats['teardown_time'] += time.time() - teardown_start
            
            # 执行全局清理
            await self._run_global_teardown()
            
        except Exception as e:
            self.logger.error(f"Test suite execution failed: {e}")
            # 即使套件执行失败，也要尝试清理
            try:
                await self._run_global_teardown()
            except:
                pass
        
        execution_time = time.time() - start_time
        self.execution_stats['total_suites_run'] += 1
        self.execution_stats['total_tests_run'] += len(test_results)
        self.execution_stats['total_execution_time'] += execution_time
        
        # 生成报告
        suite_info = {
            'suite_id': suite.suite_id,
            'suite_name': suite.name,
            'suite_description': suite.description,
            'execution_time': execution_time,
            'parallel_execution': suite.parallel_execution
        }
        
        report = self.reporter.generate_report(test_results, suite_info)
        
        self.logger.info(f"Test suite {suite.name} completed in {execution_time:.4f}s")
        return report
    
    async def run_all_test_suites(self) -> List[Dict[str, Any]]:
        """运行所有测试套件"""
        reports = []
        
        for suite_id in self.test_suites:
            try:
                report = await self.run_test_suite(suite_id)
                reports.append(report)
            except Exception as e:
                self.logger.error(f"Failed to run test suite {suite_id}: {e}")
        
        return reports
    
    async def _run_tests_sequential(self, test_cases: List[TestCase]) -> List[TestResult]:
        """顺序执行测试用例"""
        results = []
        
        for test_case in test_cases:
            result = await self._run_single_test(test_case)
            results.append(result)
        
        return results
    
    async def _run_tests_parallel(self, test_cases: List[TestCase], max_parallel: int) -> List[TestResult]:
        """并行执行测试用例"""
        semaphore = asyncio.Semaphore(max_parallel)
        
        async def run_with_semaphore(test_case):
            async with semaphore:
                return await self._run_single_test(test_case)
        
        tasks = [run_with_semaphore(test_case) for test_case in test_cases]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                error_result = TestResult(
                    test_id=test_cases[i].test_id,
                    status=TestStatus.ERROR,
                    execution_time=0.0,
                    error=str(result),
                    traceback=traceback.format_exc()
                )
                processed_results.append(error_result)
            else:
                processed_results.append(result)
        
        return processed_results
    
    async def _run_single_test(self, test_case: TestCase) -> TestResult:
        """运行单个测试用例"""
        self.logger.debug(f"Running test: {test_case.name}")
        
        start_time = time.time()
        result = TestResult(
            test_id=test_case.test_id,
            status=TestStatus.RUNNING,
            execution_time=0.0
        )
        
        try:
            # 检查依赖
            if not await self._check_dependencies(test_case):
                result.status = TestStatus.SKIPPED
                result.error = "Dependencies not met"
                return result
            
            # 执行设置
            if test_case.setup_function:
                await self._run_function(test_case.setup_function)
            
            # 执行测试函数
            test_result = await asyncio.wait_for(
                self._run_function(test_case.test_function),
                timeout=test_case.timeout
            )
            
            # 验证结果
            if test_case.expected_result is not None:
                if test_result != test_case.expected_result:
                    result.status = TestStatus.FAILED
                    result.error = f"Expected {test_case.expected_result}, got {test_result}"
                else:
                    result.status = TestStatus.PASSED
            else:
                # 如果没有期望结果，根据返回值判断
                if test_result is False:
                    result.status = TestStatus.FAILED
                    result.error = "Test function returned False"
                else:
                    result.status = TestStatus.PASSED
            
            result.result = test_result
            
            # 执行清理
            if test_case.teardown_function:
                await self._run_function(test_case.teardown_function)
            
        except asyncio.TimeoutError:
            result.status = TestStatus.FAILED
            result.error = f"Test timed out after {test_case.timeout}s"
        except Exception as e:
            result.status = TestStatus.ERROR
            result.error = str(e)
            result.traceback = traceback.format_exc()
        
        result.execution_time = time.time() - start_time
        
        self.logger.debug(f"Test {test_case.name} completed: {result.status.value}")
        return result
    
    async def _run_function(self, func: Callable) -> Any:
        """运行函数（支持同步和异步）"""
        if asyncio.iscoroutinefunction(func):
            return await func()
        else:
            return func()
    
    async def _check_dependencies(self, test_case: TestCase) -> bool:
        """检查测试依赖"""
        # 简单实现：假设所有依赖都满足
        # 实际实现中可以检查依赖的测试是否已通过
        return True
    
    async def _run_global_setup(self):
        """运行全局设置"""
        for setup_func in self.global_setup_functions:
            try:
                await self._run_function(setup_func)
            except Exception as e:
                self.logger.error(f"Global setup failed: {e}")
                raise e
    
    async def _run_global_teardown(self):
        """运行全局清理"""
        for teardown_func in self.global_teardown_functions:
            try:
                await self._run_function(teardown_func)
            except Exception as e:
                self.logger.warning(f"Global teardown failed: {e}")
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """获取执行统计"""
        return self.execution_stats.copy()


class SchemaSystemTestSuite:
    """图式系统测试套件"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.test_runner = TestRunner()
        self._setup_test_suites()
    
    def _setup_test_suites(self):
        """设置测试套件"""
        # 核心功能测试套件
        core_suite = TestSuite(
            suite_id="core_functionality",
            name="核心功能测试",
            description="测试图式系统的核心功能",
            parallel_execution=False
        )
        
        # 添加核心功能测试用例
        core_suite.test_cases.extend([
            TestCase(
                test_id="schema_creation",
                name="图式创建测试",
                description="测试各种类型图式的创建",
                test_type=TestType.UNIT,
                test_function=self._test_schema_creation,
                timeout=10.0
            ),
            TestCase(
                test_id="schema_execution",
                name="图式执行测试",
                description="测试图式的基本执行功能",
                test_type=TestType.FUNCTIONAL,
                test_function=self._test_schema_execution,
                timeout=15.0
            ),
            TestCase(
                test_id="variable_binding",
                name="变量绑定测试",
                description="测试变量绑定系统",
                test_type=TestType.UNIT,
                test_function=self._test_variable_binding,
                timeout=10.0
            ),
            TestCase(
                test_id="parameter_management",
                name="参数管理测试",
                description="测试参数传递和管理",
                test_type=TestType.UNIT,
                test_function=self._test_parameter_management,
                timeout=10.0
            )
        ])
        
        self.test_runner.register_test_suite(core_suite)
        
        # 集成测试套件
        integration_suite = TestSuite(
            suite_id="integration_tests",
            name="集成测试",
            description="测试各模块间的集成",
            parallel_execution=True,
            max_parallel_tests=3
        )
        
        integration_suite.test_cases.extend([
            TestCase(
                test_id="search_integration",
                name="搜索集成测试",
                description="测试图式与搜索系统的集成",
                test_type=TestType.INTEGRATION,
                test_function=self._test_search_integration,
                timeout=20.0
            ),
            TestCase(
                test_id="nars_integration",
                name="NARS集成测试",
                description="测试图式与NARS推理系统的集成",
                test_type=TestType.INTEGRATION,
                test_function=self._test_nars_integration,
                timeout=25.0
            ),
            TestCase(
                test_id="control_flow_integration",
                name="控制流集成测试",
                description="测试优化后的控制流任务",
                test_type=TestType.INTEGRATION,
                test_function=self._test_control_flow_integration,
                timeout=20.0
            )
        ])
        
        self.test_runner.register_test_suite(integration_suite)
        
        # 性能测试套件
        performance_suite = TestSuite(
            suite_id="performance_tests",
            name="性能测试",
            description="测试系统性能和扩展性",
            parallel_execution=False
        )
        
        performance_suite.test_cases.extend([
            TestCase(
                test_id="execution_performance",
                name="执行性能测试",
                description="测试图式执行的性能",
                test_type=TestType.PERFORMANCE,
                test_function=self._test_execution_performance,
                timeout=60.0
            ),
            TestCase(
                test_id="memory_usage",
                name="内存使用测试",
                description="测试系统的内存使用情况",
                test_type=TestType.PERFORMANCE,
                test_function=self._test_memory_usage,
                timeout=30.0
            ),
            TestCase(
                test_id="concurrent_execution",
                name="并发执行测试",
                description="测试并发执行的性能和稳定性",
                test_type=TestType.STRESS,
                test_function=self._test_concurrent_execution,
                timeout=45.0
            )
        ])
        
        self.test_runner.register_test_suite(performance_suite)
    
    # 测试方法实现
    async def _test_schema_creation(self) -> bool:
        """测试图式创建"""
        try:
            # 测试动作图式创建
            action_schema = schema_factory.create_action_schema(
                action_type='test_action',
                action_params={'param1': 'value1'},
                schema_id='test_action_schema'
            )
            
            # 测试条件图式创建
            condition_schema = schema_factory.create_condition_schema(
                condition=action_schema,
                then_branch=action_schema,
                else_branch=action_schema,
                schema_id='test_condition_schema'
            )
            
            # 测试序列图式创建
            sequence_schema = schema_factory.create_sequence_schema(
                steps=[action_schema, condition_schema],
                schema_id='test_sequence_schema'
            )
            
            # 验证创建结果
            return (action_schema is not None and 
                   condition_schema is not None and 
                   sequence_schema is not None)
            
        except Exception as e:
            self.logger.error(f"Schema creation test failed: {e}")
            return False
    
    async def _test_schema_execution(self) -> bool:
        """测试图式执行"""
        try:
            # 创建测试图式
            test_schema = schema_factory.create_action_schema(
                action_type='test_execution',
                action_params={'test_param': 'test_value'},
                schema_id='execution_test_schema'
            )
            
            # 创建执行上下文
            context = ExecutionContext(context_id="execution_test")
            context.set_variable('test_var', 'test_value')
            
            # 执行图式
            result = await schema_executor.execute(test_schema, context)
            
            # 验证执行结果
            return result is not None
            
        except Exception as e:
            self.logger.error(f"Schema execution test failed: {e}")
            return False
    
    async def _test_variable_binding(self) -> bool:
        """测试变量绑定"""
        try:
            # 测试变量定义和获取
            vbs = variable_binding_system
            
            # 定义变量
            var = vbs.define_variable("test_var", "test_value", VariableType.PRIMITIVE)
            
            # 获取变量
            retrieved_var = vbs.get_variable("test_var")
            
            # 验证结果
            return (var is not None and 
                   retrieved_var is not None and 
                   retrieved_var.get_value() == "test_value")
            
        except Exception as e:
            self.logger.error(f"Variable binding test failed: {e}")
            return False
    
    async def _test_parameter_management(self) -> bool:
        """测试参数管理"""
        try:
            # 测试参数定义和绑定
            pm = parameter_manager
            
            # 定义参数
            param_def = ParameterDefinition(
                name="test_param",
                parameter_type=VariableType.PRIMITIVE,
                direction=ParameterDirection.INPUT
            )
            
            pm.register_parameter_definition("test_function", param_def)
            
            # 创建上下文并绑定参数
            context = pm.create_parameter_context("test_context")
            pm.enter_context(context)
            
            bindings = pm.bind_parameters("test_function", {"test_param": "test_value"})
            
            # 获取参数值
            value = pm.get_parameter_value("test_param")
            
            pm.exit_context()
            
            return len(bindings) > 0 and value == "test_value"
            
        except Exception as e:
            self.logger.error(f"Parameter management test failed: {e}")
            return False
    
    async def _test_search_integration(self) -> bool:
        """测试搜索集成"""
        try:
            # 启动搜索集成器
            await schema_search_integrator.start()
            
            # 创建搜索请求
            from .SchemaSearchIntegration import SearchRequest, SearchTriggerType, SearchScope
            
            request = SearchRequest(
                request_id="integration_test",
                trigger_type=SearchTriggerType.SCHEMA_EXECUTION,
                search_scope=SearchScope.CONTEXTUAL,
                query_terms=["test", "integration"],
                max_results=10
            )
            
            # 执行搜索
            result = await schema_search_integrator.search(request)
            
            await schema_search_integrator.stop()
            
            return result is not None and result.confidence >= 0
            
        except Exception as e:
            self.logger.error(f"Search integration test failed: {e}")
            return False
    
    async def _test_nars_integration(self) -> bool:
        """测试NARS集成"""
        try:
            # 测试推理引擎
            from .NarsSchemaIntegration import ReasoningRequest, ReasoningType
            
            request = ReasoningRequest(
                request_id="nars_test",
                reasoning_type=ReasoningType.DEDUCTION,
                premises=["前提1", "前提2"],
                goals=["目标1"]
            )
            
            result = await nars_reasoning_engine.perform_reasoning(request)
            
            return result is not None and result.confidence >= 0
            
        except Exception as e:
            self.logger.error(f"NARS integration test failed: {e}")
            return False
    
    async def _test_control_flow_integration(self) -> bool:
        """测试控制流集成"""
        try:
            # 这里可以测试优化后的控制流任务
            # 由于控制流任务依赖于PAM等组件，这里做简单测试
            return True
            
        except Exception as e:
            self.logger.error(f"Control flow integration test failed: {e}")
            return False
    
    async def _test_execution_performance(self) -> bool:
        """测试执行性能"""
        try:
            # 创建多个图式并测试执行时间
            schemas = []
            for i in range(100):
                schema = schema_factory.create_action_schema(
                    action_type=f'perf_test_{i}',
                    action_params={'index': i},
                    schema_id=f'perf_schema_{i}'
                )
                schemas.append(schema)
            
            start_time = time.time()
            
            # 执行所有图式
            for schema in schemas:
                context = ExecutionContext(context_id=f"perf_test_{schema.chart_id}")
                await schema_executor.execute(schema, context)
            
            execution_time = time.time() - start_time
            
            # 性能要求：100个图式执行时间应该小于5秒
            return execution_time < 5.0
            
        except Exception as e:
            self.logger.error(f"Performance test failed: {e}")
            return False
    
    async def _test_memory_usage(self) -> bool:
        """测试内存使用"""
        try:
            import psutil
            import os
            
            # 获取当前进程
            process = psutil.Process(os.getpid())
            initial_memory = process.memory_info().rss
            
            # 创建大量对象
            objects = []
            for i in range(1000):
                schema = schema_factory.create_action_schema(
                    action_type=f'memory_test_{i}',
                    action_params={'data': 'x' * 1000},  # 1KB数据
                    schema_id=f'memory_schema_{i}'
                )
                objects.append(schema)
            
            peak_memory = process.memory_info().rss
            
            # 清理对象
            objects.clear()
            
            # 内存增长应该在合理范围内（小于100MB）
            memory_growth = peak_memory - initial_memory
            return memory_growth < 100 * 1024 * 1024  # 100MB
            
        except ImportError:
            # 如果没有psutil，跳过测试
            return True
        except Exception as e:
            self.logger.error(f"Memory usage test failed: {e}")
            return False
    
    async def _test_concurrent_execution(self) -> bool:
        """测试并发执行"""
        try:
            # 创建并发执行任务
            async def execute_schema(schema_id):
                schema = schema_factory.create_action_schema(
                    action_type='concurrent_test',
                    action_params={'schema_id': schema_id},
                    schema_id=f'concurrent_schema_{schema_id}'
                )
                
                context = ExecutionContext(context_id=f"concurrent_test_{schema_id}")
                result = await schema_executor.execute(schema, context)
                return result is not None
            
            # 并发执行50个任务
            tasks = [execute_schema(i) for i in range(50)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 检查结果
            successful_tasks = sum(1 for r in results if r is True)
            
            # 至少80%的任务应该成功
            return successful_tasks >= 40
            
        except Exception as e:
            self.logger.error(f"Concurrent execution test failed: {e}")
            return False
    
    async def run_all_tests(self) -> List[Dict[str, Any]]:
        """运行所有测试"""
        self.logger.info("Starting comprehensive test execution...")
        
        reports = await self.test_runner.run_all_test_suites()
        
        # 打印总体摘要
        total_tests = sum(r['summary']['total_tests'] for r in reports)
        total_passed = sum(r['summary']['passed'] for r in reports)
        overall_success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        print(f"\n{'='*80}")
        print(f"综合测试执行完成")
        print(f"{'='*80}")
        print(f"测试套件数: {len(reports)}")
        print(f"总测试数: {total_tests}")
        print(f"总通过数: {total_passed}")
        print(f"总体成功率: {overall_success_rate:.1f}%")
        
        # 打印每个套件的摘要
        for report in reports:
            suite_info = report['suite_info']
            summary = report['summary']
            print(f"\n{suite_info['suite_name']}:")
            print(f"  测试数: {summary['total_tests']}")
            print(f"  通过: {summary['passed']}")
            print(f"  失败: {summary['failed']}")
            print(f"  成功率: {summary['success_rate']:.1f}%")
        
        return reports


# 全局测试套件实例
comprehensive_test_suite = SchemaSystemTestSuite()
