#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
TreeChart class for the LIDA framework - Refactored for executable schema support.
"""

import logging
import uuid
import time
from abc import ABC, abstractmethod
from enum import Enum
from dataclasses import dataclass, field
from linars.edu.memphis.ccrg.linars.term import Term
from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm
from typing import Dict, List, Set, Any, Optional, Collection, Tuple, Union

from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link


class SchemaType(Enum):
    """可执行图式类型枚举"""
    SEQUENCE = "sequence"
    CONDITION = "condition"
    LOOP = "loop"
    ACTION = "action"
    VARIABLE = "variable"
    COMPOSITE = "composite"
    SCENE = "scene"


class ExecutionMode(Enum):
    """执行模式枚举"""
    MACHINE = "machine"  # 机算模式（精确执行）
    HUMAN = "human"      # 人算模式（模糊执行）
    ADAPTIVE = "adaptive" # 自适应模式


class ChartStatus(Enum):
    """图式状态枚举"""
    CREATED = 0
    PARSING = 1
    PARSED = 2
    READY = 3
    EXECUTING = 4
    COMPLETED = 5
    FAILED = 6
    SUSPENDED = 7


@dataclass
class ExecutionMetadata:
    """执行元数据"""
    created_time: float = field(default_factory=time.time)
    last_modified: float = field(default_factory=time.time)
    version: str = "1.0"
    complexity: float = 0.0
    priority: float = 0.5
    execution_count: int = 0
    success_count: int = 0
    failure_count: int = 0
    average_execution_time: float = 0.0
    tags: List[str] = field(default_factory=list)
    properties: Dict[str, Any] = field(default_factory=dict)


class TreeChart(CompoundTerm):
    """
    重构后的TreeChart类，支持可执行图式功能。

    主要改进：
    1. 分离表示与执行逻辑
    2. 支持多种图式类型
    3. 增强元数据管理
    4. 改进状态管理
    5. 支持执行模式配置
    """

    chart_size = 0

    def __init__(self, *args, finding_list=None, budget=None, scene_root=None,
                 found_list=None, schema_type=SchemaType.COMPOSITE,
                 execution_mode=ExecutionMode.ADAPTIVE, chart_id=None):
        """
        初始化TreeChart。

        Args:
            *args: 图式中的项
            finding_list: 待查找的项列表
            budget: 预算值
            scene_root: 场景根节点
            found_list: 已找到的项列表
            schema_type: 图式类型
            execution_mode: 执行模式
            chart_id: 图式ID
        """
        super().__init__(*args)

        # 基本属性
        self.chart_id = chart_id or str(uuid.uuid4())
        self.schema_type = schema_type
        self.execution_mode = execution_mode
        self.status = ChartStatus.CREATED

        # 原有属性（保持兼容性）
        self.budget = budget
        self.scene_root = scene_root
        self.found_list = found_list or []
        self.finding_list = finding_list or []
        self.parent_list = []
        self.cpsize = 0  # 已完成产生式右部长度

        # 新增属性
        self.metadata = ExecutionMetadata()
        self.sub_charts = []  # 子图式
        self.parent_chart = None  # 父图式
        self.parameters = {}  # 参数字典
        self.execution_constraints = {}  # 执行约束
        self.validation_rules = []  # 验证规则
        self.execution_history = []  # 执行历史

        # 执行相关属性
        self.execution_context = None
        self.execution_result = None
        self.error_info = None

        # 初始化参数
        if args:
            self._init_params(self.finding_list, args)

        # 设置节点ID
        TreeChart.chart_size += 1
        self.set_node_id(TreeChart.chart_size)

        # 更新状态
        self.status = ChartStatus.PARSED if args else ChartStatus.CREATED

    def print_chart(self):
        """打印图式信息"""
        print(f"TreeChart[{self.chart_id}]: {self.schema_type.value}")
        print(f"  Status: {self.status.name}")
        print(f"  Budget: {self.budget}")
        print(f"  Scene Root: {self.scene_root}")
        print(f"  Found: {len(self.found_list)} items")
        print(f"  Finding: {len(self.finding_list)} items")
        print(f"  Sub-charts: {len(self.sub_charts)}")
        print(f"  Complexity: {self.metadata.complexity}")
        print(f"  Priority: {self.metadata.priority}")

    def _init_params(self, finding_list, args):
        """
        从参数初始化图式结构。

        Args:
            finding_list: 待查找的项列表
            args: 图式中的项
        """
        # 遍历arg，找到各term的sink节点并统计，数量多的为根节点
        lsize = len(args)
        if lsize == 0:
            return

        sink_count = [0] * lsize
        max_sink_count = 0
        max_sink_index = 0
        sink_index = 0

        sink_map = {}
        order_map = {}

        for i in range(lsize):
            if isinstance(args[i], Link):
                ll = args[i]

                if ll not in finding_list:
                    self.found_list.append(args[i])

                # 统计sink节点数量，找到最多的作为根节点
                sink_node = ll.get_sink()
                if sink_node in sink_map:
                    sink_index = sink_map[sink_node]
                    sink_count[sink_index] += 1
                else:
                    sink_map[sink_node] = sink_index
                    sink_count[sink_index] = 1

                if sink_count[sink_index] > max_sink_count:
                    max_sink_count = sink_count[sink_index]
                    max_sink_index = sink_index

                # 记录顺序信息
                order_map[i] = ll
            else:
                # 处理单个term，构造默认边
                self.found_list.append(args[i])

        # 设置根节点
        if lsize > 0:
            self.scene_root = args[max_sink_index] if max_sink_index < lsize else args[0]

        # 计算复杂度
        self._calculate_complexity()

        # 更新状态
        self.status = ChartStatus.PARSED
        self.metadata.last_modified = time.time()

    def _calculate_complexity(self):
        """计算图式复杂度"""
        base_complexity = 0.1

        # 基于项数量的复杂度
        item_complexity = min(0.3, len(self.found_list) * 0.05)

        # 基于子图式的复杂度
        sub_complexity = sum(sub.metadata.complexity for sub in self.sub_charts) * 0.1

        # 基于图式类型的复杂度
        type_complexity = {
            SchemaType.ACTION: 0.1,
            SchemaType.SEQUENCE: 0.3,
            SchemaType.CONDITION: 0.4,
            SchemaType.LOOP: 0.6,
            SchemaType.COMPOSITE: 0.5,
            SchemaType.SCENE: 0.2
        }.get(self.schema_type, 0.3)

        self.metadata.complexity = min(1.0, base_complexity + item_complexity + sub_complexity + type_complexity)

    def add_sub_chart(self, sub_chart: 'TreeChart'):
        """添加子图式"""
        if sub_chart not in self.sub_charts:
            sub_chart.parent_chart = self
            self.sub_charts.append(sub_chart)
            self._calculate_complexity()
            self.metadata.last_modified = time.time()

    def remove_sub_chart(self, sub_chart: 'TreeChart'):
        """移除子图式"""
        if sub_chart in self.sub_charts:
            sub_chart.parent_chart = None
            self.sub_charts.remove(sub_chart)
            self._calculate_complexity()
            self.metadata.last_modified = time.time()

    def set_parameter(self, key: str, value: Any):
        """设置参数"""
        self.parameters[key] = value
        self.metadata.last_modified = time.time()

    def get_parameter(self, key: str, default: Any = None) -> Any:
        """获取参数"""
        return self.parameters.get(key, default)

    def add_validation_rule(self, rule: callable):
        """添加验证规则"""
        if rule not in self.validation_rules:
            self.validation_rules.append(rule)

    def validate(self) -> Tuple[bool, List[str]]:
        """
        验证图式有效性

        Returns:
            (is_valid, error_messages)
        """
        errors = []

        # 基本验证
        if not self.scene_root:
            errors.append("Scene root is required")

        if self.schema_type == SchemaType.SEQUENCE and len(self.sub_charts) < 2:
            errors.append("Sequence schema requires at least 2 sub-charts")

        if self.schema_type == SchemaType.CONDITION and len(self.sub_charts) < 2:
            errors.append("Condition schema requires at least 2 sub-charts (condition and then-branch)")

        if self.schema_type == SchemaType.LOOP and len(self.sub_charts) < 2:
            errors.append("Loop schema requires at least 2 sub-charts (condition and body)")

        # 执行自定义验证规则
        for rule in self.validation_rules:
            try:
                rule_result = rule(self)
                if isinstance(rule_result, str):
                    errors.append(rule_result)
                elif isinstance(rule_result, list):
                    errors.extend(rule_result)
            except Exception as e:
                errors.append(f"Validation rule error: {str(e)}")

        # 递归验证子图式
        for sub_chart in self.sub_charts:
            is_valid, sub_errors = sub_chart.validate()
            if not is_valid:
                errors.extend([f"Sub-chart {sub_chart.chart_id}: {err}" for err in sub_errors])

        return len(errors) == 0, errors

    def get_priority(self) -> float:
        """获取优先级"""
        if self.budget and hasattr(self.budget, 'get_priority'):
            return self.budget.get_priority()
        return self.metadata.priority

    def set_priority(self, v: float):
        """设置优先级"""
        if self.budget and hasattr(self.budget, 'set_priority'):
            self.budget.set_priority(v)
        self.metadata.priority = v
        self.metadata.last_modified = time.time()

    def inc_priority(self, v: float):
        """增加优先级"""
        current = self.get_priority()
        self.set_priority(min(1.0, current + v))

    def dec_priority(self, v: float):
        """降低优先级"""
        current = self.get_priority()
        self.set_priority(max(0.0, current - v))

    def merge(self, that: 'TreeChart') -> 'TreeChart':
        """
        合并两个图式

        Args:
            that: 要合并的图式

        Returns:
            合并后的图式
        """
        if not isinstance(that, TreeChart):
            return self

        # 合并预算
        if self.budget and that.budget and hasattr(self.budget, 'merge'):
            self.budget.merge(that.budget)

        # 合并found_list和finding_list
        for item in that.found_list:
            if item not in self.found_list:
                self.found_list.append(item)

        for item in that.finding_list:
            if item not in self.finding_list:
                self.finding_list.append(item)

        # 合并子图式
        for sub_chart in that.sub_charts:
            if sub_chart not in self.sub_charts:
                self.add_sub_chart(sub_chart)

        # 合并参数
        self.parameters.update(that.parameters)

        # 合并元数据
        self.metadata.execution_count += that.metadata.execution_count
        self.metadata.success_count += that.metadata.success_count
        self.metadata.failure_count += that.metadata.failure_count

        # 更新复杂度和时间戳
        self._calculate_complexity()
        self.metadata.last_modified = time.time()

        return self

    def get_budget(self):
        """获取预算"""
        return self.budget

    def clone(self) -> 'TreeChart':
        """克隆图式"""
        # 创建新的图式
        cloned = TreeChart(
            *self.term,
            finding_list=self.finding_list.copy(),
            budget=self.budget,
            scene_root=self.scene_root,
            found_list=self.found_list.copy(),
            schema_type=self.schema_type,
            execution_mode=self.execution_mode
        )

        # 复制属性
        cloned.parameters = self.parameters.copy()
        cloned.execution_constraints = self.execution_constraints.copy()
        cloned.validation_rules = self.validation_rules.copy()

        # 复制子图式
        for sub_chart in self.sub_charts:
            cloned.add_sub_chart(sub_chart.clone())

        return cloned

    def get_execution_plan(self) -> List[Dict[str, Any]]:
        """
        获取执行计划

        Returns:
            执行计划列表
        """
        plan = []

        if self.schema_type == SchemaType.SEQUENCE:
            for i, sub_chart in enumerate(self.sub_charts):
                plan.append({
                    'step_id': i,
                    'chart': sub_chart,
                    'type': 'sequential',
                    'dependencies': [i-1] if i > 0 else [],
                    'timeout': self.execution_constraints.get('step_timeout', 60)
                })

        elif self.schema_type == SchemaType.CONDITION:
            if len(self.sub_charts) >= 2:
                plan.append({
                    'condition': self.sub_charts[0],
                    'then_branch': self.sub_charts[1],
                    'else_branch': self.sub_charts[2] if len(self.sub_charts) > 2 else None,
                    'type': 'conditional',
                    'condition_timeout': self.execution_constraints.get('condition_timeout', 30),
                    'branch_timeout': self.execution_constraints.get('branch_timeout', 300)
                })

        elif self.schema_type == SchemaType.LOOP:
            if len(self.sub_charts) >= 2:
                plan.append({
                    'condition': self.sub_charts[0],
                    'body': self.sub_charts[1],
                    'loop_type': self.get_parameter('loop_type', 'do_while'),
                    'type': 'loop',
                    'max_iterations': self.execution_constraints.get('max_iterations', 1000)
                })

        elif self.schema_type == SchemaType.ACTION:
            plan.append({
                'action_type': self.get_parameter('action_type', 'default'),
                'action_params': self.get_parameter('action_params', {}),
                'type': 'action',
                'timeout': self.execution_constraints.get('action_timeout', 60)
            })

        elif self.schema_type == SchemaType.COMPOSITE:
            # 复合图式：并行执行所有子图式
            for i, sub_chart in enumerate(self.sub_charts):
                plan.append({
                    'step_id': i,
                    'chart': sub_chart,
                    'type': 'parallel',
                    'dependencies': [],
                    'timeout': self.execution_constraints.get('step_timeout', 60)
                })

        return plan

    def update_execution_stats(self, success: bool, execution_time: float):
        """更新执行统计"""
        self.metadata.execution_count += 1
        if success:
            self.metadata.success_count += 1
        else:
            self.metadata.failure_count += 1

        # 更新平均执行时间
        total_count = self.metadata.execution_count
        current_avg = self.metadata.average_execution_time
        self.metadata.average_execution_time = (
            (current_avg * (total_count - 1) + execution_time) / total_count
        )

        self.metadata.last_modified = time.time()

    def get_success_rate(self) -> float:
        """获取成功率"""
        if self.metadata.execution_count == 0:
            return 0.0
        return self.metadata.success_count / self.metadata.execution_count

    def chart_cmp(self, other: 'TreeChart') -> bool:
        """
        比较两个图式是否相等

        Args:
            other: 要比较的图式

        Returns:
            如果图式相等返回True，否则返回False
        """
        if not isinstance(other, TreeChart):
            return False

        # 比较基本属性
        if self.schema_type != other.schema_type:
            return False

        if not self._term_cmp(self.scene_root, other.scene_root):
            return False

        if not self._array_list_cmp(list(self.found_list), list(other.found_list)):
            return False

        if not self._array_list_cmp(list(self.finding_list), list(other.finding_list)):
            return False

        if self.status != other.status:
            return False

        if self.cpsize != other.cpsize:
            return False

        # 比较参数
        if self.parameters != other.parameters:
            return False

        # 比较子图式
        if len(self.sub_charts) != len(other.sub_charts):
            return False

        for i, sub_chart in enumerate(self.sub_charts):
            if not sub_chart.chart_cmp(other.sub_charts[i]):
                return False

        return True

    def _term_cmp(self, a: Any, b: Any) -> bool:
        """
        比较两个项

        Args:
            a: 第一个项
            b: 第二个项

        Returns:
            如果项相等返回True，否则返回False
        """
        if a is None and b is None:
            return True

        if a is None or b is None:
            return False

        if isinstance(a, CompoundTerm) and isinstance(b, CompoundTerm):
            return str(a) == str(b)
        elif isinstance(a, Term) and isinstance(b, Term):
            return str(a) == str(b)
        else:
            return str(a) == str(b)

    def _array_list_cmp(self, a: List[Any], b: List[Any]) -> bool:
        """
        比较两个列表

        Args:
            a: 第一个列表
            b: 第二个列表

        Returns:
            如果列表相等返回True，否则返回False
        """
        if len(a) != len(b):
            return False

        for i in range(len(a)):
            if not self._term_cmp(a[i], b[i]):
                return False

        return True

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典表示"""
        return {
            'chart_id': self.chart_id,
            'schema_type': self.schema_type.value,
            'execution_mode': self.execution_mode.value,
            'status': self.status.name,
            'scene_root': str(self.scene_root) if self.scene_root else None,
            'found_count': len(self.found_list),
            'finding_count': len(self.finding_list),
            'sub_charts_count': len(self.sub_charts),
            'parameters': self.parameters,
            'metadata': {
                'complexity': self.metadata.complexity,
                'priority': self.metadata.priority,
                'execution_count': self.metadata.execution_count,
                'success_count': self.metadata.success_count,
                'failure_count': self.metadata.failure_count,
                'success_rate': self.get_success_rate(),
                'average_execution_time': self.metadata.average_execution_time,
                'created_time': self.metadata.created_time,
                'last_modified': self.metadata.last_modified
            }
        }

    def __str__(self) -> str:
        """字符串表示"""
        return f"TreeChart({self.chart_id}, {self.schema_type.value}, {self.status.name})"

    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"TreeChart(id={self.chart_id}, type={self.schema_type.value}, "
                f"status={self.status.name}, complexity={self.metadata.complexity:.2f}, "
                f"priority={self.metadata.priority:.2f}, sub_charts={len(self.sub_charts)})")

    def __eq__(self, other) -> bool:
        """相等性比较"""
        return self.chart_cmp(other)

    def __hash__(self) -> int:
        """哈希值计算"""
        return hash((self.chart_id, self.schema_type, str(self.scene_root)))

    # 兼容性方法（保持与原版本的兼容性）
    def get_cpsize(self) -> int:
        """获取已完成产生式右部长度"""
        return self.cpsize

    def set_cpsize(self, size: int):
        """设置已完成产生式右部长度"""
        self.cpsize = size
        self.metadata.last_modified = time.time()

    def get_status(self) -> ChartStatus:
        """获取状态"""
        return self.status

    def set_status(self, status: ChartStatus):
        """设置状态"""
        self.status = status
        self.metadata.last_modified = time.time()

    def is_complete(self) -> bool:
        """检查是否完成"""
        return self.status == ChartStatus.COMPLETED

    def is_ready_for_execution(self) -> bool:
        """检查是否准备好执行"""
        is_valid, _ = self.validate()
        return is_valid and self.status in [ChartStatus.READY, ChartStatus.PARSED]

    def mark_as_ready(self):
        """标记为准备就绪"""
        if self.status in [ChartStatus.PARSED, ChartStatus.CREATED]:
            self.status = ChartStatus.READY
            self.metadata.last_modified = time.time()

    def mark_as_executing(self):
        """标记为执行中"""
        self.status = ChartStatus.EXECUTING
        self.metadata.last_modified = time.time()

    def mark_as_completed(self):
        """标记为已完成"""
        self.status = ChartStatus.COMPLETED
        self.metadata.last_modified = time.time()

    def mark_as_failed(self, error_info: str = None):
        """标记为失败"""
        self.status = ChartStatus.FAILED
        self.error_info = error_info
        self.metadata.last_modified = time.time()
