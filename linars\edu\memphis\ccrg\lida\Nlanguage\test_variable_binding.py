"""
变量绑定与参数传递系统测试文件
"""

import asyncio
import logging
import time
from typing import Dict, Any

from .VariableBindingSystem import (
    VariableBindingSystem, VariableType, AccessMode, ScopeType,
    variable_binding_system
)
from .ParameterManager import (
    ParameterManager, ParameterDefinition, ParameterDirection, 
    ParameterPassingMode, parameter_manager
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_variable_binding_system():
    """测试变量绑定系统"""
    print("\n=== 测试变量绑定系统 ===")
    
    # 创建新的变量绑定系统实例用于测试
    vbs = VariableBindingSystem(enable_caching=True)
    
    try:
        # 测试基本变量操作
        print("1. 测试基本变量操作")
        
        # 定义变量
        var1 = vbs.define_variable("test_var", 42, VariableType.PRIMITIVE)
        print(f"定义变量 test_var = {var1.get_value()}")
        
        # 获取变量
        retrieved_var = vbs.get_variable("test_var")
        print(f"获取变量 test_var = {retrieved_var.get_value() if retrieved_var else None}")
        
        # 设置变量值
        success = vbs.set_variable("test_var", 100)
        print(f"设置变量值成功: {success}")
        print(f"更新后的值: {vbs.get_variable('test_var').get_value()}")
        
        # 测试作用域
        print("\n2. 测试作用域管理")
        
        # 创建子作用域
        child_scope = vbs.create_scope("child_scope", ScopeType.FUNCTION)
        vbs.enter_scope(child_scope)
        
        # 在子作用域中定义变量
        vbs.define_variable("child_var", "hello", VariableType.PRIMITIVE)
        print(f"子作用域变量: {vbs.get_variable('child_var').get_value()}")
        
        # 访问父作用域变量
        parent_var = vbs.get_variable("test_var")
        print(f"从子作用域访问父变量: {parent_var.get_value() if parent_var else None}")
        
        # 退出作用域
        vbs.exit_scope()
        
        # 尝试访问子作用域变量（应该失败）
        child_var_from_parent = vbs.get_variable("child_var")
        print(f"从父作用域访问子变量: {child_var_from_parent.get_value() if child_var_from_parent else 'Not found'}")
        
        # 测试变量解析
        print("\n3. 测试变量解析")
        
        # 注册自定义解析器
        def custom_resolver(expression: str, context: Dict[str, Any]) -> Any:
            if expression == "current_time":
                return time.time()
            return expression
        
        vbs.register_resolver("current_time", custom_resolver)
        
        # 解析变量
        resolved_value = vbs.resolve_variable("$test_var")
        print(f"解析变量引用: {resolved_value}")
        
        resolved_time = vbs.resolve_variable("current_time")
        print(f"解析自定义表达式: {resolved_time}")
        
        # 测试批量参数绑定
        print("\n4. 测试批量参数绑定")
        
        parameters = {
            "param1": 123,
            "param2": "test_string",
            "param3": "$test_var"  # 变量引用
        }
        
        bound_vars = vbs.bind_parameters(parameters)
        print(f"绑定参数数量: {len(bound_vars)}")
        for name, var in bound_vars.items():
            print(f"  {name} = {var.get_value()}")
        
        # 测试变量快照
        print("\n5. 测试变量快照")
        
        snapshot = vbs.create_variable_snapshot()
        print(f"创建快照，包含 {len(snapshot)} 个变量")
        
        # 修改变量
        vbs.set_variable("test_var", 999)
        print(f"修改后的值: {vbs.get_variable('test_var').get_value()}")
        
        # 恢复快照
        vbs.restore_variable_snapshot(snapshot)
        print(f"恢复快照后的值: {vbs.get_variable('test_var').get_value()}")
        
        # 获取统计信息
        stats = vbs.get_statistics()
        print(f"\n6. 系统统计信息:")
        for key, value in stats.items():
            if isinstance(value, dict):
                print(f"  {key}:")
                for sub_key, sub_value in value.items():
                    print(f"    {sub_key}: {sub_value}")
            else:
                print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        logger.error(f"变量绑定系统测试失败: {e}")
        return False
    finally:
        vbs.cleanup()


def test_parameter_manager():
    """测试参数管理器"""
    print("\n=== 测试参数管理器 ===")
    
    # 创建新的参数管理器实例用于测试
    vbs = VariableBindingSystem(enable_caching=True)
    pm = ParameterManager(vbs)
    
    try:
        # 测试参数定义注册
        print("1. 测试参数定义注册")
        
        # 定义函数参数
        param_defs = [
            ParameterDefinition(
                name="input_value",
                parameter_type=VariableType.PRIMITIVE,
                direction=ParameterDirection.INPUT,
                passing_mode=ParameterPassingMode.BY_VALUE,
                is_required=True,
                constraints={"min_value": 0, "max_value": 1000}
            ),
            ParameterDefinition(
                name="output_result",
                parameter_type=VariableType.PRIMITIVE,
                direction=ParameterDirection.OUTPUT,
                passing_mode=ParameterPassingMode.BY_REFERENCE,
                is_required=False,
                default_value=0
            ),
            ParameterDefinition(
                name="config_flag",
                parameter_type=VariableType.PRIMITIVE,
                direction=ParameterDirection.INPUT,
                passing_mode=ParameterPassingMode.BY_VALUE,
                is_required=False,
                default_value=True
            )
        ]
        
        pm.register_function_parameters("test_function", param_defs)
        print(f"注册了 {len(param_defs)} 个参数定义")
        
        # 测试参数上下文
        print("\n2. 测试参数上下文")
        
        # 创建参数上下文
        context = pm.create_parameter_context("test_context")
        pm.enter_context(context)
        
        # 绑定参数
        arguments = {
            "input_value": 42,
            "config_flag": False
        }
        
        bindings = pm.bind_parameters("test_function", arguments)
        print(f"绑定了 {len(bindings)} 个参数")
        
        # 获取参数值
        input_val = pm.get_parameter_value("input_value")
        config_val = pm.get_parameter_value("config_flag")
        print(f"input_value = {input_val}")
        print(f"config_flag = {config_val}")
        
        # 设置返回值
        pm.set_return_value(input_val * 2, "result")
        return_val = pm.get_return_value("result")
        print(f"返回值 = {return_val}")
        
        # 退出上下文
        pm.exit_context()
        
        # 测试不同的参数传递模式
        print("\n3. 测试参数传递模式")
        
        # 按引用传递测试
        ref_param = ParameterDefinition(
            name="ref_param",
            parameter_type=VariableType.OBJECT,
            passing_mode=ParameterPassingMode.BY_REFERENCE
        )
        pm.register_parameter_definition("ref_test", ref_param)
        
        # 创建新上下文
        ref_context = pm.create_parameter_context("ref_context")
        pm.enter_context(ref_context)
        
        # 测试对象
        test_obj = {"value": 100}
        pm.bind_parameters("ref_test", {"ref_param": test_obj})
        
        # 修改对象
        retrieved_obj = pm.get_parameter_value("ref_param")
        retrieved_obj["value"] = 200
        
        print(f"原对象值: {test_obj['value']}")  # 应该是200（按引用传递）
        
        pm.exit_context()
        
        # 测试类型转换
        print("\n4. 测试类型转换")
        
        # 注册自定义类型转换器
        def string_to_list_converter(value: str) -> list:
            return value.split(';')
        
        pm.register_type_converter(str, list, string_to_list_converter)
        
        # 定义需要列表类型的参数
        list_param = ParameterDefinition(
            name="list_param",
            parameter_type=VariableType.OBJECT
        )
        pm.register_parameter_definition("convert_test", list_param)
        
        # 创建上下文并测试转换
        convert_context = pm.create_parameter_context("convert_context")
        pm.enter_context(convert_context)
        
        # 传入字符串，应该自动转换为列表
        pm.bind_parameters("convert_test", {"list_param": "item1;item2;item3"})
        converted_list = pm.get_parameter_value("list_param")
        print(f"转换后的列表: {converted_list}")
        
        pm.exit_context()
        
        # 测试参数作用域
        print("\n5. 测试参数作用域")
        
        # 创建参数作用域
        param_scope = pm.create_parameter_scope("test_function", {
            "input_value": 123,
            "config_flag": True
        })
        
        vbs.enter_scope(param_scope)
        
        # 从作用域获取参数
        scope_input = vbs.get_variable("input_value")
        scope_config = vbs.get_variable("config_flag")
        
        print(f"作用域中的 input_value: {scope_input.get_value() if scope_input else None}")
        print(f"作用域中的 config_flag: {scope_config.get_value() if scope_config else None}")
        
        vbs.exit_scope()
        
        # 获取统计信息
        stats = pm.get_statistics()
        print(f"\n6. 参数管理器统计信息:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        logger.error(f"参数管理器测试失败: {e}")
        return False
    finally:
        # 清理
        for context_id in list(pm.contexts.keys()):
            pm.cleanup_context(context_id)
        vbs.cleanup()


def test_integration():
    """测试集成功能"""
    print("\n=== 测试集成功能 ===")
    
    try:
        # 使用全局实例进行集成测试
        vbs = variable_binding_system
        pm = parameter_manager
        
        # 清理之前的状态
        vbs.cleanup()
        
        # 模拟复杂的函数调用场景
        print("1. 模拟复杂函数调用")
        
        # 定义主函数参数
        main_params = [
            ParameterDefinition("data", VariableType.OBJECT, ParameterDirection.INPUT),
            ParameterDefinition("options", VariableType.OBJECT, ParameterDirection.INPUT, 
                              default_value={"verbose": False}),
            ParameterDefinition("result", VariableType.OBJECT, ParameterDirection.OUTPUT)
        ]
        pm.register_function_parameters("main_function", main_params)
        
        # 定义辅助函数参数
        helper_params = [
            ParameterDefinition("input_data", VariableType.OBJECT, ParameterDirection.INPUT),
            ParameterDefinition("processed_data", VariableType.OBJECT, ParameterDirection.OUTPUT)
        ]
        pm.register_function_parameters("helper_function", helper_params)
        
        # 创建全局变量
        vbs.define_variable("global_config", {"debug": True, "max_items": 100})
        
        # 主函数调用
        main_context = pm.create_parameter_context("main_call")
        pm.enter_context(main_context)
        
        input_data = [1, 2, 3, 4, 5]
        pm.bind_parameters("main_function", {
            "data": input_data,
            "options": {"verbose": True}
        })
        
        # 获取参数并处理
        data = pm.get_parameter_value("data")
        options = pm.get_parameter_value("options")
        
        print(f"主函数接收到数据: {data}")
        print(f"主函数选项: {options}")
        
        # 调用辅助函数
        helper_context = pm.create_parameter_context("helper_call")
        pm.enter_context(helper_context)
        
        pm.bind_parameters("helper_function", {"input_data": data})
        helper_input = pm.get_parameter_value("input_data")
        
        # 模拟处理
        processed = [x * 2 for x in helper_input]
        pm.set_return_value(processed, "processed_data")
        
        helper_result = pm.get_return_value("processed_data")
        print(f"辅助函数处理结果: {helper_result}")
        
        # 退出辅助函数上下文
        pm.exit_context()
        
        # 在主函数中使用辅助函数结果
        pm.set_return_value({"processed": helper_result, "status": "success"}, "result")
        main_result = pm.get_return_value("result")
        print(f"主函数最终结果: {main_result}")
        
        # 退出主函数上下文
        pm.exit_context()
        
        # 测试变量快照和恢复
        print("\n2. 测试快照和恢复")
        
        # 创建快照
        snapshot = vbs.create_variable_snapshot()
        print(f"创建快照，包含 {len(snapshot)} 个变量")
        
        # 修改全局配置
        vbs.set_variable("global_config", {"debug": False, "max_items": 50})
        modified_config = vbs.get_variable("global_config").get_value()
        print(f"修改后的配置: {modified_config}")
        
        # 恢复快照
        vbs.restore_variable_snapshot(snapshot)
        restored_config = vbs.get_variable("global_config").get_value()
        print(f"恢复后的配置: {restored_config}")
        
        # 性能测试
        print("\n3. 性能测试")
        
        start_time = time.time()
        
        # 大量变量操作
        for i in range(1000):
            vbs.define_variable(f"perf_var_{i}", i)
        
        for i in range(1000):
            var = vbs.get_variable(f"perf_var_{i}")
            if var:
                var.set_value(i * 2)
        
        end_time = time.time()
        print(f"1000次变量操作耗时: {end_time - start_time:.4f}秒")
        
        # 获取最终统计
        vbs_stats = vbs.get_statistics()
        pm_stats = pm.get_statistics()
        
        print(f"\n4. 最终统计信息:")
        print("变量绑定系统:")
        for key, value in vbs_stats.items():
            if not isinstance(value, dict):
                print(f"  {key}: {value}")
        
        print("参数管理器:")
        for key, value in pm_stats.items():
            if not isinstance(value, dict):
                print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        logger.error(f"集成测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("开始变量绑定与参数传递系统测试...")
    
    tests = [
        ("变量绑定系统", test_variable_binding_system),
        ("参数管理器", test_parameter_manager),
        ("集成功能", test_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            print(f"运行测试: {test_name}")
            print(f"{'='*60}")
            
            start_time = time.time()
            result = test_func()
            execution_time = time.time() - start_time
            
            results.append(result)
            status = "✓ 通过" if result else "✗ 失败"
            print(f"\n{test_name}: {status} (耗时: {execution_time:.4f}s)")
            
        except Exception as e:
            logger.error(f"测试 {test_name} 出现异常: {e}")
            results.append(False)
            print(f"\n{test_name}: ✗ 异常 (错误: {str(e)})")
    
    # 总结
    successful_tests = sum(results)
    total_tests = len(results)
    success_rate = successful_tests / total_tests * 100
    
    print(f"\n{'='*60}")
    print(f"测试总结")
    print(f"{'='*60}")
    print(f"成功测试: {successful_tests}/{total_tests}")
    print(f"成功率: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("🎉 测试整体通过！")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    return successful_tests == total_tests


if __name__ == "__main__":
    asyncio.run(main())
