# 可执行图式系统 (Executable Schema System)

## 概述

可执行图式系统是LIDA认知架构中的核心组件，实现了基于图式的知识表示、推理和执行机制。该系统将传统的静态知识表示转换为可执行的动态图式，支持复杂的认知任务处理。

## 系统架构

### 核心组件

1. **图式表示层 (Schema Representation Layer)**
   - `TreeChart.py` - 图式树结构定义
   - `SchemaFactory.py` - 图式工厂，负责创建各种类型的图式
   - `SchemaType.py` - 图式类型定义

2. **执行引擎层 (Execution Engine Layer)**
   - `SchemaExecutor.py` - 图式执行器，核心执行引擎
   - `ExecutionContext.py` - 执行上下文管理
   - `ExecutionResult.py` - 执行结果封装

3. **变量与参数管理层 (Variable & Parameter Management Layer)**
   - `VariableBindingSystem.py` - 变量绑定系统
   - `ParameterManager.py` - 参数传递管理器

4. **控制流优化层 (Control Flow Optimization Layer)**
   - `OptimizedControlFlowTask.py` - 优化的控制流任务基类
   - `OptimizedDoSuccTask.py` - 优化的顺序执行任务
   - `OptimizedDoSelectTreeTask.py` - 优化的条件选择任务
   - `OptimizedForEachTask.py` - 优化的循环任务

5. **搜索集成层 (Search Integration Layer)**
   - `SchemaSearchIntegration.py` - 图式搜索集成系统
   - 支持多种搜索策略和搜索驱动的图式执行

6. **推理集成层 (Reasoning Integration Layer)**
   - `NarsSchemaIntegration.py` - NARS推理系统集成
   - 实现推理驱动图式执行和执行反馈推理

7. **测试验证层 (Testing & Validation Layer)**
   - `ComprehensiveTestFramework.py` - 综合测试框架
   - `run_comprehensive_tests.py` - 测试运行入口

## 主要特性

### 1. 多类型图式支持
- **动作图式 (Action Schema)**: 执行具体操作
- **条件图式 (Condition Schema)**: 条件分支控制
- **循环图式 (Loop Schema)**: 循环执行控制
- **序列图式 (Sequence Schema)**: 顺序执行控制
- **并行图式 (Parallel Schema)**: 并发执行控制

### 2. 高级执行特性
- **异步执行**: 支持异步和并发执行
- **错误处理**: 完善的错误处理和恢复机制
- **检查点**: 支持执行状态保存和恢复
- **性能监控**: 实时执行性能统计
- **缓存优化**: 智能缓存机制提升性能

### 3. 智能变量管理
- **作用域管理**: 层次化的变量作用域
- **类型系统**: 强类型变量系统
- **自动转换**: 智能类型转换
- **缓存机制**: 变量访问缓存优化

### 4. 灵活参数传递
- **多种传递模式**: 按值、按引用、按名、按需传递
- **参数验证**: 自动参数验证和约束检查
- **上下文管理**: 参数上下文隔离
- **批量绑定**: 支持批量参数绑定

### 5. 搜索驱动执行
- **多策略搜索**: 基于图式、激活引导、语义搜索
- **搜索集成**: 搜索结果驱动图式执行
- **自适应优化**: 基于搜索结果的执行优化

### 6. 推理系统集成
- **NARS集成**: 与NARS推理系统深度集成
- **多种推理**: 演绎、归纳、溯因、类比推理
- **双向集成**: 推理驱动执行和执行反馈推理

## 快速开始

### 1. 基本图式创建和执行

```python
from linars.edu.memphis.ccrg.lida.Nlanguage.SchemaFactory import schema_factory
from linars.edu.memphis.ccrg.lida.Nlanguage.SchemaExecutor import schema_executor, ExecutionContext

# 创建动作图式
action_schema = schema_factory.create_action_schema(
    action_type='hello_world',
    action_params={'message': 'Hello, World!'},
    schema_id='hello_schema'
)

# 创建执行上下文
context = ExecutionContext(context_id="hello_context")

# 执行图式
result = await schema_executor.execute(action_schema, context)
print(f"执行结果: {result.success}")
```

### 2. 条件图式使用

```python
# 创建条件图式
condition_schema = schema_factory.create_condition_schema(
    condition=schema_factory.create_action_schema(
        action_type='check_condition',
        action_params={'condition': 'x > 0'}
    ),
    then_branch=schema_factory.create_action_schema(
        action_type='positive_action',
        action_params={'action': 'execute_positive'}
    ),
    else_branch=schema_factory.create_action_schema(
        action_type='negative_action',
        action_params={'action': 'execute_negative'}
    ),
    schema_id='condition_example'
)

# 执行条件图式
context = ExecutionContext(context_id="condition_context")
context.set_variable('x', 5)
result = await schema_executor.execute(condition_schema, context)
```

### 3. 变量绑定使用

```python
from linars.edu.memphis.ccrg.lida.Nlanguage.VariableBindingSystem import variable_binding_system, VariableType

# 定义变量
vbs = variable_binding_system
var = vbs.define_variable("my_var", "my_value", VariableType.PRIMITIVE)

# 获取变量
retrieved_var = vbs.get_variable("my_var")
print(f"变量值: {retrieved_var.get_value()}")

# 创建作用域
from linars.edu.memphis.ccrg.lida.Nlanguage.VariableBindingSystem import ScopeType
scope = vbs.create_scope("my_scope", ScopeType.FUNCTION)
vbs.enter_scope(scope)

# 在新作用域中定义变量
vbs.define_variable("local_var", "local_value", VariableType.PRIMITIVE)
```

### 4. 参数管理使用

```python
from linars.edu.memphis.ccrg.lida.Nlanguage.ParameterManager import parameter_manager, ParameterDefinition, ParameterDirection, VariableType

# 定义参数
param_def = ParameterDefinition(
    name="input_param",
    parameter_type=VariableType.PRIMITIVE,
    direction=ParameterDirection.INPUT,
    is_required=True
)

# 注册参数定义
parameter_manager.register_parameter_definition("my_function", param_def)

# 创建参数上下文
context = parameter_manager.create_parameter_context("my_context")
parameter_manager.enter_context(context)

# 绑定参数
bindings = parameter_manager.bind_parameters("my_function", {"input_param": "test_value"})

# 获取参数值
value = parameter_manager.get_parameter_value("input_param")
print(f"参数值: {value}")
```

## 运行测试

### 1. 运行所有测试

```bash
python -m linars.edu.memphis.ccrg.lida.Nlanguage.run_comprehensive_tests --mode all
```

### 2. 运行特定类型测试

```bash
# 单元测试
python -m linars.edu.memphis.ccrg.lida.Nlanguage.run_comprehensive_tests --mode unit

# 集成测试
python -m linars.edu.memphis.ccrg.lida.Nlanguage.run_comprehensive_tests --mode integration

# 性能测试
python -m linars.edu.memphis.ccrg.lida.Nlanguage.run_comprehensive_tests --mode performance

# 冒烟测试
python -m linars.edu.memphis.ccrg.lida.Nlanguage.run_comprehensive_tests --mode smoke
```

### 3. 交互模式

```bash
python -m linars.edu.memphis.ccrg.lida.Nlanguage.run_comprehensive_tests --mode interactive
```

### 4. 验证系统要求

```bash
python -m linars.edu.memphis.ccrg.lida.Nlanguage.run_comprehensive_tests --validate
```

## 系统要求

- Python 3.7+
- asyncio (内置)
- logging (内置)
- json (内置)
- uuid (内置)
- threading (内置)
- collections (内置)
- dataclasses (内置)
- typing (内置)
- enum (内置)

### 可选依赖

- psutil (用于内存使用测试)
- statistics (用于统计分析)

## 性能特性

### 1. 执行性能
- 支持异步并发执行
- 智能缓存机制
- 优化的控制流处理
- 内存使用优化

### 2. 扩展性
- 模块化设计
- 插件式架构
- 可配置的执行策略
- 支持大规模图式执行

### 3. 可靠性
- 完善的错误处理
- 自动重试机制
- 检查点和恢复
- 全面的测试覆盖

## 扩展开发

### 1. 自定义图式类型

```python
from linars.edu.memphis.ccrg.lida.Nlanguage.TreeChart import TreeChart, SchemaType

class CustomSchema(TreeChart):
    def __init__(self, custom_params):
        super().__init__(SchemaType.CUSTOM, "custom_schema")
        self.custom_params = custom_params
```

### 2. 自定义执行器

```python
from linars.edu.memphis.ccrg.lida.Nlanguage.SchemaExecutor import ActionHandler

class CustomActionHandler(ActionHandler):
    async def execute(self, action_params, context):
        # 自定义执行逻辑
        return {"result": "custom_execution"}
    
    def validate_params(self, action_params):
        # 参数验证逻辑
        return True

# 注册自定义处理器
schema_executor.register_action_handler('custom_action', CustomActionHandler())
```

### 3. 自定义搜索策略

```python
from linars.edu.memphis.ccrg.lida.Nlanguage.SchemaSearchIntegration import SearchStrategy

class CustomSearchStrategy(SearchStrategy):
    async def search(self, request):
        # 自定义搜索逻辑
        return search_result
    
    def get_strategy_name(self):
        return "custom_search"
    
    def calculate_relevance(self, request):
        return 0.8

# 注册自定义策略
schema_search_integrator.register_strategy(CustomSearchStrategy())
```

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 运行测试确保通过
5. 提交 Pull Request

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 项目仓库: [GitHub链接]
- 邮箱: [联系邮箱]
- 文档: [文档链接]

---

*可执行图式系统 - 让认知架构更智能、更高效*
