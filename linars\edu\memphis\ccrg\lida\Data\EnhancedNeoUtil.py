"""
增强版Neo4j集成优化模块
完善Neo4j集成，实现高效的图谱查询、缓存机制和批量处理功能
"""

import logging
import threading
import time
import hashlib
from typing import Dict, List, Set, Optional, Any, Tuple, Union
from collections import defaultdict, OrderedDict
from concurrent.futures import ThreadPoolExecutor, as_completed
import json

from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName

# 尝试导入Neo4j相关模块
try:
    from py2neo import Graph, Node as Neo4jNode, Relationship as Neo4jRelationship
    NEO4J_AVAILABLE = True
except ImportError:
    NEO4J_AVAILABLE = False


class CacheEntry:
    """缓存条目"""
    
    def __init__(self, data, ttl_seconds=3600):
        self.data = data
        self.created_time = time.time()
        self.ttl_seconds = ttl_seconds
        self.access_count = 0
        self.last_access = time.time()
    
    def is_expired(self):
        """检查是否过期"""
        return time.time() - self.created_time > self.ttl_seconds
    
    def access(self):
        """记录访问"""
        self.access_count += 1
        self.last_access = time.time()
        return self.data


class QueryOptimizer:
    """查询优化器"""
    
    def __init__(self):
        self.query_stats = defaultdict(lambda: {'count': 0, 'total_time': 0.0, 'avg_time': 0.0})
        self.optimization_rules = []
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def optimize_query(self, query: str, parameters: dict = None):
        """
        优化查询
        
        Args:
            query: 原始查询
            parameters: 查询参数
            
        Returns:
            tuple: (优化后的查询, 优化后的参数)
        """
        try:
            optimized_query = query
            optimized_params = parameters or {}
            
            # 应用优化规则
            for rule in self.optimization_rules:
                optimized_query, optimized_params = rule(optimized_query, optimized_params)
            
            # 添加查询提示
            optimized_query = self.add_query_hints(optimized_query)
            
            return optimized_query, optimized_params
            
        except Exception as e:
            self.logger.error(f"Error optimizing query: {e}")
            return query, parameters or {}
    
    def add_query_hints(self, query: str):
        """添加查询提示"""
        try:
            # 为常见模式添加索引提示
            if "MATCH (n)" in query and "WHERE n.name" in query:
                # 建议使用索引
                query = query.replace("MATCH (n)", "MATCH (n) USING INDEX n:Node(name)")
            
            # 限制结果数量
            if "RETURN" in query and "LIMIT" not in query:
                query += " LIMIT 1000"
            
            return query
            
        except Exception as e:
            self.logger.error(f"Error adding query hints: {e}")
            return query
    
    def record_query_stats(self, query: str, execution_time: float):
        """记录查询统计"""
        try:
            stats = self.query_stats[query]
            stats['count'] += 1
            stats['total_time'] += execution_time
            stats['avg_time'] = stats['total_time'] / stats['count']
            
        except Exception as e:
            self.logger.error(f"Error recording query stats: {e}")


class BatchProcessor:
    """批量处理器"""
    
    def __init__(self, batch_size=100, max_workers=4):
        self.batch_size = batch_size
        self.max_workers = max_workers
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def process_batch_queries(self, queries: List[Tuple[str, dict]], graph):
        """
        批量处理查询
        
        Args:
            queries: 查询列表，每个元素为(query, parameters)
            graph: Neo4j图实例
            
        Returns:
            list: 查询结果列表
        """
        try:
            results = []
            
            # 分批处理
            for i in range(0, len(queries), self.batch_size):
                batch = queries[i:i + self.batch_size]
                batch_results = self.process_single_batch(batch, graph)
                results.extend(batch_results)
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error in batch processing: {e}")
            return []
    
    def process_single_batch(self, batch: List[Tuple[str, dict]], graph):
        """处理单个批次"""
        try:
            results = []
            
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交所有查询
                future_to_query = {
                    executor.submit(self.execute_single_query, query, params, graph): (query, params)
                    for query, params in batch
                }
                
                # 收集结果
                for future in as_completed(future_to_query):
                    query, params = future_to_query[future]
                    try:
                        result = future.result()
                        results.append(result)
                    except Exception as e:
                        self.logger.error(f"Error executing query {query}: {e}")
                        results.append(None)
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error processing single batch: {e}")
            return []
    
    def execute_single_query(self, query: str, parameters: dict, graph):
        """执行单个查询"""
        try:
            return graph.run(query, parameters).data()
        except Exception as e:
            self.logger.error(f"Error executing single query: {e}")
            return None


class EnhancedNeoUtil:
    """
    增强版Neo4j工具类
    实现高效的图谱查询、缓存机制和批量处理功能
    """

    def __init__(self, neo4j_uri="bolt://localhost:7687", username="neo4j", password="password"):
        """
        初始化增强版Neo4j工具
        
        Args:
            neo4j_uri: Neo4j连接URI
            username: 用户名
            password: 密码
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Neo4j连接
        self.graph = None
        self.neo4j_uri = neo4j_uri
        self.username = username
        self.password = password
        
        # 缓存系统
        self.query_cache = OrderedDict()
        self.cache_lock = threading.RLock()
        self.max_cache_size = 10000
        self.default_ttl = 3600  # 1小时
        
        # 查询优化器
        self.query_optimizer = QueryOptimizer()
        
        # 批量处理器
        self.batch_processor = BatchProcessor()
        
        # 连接池和性能参数
        self.connection_pool_size = 10
        self.query_timeout = 30
        self.retry_attempts = 3
        
        # 统计信息
        self.stats = {
            'total_queries': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'failed_queries': 0,
            'total_query_time': 0.0,
            'batch_operations': 0
        }
        
        # 初始化连接
        self.initialize_connection()

    def initialize_connection(self):
        """初始化Neo4j连接"""
        try:
            if NEO4J_AVAILABLE:
                self.graph = Graph(self.neo4j_uri, auth=(self.username, self.password))
                self.logger.info("Neo4j connection initialized successfully")
            else:
                self.logger.warning("Neo4j not available, using mock implementation")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize Neo4j connection: {e}")
            self.graph = None

    def execute_query(self, query: str, parameters: dict = None, use_cache: bool = True, ttl: int = None):
        """
        执行查询（带缓存）
        
        Args:
            query: Cypher查询
            parameters: 查询参数
            use_cache: 是否使用缓存
            ttl: 缓存生存时间（秒）
            
        Returns:
            查询结果
        """
        try:
            start_time = time.time()
            
            # 生成缓存键
            cache_key = self.generate_cache_key(query, parameters)
            
            # 检查缓存
            if use_cache:
                cached_result = self.get_from_cache(cache_key)
                if cached_result is not None:
                    self.stats['cache_hits'] += 1
                    return cached_result
                else:
                    self.stats['cache_misses'] += 1
            
            # 优化查询
            optimized_query, optimized_params = self.query_optimizer.optimize_query(query, parameters)
            
            # 执行查询
            result = self.execute_query_with_retry(optimized_query, optimized_params)
            
            # 缓存结果
            if use_cache and result is not None:
                self.put_to_cache(cache_key, result, ttl or self.default_ttl)
            
            # 记录统计
            execution_time = time.time() - start_time
            self.stats['total_queries'] += 1
            self.stats['total_query_time'] += execution_time
            self.query_optimizer.record_query_stats(query, execution_time)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error executing query: {e}")
            self.stats['failed_queries'] += 1
            return None

    def execute_query_with_retry(self, query: str, parameters: dict = None):
        """
        带重试的查询执行
        
        Args:
            query: Cypher查询
            parameters: 查询参数
            
        Returns:
            查询结果
        """
        last_exception = None
        
        for attempt in range(self.retry_attempts):
            try:
                if self.graph is None:
                    self.initialize_connection()
                
                if self.graph:
                    result = self.graph.run(query, parameters or {})
                    return result.data()
                else:
                    return self.mock_query_execution(query, parameters)
                    
            except Exception as e:
                last_exception = e
                self.logger.warning(f"Query attempt {attempt + 1} failed: {e}")
                
                if attempt < self.retry_attempts - 1:
                    time.sleep(0.5 * (attempt + 1))  # 指数退避
        
        raise last_exception or Exception("All query attempts failed")

    def mock_query_execution(self, query: str, parameters: dict = None):
        """模拟查询执行（当Neo4j不可用时）"""
        self.logger.warning("Using mock query execution")
        return []

    def generate_cache_key(self, query: str, parameters: dict = None):
        """
        生成缓存键

        Args:
            query: 查询字符串
            parameters: 查询参数

        Returns:
            str: 缓存键
        """
        try:
            # 标准化查询（移除多余空格）
            normalized_query = ' '.join(query.split())

            # 创建包含查询和参数的字符串
            cache_data = {
                'query': normalized_query,
                'parameters': parameters or {}
            }

            # 生成MD5哈希
            cache_string = json.dumps(cache_data, sort_keys=True)
            return hashlib.md5(cache_string.encode()).hexdigest()

        except Exception as e:
            self.logger.error(f"Error generating cache key: {e}")
            return str(hash(query))

    def get_from_cache(self, cache_key: str):
        """
        从缓存获取数据

        Args:
            cache_key: 缓存键

        Returns:
            缓存的数据或None
        """
        try:
            with self.cache_lock:
                if cache_key in self.query_cache:
                    entry = self.query_cache[cache_key]

                    if entry.is_expired():
                        # 删除过期条目
                        del self.query_cache[cache_key]
                        return None

                    # 移动到末尾（LRU）
                    self.query_cache.move_to_end(cache_key)
                    return entry.access()

                return None

        except Exception as e:
            self.logger.error(f"Error getting from cache: {e}")
            return None

    def put_to_cache(self, cache_key: str, data, ttl: int = None):
        """
        将数据放入缓存

        Args:
            cache_key: 缓存键
            data: 要缓存的数据
            ttl: 生存时间（秒）
        """
        try:
            with self.cache_lock:
                # 检查缓存大小限制
                if len(self.query_cache) >= self.max_cache_size:
                    # 删除最旧的条目
                    oldest_key = next(iter(self.query_cache))
                    del self.query_cache[oldest_key]

                # 添加新条目
                entry = CacheEntry(data, ttl or self.default_ttl)
                self.query_cache[cache_key] = entry

        except Exception as e:
            self.logger.error(f"Error putting to cache: {e}")

    def clear_cache(self):
        """清空缓存"""
        try:
            with self.cache_lock:
                self.query_cache.clear()
            self.logger.info("Query cache cleared")

        except Exception as e:
            self.logger.error(f"Error clearing cache: {e}")

    def cleanup_expired_cache(self):
        """清理过期的缓存条目"""
        try:
            expired_keys = []

            with self.cache_lock:
                for key, entry in self.query_cache.items():
                    if entry.is_expired():
                        expired_keys.append(key)

                for key in expired_keys:
                    del self.query_cache[key]

            if expired_keys:
                self.logger.info(f"Cleaned up {len(expired_keys)} expired cache entries")

        except Exception as e:
            self.logger.error(f"Error cleaning up expired cache: {e}")

    def batch_execute_queries(self, queries: List[Tuple[str, dict]], use_cache: bool = True):
        """
        批量执行查询

        Args:
            queries: 查询列表，每个元素为(query, parameters)
            use_cache: 是否使用缓存

        Returns:
            list: 查询结果列表
        """
        try:
            self.stats['batch_operations'] += 1

            if use_cache:
                # 检查缓存，分离已缓存和未缓存的查询
                cached_results = {}
                uncached_queries = []

                for i, (query, params) in enumerate(queries):
                    cache_key = self.generate_cache_key(query, params)
                    cached_result = self.get_from_cache(cache_key)

                    if cached_result is not None:
                        cached_results[i] = cached_result
                        self.stats['cache_hits'] += 1
                    else:
                        uncached_queries.append((i, query, params))
                        self.stats['cache_misses'] += 1

                # 批量执行未缓存的查询
                if uncached_queries:
                    batch_queries = [(query, params) for _, query, params in uncached_queries]
                    batch_results = self.batch_processor.process_batch_queries(batch_queries, self.graph)

                    # 缓存结果
                    for (original_index, query, params), result in zip(uncached_queries, batch_results):
                        if result is not None:
                            cache_key = self.generate_cache_key(query, params)
                            self.put_to_cache(cache_key, result)
                        cached_results[original_index] = result

                # 按原始顺序组织结果
                final_results = []
                for i in range(len(queries)):
                    final_results.append(cached_results.get(i))

                return final_results
            else:
                # 不使用缓存，直接批量执行
                return self.batch_processor.process_batch_queries(queries, self.graph)

        except Exception as e:
            self.logger.error(f"Error in batch query execution: {e}")
            return [None] * len(queries)

    def get_nodes_by_names(self, node_names: List[str], use_cache: bool = True):
        """
        根据名称批量获取节点

        Args:
            node_names: 节点名称列表
            use_cache: 是否使用缓存

        Returns:
            dict: 节点名称到节点对象的映射
        """
        try:
            if not node_names:
                return {}

            # 构建批量查询
            queries = []
            for name in node_names:
                query = "MATCH (n {name: $name}) RETURN n"
                params = {"name": name}
                queries.append((query, params))

            # 批量执行
            results = self.batch_execute_queries(queries, use_cache)

            # 组织结果
            node_map = {}
            for name, result in zip(node_names, results):
                if result and len(result) > 0:
                    node_data = result[0].get('n')
                    if node_data:
                        node_map[name] = self.convert_neo4j_node_to_lida(node_data)

            return node_map

        except Exception as e:
            self.logger.error(f"Error getting nodes by names: {e}")
            return {}

    def get_relationships_by_nodes(self, node_names: List[str], relationship_types: List[str] = None, use_cache: bool = True):
        """
        根据节点获取关系

        Args:
            node_names: 节点名称列表
            relationship_types: 关系类型列表（可选）
            use_cache: 是否使用缓存

        Returns:
            list: 关系列表
        """
        try:
            if not node_names:
                return []

            # 构建查询
            if relationship_types:
                type_filter = "|".join(relationship_types)
                query = f"MATCH (n)-[r:{type_filter}]-(m) WHERE n.name IN $names RETURN r, n, m"
            else:
                query = "MATCH (n)-[r]-(m) WHERE n.name IN $names RETURN r, n, m"

            params = {"names": node_names}

            # 执行查询
            result = self.execute_query(query, params, use_cache)

            # 转换结果
            relationships = []
            if result:
                for record in result:
                    rel_data = record.get('r')
                    source_data = record.get('n')
                    target_data = record.get('m')

                    if rel_data and source_data and target_data:
                        lida_link = self.convert_neo4j_relationship_to_lida(rel_data, source_data, target_data)
                        if lida_link:
                            relationships.append(lida_link)

            return relationships

        except Exception as e:
            self.logger.error(f"Error getting relationships by nodes: {e}")
            return []

    def convert_neo4j_node_to_lida(self, neo4j_node):
        """
        将Neo4j节点转换为LIDA节点

        Args:
            neo4j_node: Neo4j节点

        Returns:
            Node: LIDA节点
        """
        try:
            from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeImpl import NodeImpl

            lida_node = NodeImpl()

            # 设置节点名称
            if hasattr(neo4j_node, 'get') and 'name' in neo4j_node:
                lida_node.set_node_name(neo4j_node['name'])
            elif hasattr(neo4j_node, 'name'):
                lida_node.set_node_name(neo4j_node.name)

            # 设置其他属性
            if hasattr(neo4j_node, 'items'):
                for key, value in neo4j_node.items():
                    if key != 'name':
                        lida_node.set_property(key, value)

            return lida_node

        except Exception as e:
            self.logger.error(f"Error converting Neo4j node to LIDA: {e}")
            return None

    def convert_neo4j_relationship_to_lida(self, neo4j_rel, source_node, target_node):
        """
        将Neo4j关系转换为LIDA链接

        Args:
            neo4j_rel: Neo4j关系
            source_node: 源节点
            target_node: 目标节点

        Returns:
            Link: LIDA链接
        """
        try:
            from linars.edu.memphis.ccrg.lida.Framework.Shared.LinkImpl import LinkImpl
            from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeImpl import NodeImpl

            # 转换源节点和目标节点
            lida_source = self.convert_neo4j_node_to_lida(source_node)
            lida_target = self.convert_neo4j_node_to_lida(target_node)

            if not lida_source or not lida_target:
                return None

            # 创建链接
            lida_link = LinkImpl()
            lida_link.setSource(lida_source)
            lida_link.setSink(lida_target)

            # 设置链接类型
            if hasattr(neo4j_rel, 'type'):
                category_node = NodeImpl()
                category_node.set_node_name(neo4j_rel.type)
                lida_link.setCategory(category_node)

            # 设置其他属性
            if hasattr(neo4j_rel, 'items'):
                for key, value in neo4j_rel.items():
                    lida_link.set_property(key, value)

            return lida_link

        except Exception as e:
            self.logger.error(f"Error converting Neo4j relationship to LIDA: {e}")
            return None

    def find_shortest_paths(self, start_node_name: str, end_node_name: str, max_depth: int = 6, use_cache: bool = True):
        """
        查找最短路径

        Args:
            start_node_name: 起始节点名称
            end_node_name: 结束节点名称
            max_depth: 最大深度
            use_cache: 是否使用缓存

        Returns:
            list: 路径列表
        """
        try:
            query = """
            MATCH (start {name: $start_name}), (end {name: $end_name})
            MATCH path = shortestPath((start)-[*1..$max_depth]-(end))
            RETURN path
            """

            params = {
                "start_name": start_node_name,
                "end_name": end_node_name,
                "max_depth": max_depth
            }

            result = self.execute_query(query, params, use_cache)

            paths = []
            if result:
                for record in result:
                    path_data = record.get('path')
                    if path_data:
                        path = self.convert_neo4j_path_to_lida(path_data)
                        if path:
                            paths.append(path)

            return paths

        except Exception as e:
            self.logger.error(f"Error finding shortest paths: {e}")
            return []

    def convert_neo4j_path_to_lida(self, neo4j_path):
        """
        将Neo4j路径转换为LIDA路径

        Args:
            neo4j_path: Neo4j路径

        Returns:
            dict: LIDA路径表示
        """
        try:
            path = {
                'nodes': [],
                'relationships': [],
                'length': 0
            }

            # 提取节点和关系
            if hasattr(neo4j_path, 'nodes'):
                for node in neo4j_path.nodes:
                    lida_node = self.convert_neo4j_node_to_lida(node)
                    if lida_node:
                        path['nodes'].append(lida_node)

            if hasattr(neo4j_path, 'relationships'):
                for rel in neo4j_path.relationships:
                    # 这里需要源节点和目标节点信息
                    # 简化处理，只记录关系类型
                    rel_info = {
                        'type': rel.type if hasattr(rel, 'type') else 'unknown',
                        'properties': dict(rel.items()) if hasattr(rel, 'items') else {}
                    }
                    path['relationships'].append(rel_info)

            path['length'] = len(path['relationships'])

            return path

        except Exception as e:
            self.logger.error(f"Error converting Neo4j path to LIDA: {e}")
            return None

    def semantic_search(self, search_terms: List[str], similarity_threshold: float = 0.5, use_cache: bool = True):
        """
        语义搜索

        Args:
            search_terms: 搜索词列表
            similarity_threshold: 相似度阈值
            use_cache: 是否使用缓存

        Returns:
            list: 搜索结果
        """
        try:
            results = []

            for term in search_terms:
                # 精确匹配
                exact_query = "MATCH (n {name: $term}) RETURN n"
                exact_result = self.execute_query(exact_query, {"term": term}, use_cache)

                if exact_result:
                    for record in exact_result:
                        node_data = record.get('n')
                        if node_data:
                            lida_node = self.convert_neo4j_node_to_lida(node_data)
                            if lida_node:
                                results.append({
                                    'node': lida_node,
                                    'similarity': 1.0,
                                    'match_type': 'exact'
                                })

                # 模糊匹配
                fuzzy_query = "MATCH (n) WHERE n.name CONTAINS $term RETURN n"
                fuzzy_result = self.execute_query(fuzzy_query, {"term": term}, use_cache)

                if fuzzy_result:
                    for record in fuzzy_result:
                        node_data = record.get('n')
                        if node_data:
                            node_name = node_data.get('name', '')
                            similarity = self.calculate_string_similarity(term, node_name)

                            if similarity >= similarity_threshold:
                                lida_node = self.convert_neo4j_node_to_lida(node_data)
                                if lida_node:
                                    results.append({
                                        'node': lida_node,
                                        'similarity': similarity,
                                        'match_type': 'fuzzy'
                                    })

            # 去重和排序
            unique_results = self.deduplicate_search_results(results)
            unique_results.sort(key=lambda x: x['similarity'], reverse=True)

            return unique_results

        except Exception as e:
            self.logger.error(f"Error in semantic search: {e}")
            return []

    def calculate_string_similarity(self, str1: str, str2: str):
        """
        计算字符串相似度

        Args:
            str1: 字符串1
            str2: 字符串2

        Returns:
            float: 相似度（0-1）
        """
        try:
            if not str1 or not str2:
                return 0.0

            # 简单的编辑距离相似度
            max_len = max(len(str1), len(str2))
            if max_len == 0:
                return 1.0

            # 计算编辑距离
            edit_distance = self.levenshtein_distance(str1.lower(), str2.lower())
            similarity = 1.0 - (edit_distance / max_len)

            return max(0.0, similarity)

        except Exception as e:
            self.logger.error(f"Error calculating string similarity: {e}")
            return 0.0

    def levenshtein_distance(self, s1: str, s2: str):
        """计算编辑距离"""
        if len(s1) < len(s2):
            return self.levenshtein_distance(s2, s1)

        if len(s2) == 0:
            return len(s1)

        previous_row = list(range(len(s2) + 1))
        for i, c1 in enumerate(s1):
            current_row = [i + 1]
            for j, c2 in enumerate(s2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row

        return previous_row[-1]

    def deduplicate_search_results(self, results: List[dict]):
        """去重搜索结果"""
        try:
            seen_nodes = set()
            unique_results = []

            for result in results:
                node = result['node']
                node_name = node.get_node_name() if hasattr(node, 'get_node_name') else str(node)

                if node_name not in seen_nodes:
                    seen_nodes.add(node_name)
                    unique_results.append(result)

            return unique_results

        except Exception as e:
            self.logger.error(f"Error deduplicating search results: {e}")
            return results

    def get_statistics(self):
        """
        获取统计信息

        Returns:
            dict: 统计信息
        """
        try:
            stats = self.stats.copy()

            # 添加缓存统计
            with self.cache_lock:
                stats['cache_size'] = len(self.query_cache)
                stats['cache_hit_rate'] = (
                    self.stats['cache_hits'] / (self.stats['cache_hits'] + self.stats['cache_misses'])
                    if (self.stats['cache_hits'] + self.stats['cache_misses']) > 0 else 0.0
                )

            # 添加查询性能统计
            if self.stats['total_queries'] > 0:
                stats['avg_query_time'] = self.stats['total_query_time'] / self.stats['total_queries']
            else:
                stats['avg_query_time'] = 0.0

            # 添加查询优化器统计
            stats['query_optimizer_stats'] = dict(self.query_optimizer.query_stats)

            return stats

        except Exception as e:
            self.logger.error(f"Error getting statistics: {e}")
            return self.stats.copy()

    def reset_statistics(self):
        """重置统计信息"""
        try:
            self.stats = {
                'total_queries': 0,
                'cache_hits': 0,
                'cache_misses': 0,
                'failed_queries': 0,
                'total_query_time': 0.0,
                'batch_operations': 0
            }

            self.query_optimizer.query_stats.clear()
            self.logger.info("Statistics reset")

        except Exception as e:
            self.logger.error(f"Error resetting statistics: {e}")

    def optimize_database(self):
        """
        优化数据库性能
        """
        try:
            if not self.graph:
                self.logger.warning("No database connection for optimization")
                return

            # 创建常用索引
            index_queries = [
                "CREATE INDEX node_name_index IF NOT EXISTS FOR (n:Node) ON (n.name)",
                "CREATE INDEX concept_name_index IF NOT EXISTS FOR (n:Concept) ON (n.name)",
                "CREATE INDEX grammar_name_index IF NOT EXISTS FOR (n:Grammar) ON (n.name)"
            ]

            for query in index_queries:
                try:
                    self.execute_query(query, use_cache=False)
                    self.logger.info(f"Created index: {query}")
                except Exception as e:
                    self.logger.warning(f"Failed to create index: {e}")

            # 清理过期缓存
            self.cleanup_expired_cache()

            self.logger.info("Database optimization completed")

        except Exception as e:
            self.logger.error(f"Error optimizing database: {e}")

    def health_check(self):
        """
        健康检查

        Returns:
            dict: 健康状态信息
        """
        try:
            health_status = {
                'database_connected': False,
                'cache_healthy': False,
                'query_performance': 'unknown',
                'last_check': time.time()
            }

            # 检查数据库连接
            try:
                if self.graph:
                    test_result = self.execute_query("RETURN 1 as test", use_cache=False)
                    health_status['database_connected'] = test_result is not None
                else:
                    health_status['database_connected'] = False
            except:
                health_status['database_connected'] = False

            # 检查缓存健康状态
            try:
                with self.cache_lock:
                    cache_size = len(self.query_cache)
                    health_status['cache_healthy'] = cache_size < self.max_cache_size * 0.9
                    health_status['cache_size'] = cache_size
            except:
                health_status['cache_healthy'] = False

            # 检查查询性能
            try:
                if self.stats['total_queries'] > 0:
                    avg_time = self.stats['total_query_time'] / self.stats['total_queries']
                    if avg_time < 1.0:
                        health_status['query_performance'] = 'good'
                    elif avg_time < 5.0:
                        health_status['query_performance'] = 'fair'
                    else:
                        health_status['query_performance'] = 'poor'
                    health_status['avg_query_time'] = avg_time
            except:
                health_status['query_performance'] = 'unknown'

            return health_status

        except Exception as e:
            self.logger.error(f"Error in health check: {e}")
            return {'error': str(e), 'last_check': time.time()}

    def maintenance(self):
        """
        执行维护任务
        """
        try:
            self.logger.info("Starting maintenance tasks")

            # 清理过期缓存
            self.cleanup_expired_cache()

            # 优化数据库
            self.optimize_database()

            # 重置旧的统计信息（保留最近的）
            if self.stats['total_queries'] > 100000:
                # 保留最近的统计信息，重置计数器
                recent_stats = {
                    'total_queries': self.stats['total_queries'] // 2,
                    'cache_hits': self.stats['cache_hits'] // 2,
                    'cache_misses': self.stats['cache_misses'] // 2,
                    'failed_queries': self.stats['failed_queries'] // 2,
                    'total_query_time': self.stats['total_query_time'] / 2,
                    'batch_operations': self.stats['batch_operations'] // 2
                }
                self.stats.update(recent_stats)

            self.logger.info("Maintenance tasks completed")

        except Exception as e:
            self.logger.error(f"Error during maintenance: {e}")

    def close(self):
        """
        关闭连接和清理资源
        """
        try:
            # 清空缓存
            self.clear_cache()

            # 关闭数据库连接
            if self.graph:
                # py2neo的Graph对象通常不需要显式关闭
                self.graph = None

            self.logger.info("EnhancedNeoUtil closed successfully")

        except Exception as e:
            self.logger.error(f"Error closing EnhancedNeoUtil: {e}")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()


# 全局实例（单例模式）
_enhanced_neo_util_instance = None
_instance_lock = threading.Lock()


def get_enhanced_neo_util(neo4j_uri="bolt://localhost:7687", username="neo4j", password="password"):
    """
    获取EnhancedNeoUtil的全局实例（单例模式）

    Args:
        neo4j_uri: Neo4j连接URI
        username: 用户名
        password: 密码

    Returns:
        EnhancedNeoUtil: 增强版Neo4j工具实例
    """
    global _enhanced_neo_util_instance

    with _instance_lock:
        if _enhanced_neo_util_instance is None:
            _enhanced_neo_util_instance = EnhancedNeoUtil(neo4j_uri, username, password)

        return _enhanced_neo_util_instance


def reset_enhanced_neo_util():
    """重置全局实例"""
    global _enhanced_neo_util_instance

    with _instance_lock:
        if _enhanced_neo_util_instance:
            _enhanced_neo_util_instance.close()
            _enhanced_neo_util_instance = None
