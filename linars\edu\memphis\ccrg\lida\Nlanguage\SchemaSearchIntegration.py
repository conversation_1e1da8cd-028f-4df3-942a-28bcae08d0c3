"""
图式搜索集成系统 - 将可执行图式与搜索机制深度集成
"""

import asyncio
import logging
import time
import uuid
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Callable, Union, Set, Tuple
from enum import Enum
from collections import defaultdict, deque
import threading

from .TreeChart import TreeChart, SchemaType, ExecutionMode, ChartStatus
from .SchemaExecutor import schema_executor, ExecutionContext, ExecutionResult
from .SchemaFactory import schema_factory
from .VariableBindingSystem import variable_binding_system, VariableType, ScopeType


class SearchTriggerType(Enum):
    """搜索触发类型"""
    SCHEMA_EXECUTION = "schema_execution"     # 图式执行触发
    VARIABLE_BINDING = "variable_binding"     # 变量绑定触发
    CONDITION_EVALUATION = "condition_evaluation"  # 条件评估触发
    LOOP_ITERATION = "loop_iteration"         # 循环迭代触发
    ERROR_RECOVERY = "error_recovery"         # 错误恢复触发
    ADAPTIVE_OPTIMIZATION = "adaptive_optimization"  # 自适应优化触发


class SearchScope(Enum):
    """搜索范围"""
    LOCAL = "local"           # 本地搜索
    GLOBAL = "global"         # 全局搜索
    CONTEXTUAL = "contextual" # 上下文搜索
    SEMANTIC = "semantic"     # 语义搜索
    STRUCTURAL = "structural" # 结构搜索


@dataclass
class SearchRequest:
    """搜索请求"""
    request_id: str
    trigger_type: SearchTriggerType
    search_scope: SearchScope
    query_terms: List[str]
    context_data: Dict[str, Any] = field(default_factory=dict)
    constraints: Dict[str, Any] = field(default_factory=dict)
    priority: float = 0.5
    timeout: float = 5.0
    max_results: int = 50
    created_time: float = field(default_factory=time.time)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'request_id': self.request_id,
            'trigger_type': self.trigger_type.value,
            'search_scope': self.search_scope.value,
            'query_terms': self.query_terms,
            'context_data': self.context_data,
            'constraints': self.constraints,
            'priority': self.priority,
            'timeout': self.timeout,
            'max_results': self.max_results,
            'created_time': self.created_time
        }


@dataclass
class SearchResult:
    """搜索结果"""
    result_id: str
    request_id: str
    results: List[Any]
    confidence: float
    execution_time: float
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def merge(self, other: 'SearchResult') -> 'SearchResult':
        """合并搜索结果"""
        merged_results = self.results + other.results
        merged_confidence = (self.confidence + other.confidence) / 2
        merged_time = self.execution_time + other.execution_time
        
        return SearchResult(
            result_id=str(uuid.uuid4()),
            request_id=self.request_id,
            results=merged_results,
            confidence=merged_confidence,
            execution_time=merged_time,
            metadata={**self.metadata, **other.metadata}
        )


class SearchStrategy(ABC):
    """搜索策略抽象基类"""
    
    @abstractmethod
    async def search(self, request: SearchRequest) -> SearchResult:
        """执行搜索"""
        pass
    
    @abstractmethod
    def get_strategy_name(self) -> str:
        """获取策略名称"""
        pass
    
    @abstractmethod
    def calculate_relevance(self, request: SearchRequest) -> float:
        """计算策略相关性"""
        pass


class SchemaBasedSearchStrategy(SearchStrategy):
    """基于图式的搜索策略"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.schema_cache = {}
    
    async def search(self, request: SearchRequest) -> SearchResult:
        """执行基于图式的搜索"""
        start_time = time.time()
        results = []
        
        try:
            # 根据查询词构建搜索图式
            search_schema = self._build_search_schema(request)
            
            # 创建执行上下文
            context = ExecutionContext(context_id=f"search_{request.request_id}")
            
            # 设置搜索参数
            for key, value in request.context_data.items():
                context.set_variable(key, value)
            
            # 执行搜索图式
            execution_result = await schema_executor.execute(search_schema, context)
            
            if execution_result.success:
                results = execution_result.result if isinstance(execution_result.result, list) else [execution_result.result]
                confidence = 0.8
            else:
                confidence = 0.1
                self.logger.warning(f"Schema-based search failed: {execution_result.error}")
            
        except Exception as e:
            self.logger.error(f"Error in schema-based search: {e}")
            confidence = 0.0
        
        execution_time = time.time() - start_time
        
        return SearchResult(
            result_id=str(uuid.uuid4()),
            request_id=request.request_id,
            results=results,
            confidence=confidence,
            execution_time=execution_time,
            metadata={'strategy': 'schema_based', 'schema_type': 'search'}
        )
    
    def _build_search_schema(self, request: SearchRequest) -> TreeChart:
        """构建搜索图式"""
        # 创建搜索动作序列
        search_actions = []
        
        # 1. 查询预处理
        preprocess_action = schema_factory.create_action_schema(
            action_type='preprocess_query',
            action_params={
                'query_terms': request.query_terms,
                'search_scope': request.search_scope.value,
                'constraints': request.constraints
            }
        )
        search_actions.append(preprocess_action)
        
        # 2. 执行搜索
        for term in request.query_terms:
            search_action = schema_factory.create_action_schema(
                action_type='execute_search',
                action_params={
                    'term': term,
                    'scope': request.search_scope.value,
                    'max_results': request.max_results // len(request.query_terms)
                }
            )
            search_actions.append(search_action)
        
        # 3. 结果后处理
        postprocess_action = schema_factory.create_action_schema(
            action_type='postprocess_results',
            action_params={
                'merge_strategy': 'weighted_union',
                'ranking_criteria': ['relevance', 'activation', 'recency']
            }
        )
        search_actions.append(postprocess_action)
        
        # 创建搜索序列图式
        search_schema = schema_factory.create_sequence_schema(
            steps=search_actions,
            schema_id=f"search_schema_{request.request_id}"
        )
        
        return search_schema
    
    def get_strategy_name(self) -> str:
        return "schema_based"
    
    def calculate_relevance(self, request: SearchRequest) -> float:
        """计算策略相关性"""
        # 基于图式的搜索对结构化查询更有效
        if request.search_scope in [SearchScope.STRUCTURAL, SearchScope.CONTEXTUAL]:
            return 0.9
        elif request.trigger_type == SearchTriggerType.SCHEMA_EXECUTION:
            return 0.8
        else:
            return 0.6


class ActivationGuidedSearchStrategy(SearchStrategy):
    """激活引导搜索策略"""
    
    def __init__(self, pam=None):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.pam = pam
        self.activation_threshold = 0.5
    
    async def search(self, request: SearchRequest) -> SearchResult:
        """执行激活引导搜索"""
        start_time = time.time()
        results = []
        
        try:
            # 获取高激活节点
            high_activation_nodes = self._get_high_activation_nodes()
            
            # 基于激活度进行搜索
            for term in request.query_terms:
                term_results = self._search_by_activation(term, high_activation_nodes, request)
                results.extend(term_results)
            
            # 按激活度排序
            results.sort(key=lambda x: x.get('activation', 0), reverse=True)
            results = results[:request.max_results]
            
            confidence = 0.7 if results else 0.2
            
        except Exception as e:
            self.logger.error(f"Error in activation-guided search: {e}")
            confidence = 0.0
        
        execution_time = time.time() - start_time
        
        return SearchResult(
            result_id=str(uuid.uuid4()),
            request_id=request.request_id,
            results=results,
            confidence=confidence,
            execution_time=execution_time,
            metadata={'strategy': 'activation_guided', 'threshold': self.activation_threshold}
        )
    
    def _get_high_activation_nodes(self) -> List[Any]:
        """获取高激活节点"""
        if not self.pam:
            return []
        
        try:
            # 从PAM获取高激活节点
            # 这里需要根据实际的PAM接口实现
            high_nodes = []
            
            # 模拟获取高激活节点的逻辑
            if hasattr(self.pam, 'get_high_activation_nodes'):
                high_nodes = self.pam.get_high_activation_nodes(self.activation_threshold)
            
            return high_nodes
            
        except Exception as e:
            self.logger.warning(f"Failed to get high activation nodes: {e}")
            return []
    
    def _search_by_activation(self, term: str, high_nodes: List[Any], request: SearchRequest) -> List[Dict[str, Any]]:
        """基于激活度搜索"""
        results = []
        
        for node in high_nodes:
            try:
                # 检查节点是否与搜索词相关
                if self._is_node_relevant(node, term):
                    activation = getattr(node, 'get_activation', lambda: 0.5)()
                    
                    result = {
                        'node': node,
                        'term': term,
                        'activation': activation,
                        'relevance_score': self._calculate_relevance_score(node, term),
                        'search_type': 'activation_guided'
                    }
                    results.append(result)
                    
            except Exception as e:
                self.logger.warning(f"Error processing node in activation search: {e}")
        
        return results
    
    def _is_node_relevant(self, node: Any, term: str) -> bool:
        """检查节点是否与搜索词相关"""
        try:
            node_name = getattr(node, 'get_tn_name', lambda: '')()
            return term.lower() in node_name.lower() if node_name else False
        except:
            return False
    
    def _calculate_relevance_score(self, node: Any, term: str) -> float:
        """计算相关性分数"""
        try:
            node_name = getattr(node, 'get_tn_name', lambda: '')()
            if not node_name:
                return 0.0
            
            # 简单的字符串匹配相关性
            if term.lower() == node_name.lower():
                return 1.0
            elif term.lower() in node_name.lower():
                return 0.8
            else:
                # 可以实现更复杂的语义相似度计算
                return 0.3
                
        except:
            return 0.0
    
    def get_strategy_name(self) -> str:
        return "activation_guided"
    
    def calculate_relevance(self, request: SearchRequest) -> float:
        """计算策略相关性"""
        if request.trigger_type == SearchTriggerType.SCHEMA_EXECUTION:
            return 0.8
        elif request.search_scope == SearchScope.CONTEXTUAL:
            return 0.7
        else:
            return 0.5


class SemanticSearchStrategy(SearchStrategy):
    """语义搜索策略"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.semantic_cache = {}
    
    async def search(self, request: SearchRequest) -> SearchResult:
        """执行语义搜索"""
        start_time = time.time()
        results = []
        
        try:
            # 对每个查询词进行语义搜索
            for term in request.query_terms:
                term_results = await self._semantic_search_term(term, request)
                results.extend(term_results)
            
            # 去重和排序
            results = self._deduplicate_and_rank(results)
            results = results[:request.max_results]
            
            confidence = 0.6 if results else 0.1
            
        except Exception as e:
            self.logger.error(f"Error in semantic search: {e}")
            confidence = 0.0
        
        execution_time = time.time() - start_time
        
        return SearchResult(
            result_id=str(uuid.uuid4()),
            request_id=request.request_id,
            results=results,
            confidence=confidence,
            execution_time=execution_time,
            metadata={'strategy': 'semantic', 'cache_size': len(self.semantic_cache)}
        )
    
    async def _semantic_search_term(self, term: str, request: SearchRequest) -> List[Dict[str, Any]]:
        """对单个词进行语义搜索"""
        # 检查缓存
        cache_key = f"{term}_{request.search_scope.value}"
        if cache_key in self.semantic_cache:
            return self.semantic_cache[cache_key]
        
        results = []
        
        try:
            # 使用Neo4j进行语义搜索
            from linars.edu.memphis.ccrg.lida.Data.neo_util import NeoUtil
            
            # 构建语义查询
            if request.search_scope == SearchScope.SEMANTIC:
                query = f"MATCH (n)-[r:语义相关]->(m) WHERE n.name CONTAINS '{term}' RETURN m, r.weight as weight LIMIT {request.max_results}"
            else:
                query = f"MATCH (n) WHERE n.name CONTAINS '{term}' RETURN n LIMIT {request.max_results}"
            
            # 执行查询
            neo_results = NeoUtil.execute_query(query)
            
            if neo_results:
                while hasattr(neo_results, "has_next") and neo_results.has_next():
                    row = neo_results.next()
                    node = row.get('n') or row.get('m')
                    weight = row.get('weight', 0.5)
                    
                    if node:
                        result = {
                            'node': node,
                            'term': term,
                            'semantic_weight': weight,
                            'search_type': 'semantic'
                        }
                        results.append(result)
            
            # 缓存结果
            self.semantic_cache[cache_key] = results
            
        except Exception as e:
            self.logger.warning(f"Neo4j semantic search failed for term '{term}': {e}")
        
        return results
    
    def _deduplicate_and_rank(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重和排序"""
        # 简单的去重逻辑
        seen_nodes = set()
        unique_results = []
        
        for result in results:
            node_id = id(result.get('node'))
            if node_id not in seen_nodes:
                seen_nodes.add(node_id)
                unique_results.append(result)
        
        # 按语义权重排序
        unique_results.sort(key=lambda x: x.get('semantic_weight', 0), reverse=True)
        
        return unique_results
    
    def get_strategy_name(self) -> str:
        return "semantic"
    
    def calculate_relevance(self, request: SearchRequest) -> float:
        """计算策略相关性"""
        if request.search_scope == SearchScope.SEMANTIC:
            return 0.9
        elif request.trigger_type == SearchTriggerType.CONDITION_EVALUATION:
            return 0.7
        else:
            return 0.4


class SchemaSearchIntegrator:
    """图式搜索集成器"""
    
    def __init__(self, pam=None):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.pam = pam
        
        # 搜索策略
        self.strategies: Dict[str, SearchStrategy] = {}
        self.register_default_strategies()
        
        # 搜索请求队列
        self.request_queue = asyncio.Queue()
        self.result_cache = {}
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_searches': 0,
            'failed_searches': 0,
            'average_response_time': 0.0,
            'cache_hits': 0
        }
        
        # 线程安全
        self.lock = threading.RLock()
        
        # 启动搜索处理器
        self.search_processor_task = None
        self.is_running = False
    
    def register_default_strategies(self):
        """注册默认搜索策略"""
        self.register_strategy(SchemaBasedSearchStrategy())
        self.register_strategy(ActivationGuidedSearchStrategy(self.pam))
        self.register_strategy(SemanticSearchStrategy())
    
    def register_strategy(self, strategy: SearchStrategy):
        """注册搜索策略"""
        self.strategies[strategy.get_strategy_name()] = strategy
        self.logger.info(f"Registered search strategy: {strategy.get_strategy_name()}")
    
    async def start(self):
        """启动搜索集成器"""
        if not self.is_running:
            self.is_running = True
            self.search_processor_task = asyncio.create_task(self._process_search_requests())
            self.logger.info("Schema search integrator started")
    
    async def stop(self):
        """停止搜索集成器"""
        if self.is_running:
            self.is_running = False
            if self.search_processor_task:
                self.search_processor_task.cancel()
                try:
                    await self.search_processor_task
                except asyncio.CancelledError:
                    pass
            self.logger.info("Schema search integrator stopped")
    
    async def search(self, request: SearchRequest) -> SearchResult:
        """执行搜索"""
        with self.lock:
            self.stats['total_requests'] += 1
        
        # 检查缓存
        cache_key = self._generate_cache_key(request)
        if cache_key in self.result_cache:
            with self.lock:
                self.stats['cache_hits'] += 1
            return self.result_cache[cache_key]
        
        try:
            # 选择最佳策略组合
            selected_strategies = self._select_strategies(request)
            
            # 并行执行多个策略
            strategy_tasks = []
            for strategy_name in selected_strategies:
                if strategy_name in self.strategies:
                    strategy = self.strategies[strategy_name]
                    task = asyncio.create_task(strategy.search(request))
                    strategy_tasks.append(task)
            
            # 等待所有策略完成
            strategy_results = await asyncio.gather(*strategy_tasks, return_exceptions=True)
            
            # 合并结果
            merged_result = self._merge_strategy_results(request, strategy_results)
            
            # 缓存结果
            self.result_cache[cache_key] = merged_result
            
            with self.lock:
                self.stats['successful_searches'] += 1
            
            return merged_result
            
        except Exception as e:
            self.logger.error(f"Search failed for request {request.request_id}: {e}")
            
            with self.lock:
                self.stats['failed_searches'] += 1
            
            return SearchResult(
                result_id=str(uuid.uuid4()),
                request_id=request.request_id,
                results=[],
                confidence=0.0,
                execution_time=0.0,
                metadata={'error': str(e)}
            )
    
    def _select_strategies(self, request: SearchRequest) -> List[str]:
        """选择搜索策略"""
        strategy_scores = {}
        
        for name, strategy in self.strategies.items():
            relevance = strategy.calculate_relevance(request)
            strategy_scores[name] = relevance
        
        # 选择相关性最高的策略
        sorted_strategies = sorted(strategy_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 根据请求优先级决定使用多少个策略
        if request.priority > 0.8:
            return [s[0] for s in sorted_strategies[:3]]  # 高优先级使用3个策略
        elif request.priority > 0.5:
            return [s[0] for s in sorted_strategies[:2]]  # 中优先级使用2个策略
        else:
            return [sorted_strategies[0][0]]  # 低优先级使用1个策略
    
    def _merge_strategy_results(self, request: SearchRequest, strategy_results: List[Any]) -> SearchResult:
        """合并策略结果"""
        all_results = []
        total_confidence = 0.0
        total_time = 0.0
        valid_results = 0
        
        for result in strategy_results:
            if isinstance(result, SearchResult):
                all_results.extend(result.results)
                total_confidence += result.confidence
                total_time += result.execution_time
                valid_results += 1
            elif isinstance(result, Exception):
                self.logger.warning(f"Strategy execution failed: {result}")
        
        # 去重和排序
        unique_results = self._deduplicate_results(all_results)
        ranked_results = self._rank_results(unique_results, request)
        
        # 限制结果数量
        final_results = ranked_results[:request.max_results]
        
        # 计算平均置信度
        avg_confidence = total_confidence / max(valid_results, 1)
        
        return SearchResult(
            result_id=str(uuid.uuid4()),
            request_id=request.request_id,
            results=final_results,
            confidence=avg_confidence,
            execution_time=total_time,
            metadata={
                'strategies_used': valid_results,
                'total_candidates': len(all_results),
                'unique_results': len(unique_results)
            }
        )
    
    def _deduplicate_results(self, results: List[Any]) -> List[Any]:
        """去重结果"""
        seen = set()
        unique_results = []
        
        for result in results:
            # 使用结果的唯一标识进行去重
            result_id = self._get_result_id(result)
            if result_id not in seen:
                seen.add(result_id)
                unique_results.append(result)
        
        return unique_results
    
    def _get_result_id(self, result: Any) -> str:
        """获取结果的唯一标识"""
        if isinstance(result, dict):
            node = result.get('node')
            if node and hasattr(node, 'get_node_id'):
                return str(node.get_node_id())
            elif node and hasattr(node, 'get_tn_name'):
                return node.get_tn_name()
        
        return str(id(result))
    
    def _rank_results(self, results: List[Any], request: SearchRequest) -> List[Any]:
        """对结果进行排序"""
        def calculate_score(result):
            score = 0.0
            
            # 激活度权重
            if isinstance(result, dict):
                activation = result.get('activation', 0.0)
                score += activation * 0.4
                
                # 语义权重
                semantic_weight = result.get('semantic_weight', 0.0)
                score += semantic_weight * 0.3
                
                # 相关性分数
                relevance = result.get('relevance_score', 0.0)
                score += relevance * 0.3
            
            return score
        
        return sorted(results, key=calculate_score, reverse=True)
    
    def _generate_cache_key(self, request: SearchRequest) -> str:
        """生成缓存键"""
        key_parts = [
            request.trigger_type.value,
            request.search_scope.value,
            '_'.join(sorted(request.query_terms)),
            str(hash(frozenset(request.constraints.items())))
        ]
        return '_'.join(key_parts)
    
    async def _process_search_requests(self):
        """处理搜索请求队列"""
        while self.is_running:
            try:
                # 从队列获取请求（带超时）
                request = await asyncio.wait_for(self.request_queue.get(), timeout=1.0)
                
                # 处理请求
                result = await self.search(request)
                
                # 标记任务完成
                self.request_queue.task_done()
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Error processing search request: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.lock:
            stats = self.stats.copy()
            stats.update({
                'registered_strategies': len(self.strategies),
                'cache_size': len(self.result_cache),
                'queue_size': self.request_queue.qsize() if hasattr(self.request_queue, 'qsize') else 0,
                'is_running': self.is_running
            })
            return stats
    
    def clear_cache(self):
        """清空缓存"""
        self.result_cache.clear()
        self.logger.info("Search result cache cleared")


# 全局图式搜索集成器实例
schema_search_integrator = SchemaSearchIntegrator()


class SearchDrivenSchemaExecutor:
    """搜索驱动的图式执行器"""

    def __init__(self, search_integrator: SchemaSearchIntegrator):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.search_integrator = search_integrator
        self.execution_history = []
        self.adaptive_weights = {
            'search_confidence': 0.4,
            'execution_success': 0.3,
            'context_relevance': 0.2,
            'performance_metrics': 0.1
        }

    async def execute_with_search_guidance(self, schema: TreeChart,
                                         context: ExecutionContext,
                                         search_guidance: bool = True) -> ExecutionResult:
        """使用搜索引导执行图式"""
        execution_id = str(uuid.uuid4())
        start_time = time.time()

        try:
            if search_guidance:
                # 在执行前进行搜索以获取上下文信息
                search_request = self._create_pre_execution_search_request(schema, context)
                search_result = await self.search_integrator.search(search_request)

                # 将搜索结果集成到执行上下文中
                self._integrate_search_results_to_context(search_result, context)

            # 执行图式
            execution_result = await schema_executor.execute(schema, context)

            # 记录执行历史
            self._record_execution_history(execution_id, schema, context, execution_result, time.time() - start_time)

            if not execution_result.success and search_guidance:
                # 执行失败时进行错误恢复搜索
                recovery_result = await self._perform_error_recovery_search(schema, context, execution_result)
                if recovery_result:
                    # 使用恢复信息重新执行
                    execution_result = await schema_executor.execute(schema, context)

            return execution_result

        except Exception as e:
            self.logger.error(f"Search-driven execution failed: {e}")
            return ExecutionResult(
                success=False,
                result=None,
                error=str(e),
                execution_time=time.time() - start_time,
                metadata={'execution_id': execution_id}
            )

    def _create_pre_execution_search_request(self, schema: TreeChart,
                                           context: ExecutionContext) -> SearchRequest:
        """创建执行前搜索请求"""
        # 从图式中提取搜索词
        query_terms = self._extract_query_terms_from_schema(schema)

        # 从上下文中提取相关信息
        context_data = {
            'schema_type': schema.schema_type.value if hasattr(schema, 'schema_type') else 'unknown',
            'execution_mode': schema.execution_mode.value if hasattr(schema, 'execution_mode') else 'unknown',
            'context_variables': list(context.variables.keys()) if hasattr(context, 'variables') else []
        }

        return SearchRequest(
            request_id=str(uuid.uuid4()),
            trigger_type=SearchTriggerType.SCHEMA_EXECUTION,
            search_scope=SearchScope.CONTEXTUAL,
            query_terms=query_terms,
            context_data=context_data,
            priority=0.7,
            timeout=3.0,
            max_results=20
        )

    def _extract_query_terms_from_schema(self, schema: TreeChart) -> List[str]:
        """从图式中提取查询词"""
        query_terms = []

        try:
            # 从图式ID中提取
            if hasattr(schema, 'chart_id') and schema.chart_id:
                query_terms.append(schema.chart_id)

            # 从动作参数中提取
            if hasattr(schema, 'action_params') and schema.action_params:
                for key, value in schema.action_params.items():
                    if isinstance(value, str) and len(value) > 2:
                        query_terms.append(value)

            # 从子图式中提取
            if hasattr(schema, 'sub_charts') and schema.sub_charts:
                for sub_chart in schema.sub_charts:
                    sub_terms = self._extract_query_terms_from_schema(sub_chart)
                    query_terms.extend(sub_terms)

            # 去重并限制数量
            unique_terms = list(set(query_terms))[:10]
            return unique_terms

        except Exception as e:
            self.logger.warning(f"Failed to extract query terms from schema: {e}")
            return ['default_search']

    def _integrate_search_results_to_context(self, search_result: SearchResult,
                                           context: ExecutionContext):
        """将搜索结果集成到执行上下文中"""
        try:
            # 设置搜索结果变量
            context.set_variable('search_results', search_result.results)
            context.set_variable('search_confidence', search_result.confidence)

            # 提取有用的上下文信息
            for i, result in enumerate(search_result.results[:5]):  # 只取前5个结果
                if isinstance(result, dict):
                    # 设置节点信息
                    if 'node' in result:
                        context.set_variable(f'search_node_{i}', result['node'])

                    # 设置激活信息
                    if 'activation' in result:
                        context.set_variable(f'search_activation_{i}', result['activation'])

                    # 设置语义权重
                    if 'semantic_weight' in result:
                        context.set_variable(f'search_semantic_{i}', result['semantic_weight'])

            self.logger.debug(f"Integrated {len(search_result.results)} search results into context")

        except Exception as e:
            self.logger.warning(f"Failed to integrate search results: {e}")

    async def _perform_error_recovery_search(self, schema: TreeChart,
                                           context: ExecutionContext,
                                           failed_result: ExecutionResult) -> Optional[SearchResult]:
        """执行错误恢复搜索"""
        try:
            # 创建错误恢复搜索请求
            error_terms = self._extract_error_terms(failed_result)

            recovery_request = SearchRequest(
                request_id=str(uuid.uuid4()),
                trigger_type=SearchTriggerType.ERROR_RECOVERY,
                search_scope=SearchScope.GLOBAL,
                query_terms=error_terms,
                context_data={
                    'error_message': str(failed_result.error),
                    'schema_id': getattr(schema, 'chart_id', 'unknown'),
                    'execution_context': context.context_id
                },
                priority=0.9,
                timeout=5.0,
                max_results=10
            )

            recovery_result = await self.search_integrator.search(recovery_request)

            if recovery_result.confidence > 0.3:
                # 将恢复信息应用到上下文
                self._apply_recovery_information(recovery_result, context)
                return recovery_result

        except Exception as e:
            self.logger.error(f"Error recovery search failed: {e}")

        return None

    def _extract_error_terms(self, failed_result: ExecutionResult) -> List[str]:
        """从失败结果中提取错误相关的搜索词"""
        error_terms = []

        if failed_result.error:
            error_message = str(failed_result.error).lower()

            # 提取关键错误词
            error_keywords = ['error', 'exception', 'failed', 'missing', 'invalid', 'timeout']
            for keyword in error_keywords:
                if keyword in error_message:
                    error_terms.append(keyword)

            # 提取具体的错误信息
            words = error_message.split()
            for word in words:
                if len(word) > 3 and word.isalpha():
                    error_terms.append(word)

        return error_terms[:5]  # 限制搜索词数量

    def _apply_recovery_information(self, recovery_result: SearchResult, context: ExecutionContext):
        """应用恢复信息到上下文"""
        try:
            # 设置恢复标志
            context.set_variable('recovery_attempted', True)
            context.set_variable('recovery_confidence', recovery_result.confidence)

            # 应用恢复建议
            for i, result in enumerate(recovery_result.results[:3]):
                if isinstance(result, dict):
                    context.set_variable(f'recovery_suggestion_{i}', result)

            self.logger.info(f"Applied recovery information with confidence {recovery_result.confidence}")

        except Exception as e:
            self.logger.warning(f"Failed to apply recovery information: {e}")

    def _record_execution_history(self, execution_id: str, schema: TreeChart,
                                context: ExecutionContext, result: ExecutionResult,
                                execution_time: float):
        """记录执行历史"""
        history_entry = {
            'execution_id': execution_id,
            'schema_id': getattr(schema, 'chart_id', 'unknown'),
            'schema_type': getattr(schema, 'schema_type', 'unknown'),
            'context_id': context.context_id,
            'success': result.success,
            'execution_time': execution_time,
            'timestamp': time.time(),
            'error': str(result.error) if result.error else None
        }

        self.execution_history.append(history_entry)

        # 限制历史记录数量
        if len(self.execution_history) > 1000:
            self.execution_history = self.execution_history[-500:]

    def get_execution_statistics(self) -> Dict[str, Any]:
        """获取执行统计信息"""
        if not self.execution_history:
            return {'total_executions': 0}

        total = len(self.execution_history)
        successful = sum(1 for entry in self.execution_history if entry['success'])
        failed = total - successful

        avg_time = sum(entry['execution_time'] for entry in self.execution_history) / total

        return {
            'total_executions': total,
            'successful_executions': successful,
            'failed_executions': failed,
            'success_rate': successful / total,
            'average_execution_time': avg_time,
            'recent_executions': self.execution_history[-10:]
        }


# 全局搜索驱动执行器实例
search_driven_executor = SearchDrivenSchemaExecutor(schema_search_integrator)
